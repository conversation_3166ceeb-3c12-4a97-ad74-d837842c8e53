import request from '@/utils/axios'

const url = '/'

function getFilterList(data) {
  return request({
    url: url + 'prompt/filterList',
    method: 'POST',
    data
  })
}

function getUserList(params) {
  return request({
    url: url + 'user/itcode/search',
    method: 'get',
    params
  })
}

function getPromptList(data) {
  return request({
    url: url + 'prompt/list',
    method: 'post',
    data
  })
}

function addPrompt(data) {
  return request({
    url: url + 'prompt/add',
    method: 'post',
    data
  })
}

function updatePrompt(data) {
  return request({
    url: url + 'prompt/update',
    method: 'post',
    data
  })
}

function deletePrompt(data) {
  return request({
    url: url + 'prompt/delete',
    method: 'post',
    data
  })
}

export {
  getFilterList,
  getUserList,
  getPromptList,
  addPrompt,
  updatePrompt,
  deletePrompt
}
