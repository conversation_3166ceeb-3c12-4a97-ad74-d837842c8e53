<script setup>
import { ref } from 'vue'
import { ElLoading } from 'element-plus'
import * as api from './api'

import { useI18n } from 'vue-i18n'
const { t } = useI18n()
// 声明props
const props = defineProps({
  knowledgeId: Number
})

// 是否显示对话框
const dialogVisible = ref(false)

// 数据源表单数据
const argumentObj = {
  embedding: {
    topk: '',
    recallScore: '',
    recallType: '',
    model: '',
    chunkSize: '',
    chunkOverlap: ''
  },
  prompt: {
    subChatScene: '',
    content: '',
    maxToken: ''
  },
  summary: {
    maxIteration: '',
    concurrencyLimit: ''
  }
}
const argumentForm = ref(JSON.parse(JSON.stringify(argumentObj)))
const embeddingRules = {
  topk: [{ required: true, message: () => `${t('COMMON.PLS_INPUT')} topk`, trigger: 'blur' }],
  recallScore: [{ required: true, message: () => `${t('COMMON.PLS_INPUT')} recall score`, trigger: 'blur' }],
  recallType: [{ required: true, message: () => `${t('COMMON.PLS_INPUT')} recall type`, trigger: 'blur' }],
  model: [{ required: true, message: () => `${t('COMMON.PLS_INPUT')} model`, trigger: 'blur' }],
  chunkSize: [{ required: true, message: () => `${t('COMMON.PLS_INPUT')} chunk size`, trigger: 'blur' }],
  chunkOverlap: [{ required: true, message: () => `${t('COMMON.PLS_INPUT')} chunk overlap`, trigger: 'blur' }]
}
const summaryRules = {
  maxIteration: [{ required: true, message: () => `${t('COMMON.PLS_INPUT')} max iteration`, trigger: 'blur' }],
  concurrencyLimit: [{ required: true, message: () => `${t('COMMON.PLS_INPUT')} concurrency limit`, trigger: 'blur' }]
}
// 获取知识库参数
const getArguments = async () => {
  const loading = ElLoading.service({ target: '.el-dialog' })
  try {
    const res = await api.getArguments(props.knowledgeId, {})
    loading.close()
    if (res) {
      res.summary = res.summary || {
        maxIteration: '',
        concurrencyLimit: ''
      }
      argumentForm.value = res
    }
  } catch(e) {
    loading.close()
  }
}
// 保存知识库参数
const saveArguments = async () => {
  const loading = ElLoading.service({ target: '.el-dialog' })
  try {
    await api.saveArguments(props.knowledgeId, argumentForm.value)
    loading.close()
    dialogVisible.value = false
  } catch (e) {
    loading.close()
  }
}
// 嵌入表单实例
const embeddingFormRef = ref()
// 总结表单实例
const summaryFormRef = ref()
// 提交表单
const submitForm = () => {
  embeddingFormRef.value.validate((valid1) => {
    summaryFormRef.value.validate((valid2) => {
      if (valid1 && valid2) {
        saveArguments()
      }
    })
  })
}

// 关闭对话框时清除所有数据
const resetData = () => {
  argumentForm.value = JSON.parse(JSON.stringify(argumentObj))
}

// 暴露属性和方法
defineExpose({ dialogVisible })
</script>

<template>
  <div class="argument-dialog">
    <el-dialog
      class="setting-page-dialog-wrap"
      v-model="dialogVisible"
      :title="$t('KNOWLEDGE.PARAMS')"
      width="800px"
      :close-on-click-modal="false"
      destroy-on-close
      @open="getArguments()"
      @closed="resetData()"
    >
      <el-tabs>
        <el-tab-pane>
          <template #label>
            <DocumentAdd class="tab-icon" />
            <span>{{ $t('KNOWLEDGE.EMBED') }}</span>
          </template>
          <el-form
            ref="embeddingFormRef"
            :model="argumentForm.embedding"
            :rules="embeddingRules"
            label-width="138px"
            size="large"
          >
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item prop="topk">
                  <template #label>
                    <span>topk</span>
                    <el-tooltip :content="$t('KNOWLEDGE.TOPK')" placement="top">
                      <QuestionFilled class="question-icon" />
                    </el-tooltip>
                  </template>
                  <el-input v-model="argumentForm.embedding.topk" :placeholder="`${$t('COMMON.PLS_INPUT')} topk`" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="recallScore">
                  <template #label>
                    <span>recallScore</span>
                    <el-tooltip :content="$t('KNOWLEDGE.RECALL_SCORE')" placement="top">
                      <QuestionFilled class="question-icon" />
                    </el-tooltip>
                  </template>
                  <el-input v-model="argumentForm.embedding.recallScore" :placeholder="`${$t('COMMON.PLS_INPUT')} recall score`" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item prop="recallType">
                  <template #label>
                    <span>recallType</span>
                    <el-tooltip :content="$t('KNOWLEDGE.RECALL_TYPE')" placement="top">
                      <QuestionFilled class="question-icon" />
                    </el-tooltip>
                  </template>
                  <el-input v-model="argumentForm.embedding.recallType" :placeholder="`${$t('COMMON.PLS_INPUT')} recall type`" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="model">
                  <template #label>
                    <span>model</span>
                    <el-tooltip :content="$t('KNOWLEDGE.MODEL')" placement="top">
                      <QuestionFilled class="question-icon" />
                    </el-tooltip>
                  </template>
                  <el-input v-model="argumentForm.embedding.model" :placeholder="`${$t('COMMON.PLS_INPUT')} model`" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item prop="chunkSize">
                  <template #label>
                    <span>chunkSize</span>
                    <el-tooltip :content="$t('KNOWLEDGE.CHUNK_SIZE')" placement="top">
                      <QuestionFilled class="question-icon" />
                    </el-tooltip>
                  </template>
                  <el-input v-model="argumentForm.embedding.chunkSize" :placeholder="`${$t('COMMON.PLS_INPUT')} chunk size`" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="chunkOverlap">
                  <template #label>
                    <span>chunkOverlap</span>
                    <el-tooltip :content="$t('KNOWLEDGE.CHUNK_OVERLAP')" placement="top">
                      <QuestionFilled class="question-icon" />
                    </el-tooltip>
                  </template>
                  <el-input v-model="argumentForm.embedding.chunkOverlap" :placeholder="`${$t('COMMON.PLS_INPUT')} chunk overlap`" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        <el-tab-pane>
          <template #label>
            <Opportunity class="tab-icon" />
            <span>{{ $t('KNOWLEDGE.PROMPT') }}</span>
          </template>
          <el-form
            :model="argumentForm.prompt"
            label-width="110px"
            size="large"
          >
            <el-form-item prop="subChatScene">
              <template #label>
                <span>scene</span>
                <el-tooltip :content="$t('KNOWLEDGE.SCENE')" placement="top">
                  <QuestionFilled class="question-icon" />
                </el-tooltip>
              </template>
              <el-input
                v-model="argumentForm.prompt.subChatScene"
                type="textarea"
                :rows="4"
                resize="none"
                :placeholder="`${$t('COMMON.PLS_INPUT')} scene`"
              />
            </el-form-item>
            <el-form-item prop="content">
              <template #label>
                <span>template</span>
                <el-tooltip :content="$t('KNOWLEDGE.TEMPLATE')" placement="top">
                  <QuestionFilled class="question-icon" />
                </el-tooltip>
              </template>
              <el-input
                v-model="argumentForm.prompt.content"
                type="textarea"
                :rows="7"
                resize="none"
                :placeholder="`${$t('COMMON.PLS_INPUT')} template`"
              />
            </el-form-item>
            <el-form-item prop="maxToken">
              <template #label>
                <span>max_token</span>
                <el-tooltip :content="$t('KNOWLEDGE.MAX_TOKEN')" placement="top">
                  <QuestionFilled class="question-icon" />
                </el-tooltip>
              </template>
              <el-input
                v-model="argumentForm.prompt.maxToken"
                :placeholder="`${$t('COMMON.PLS_INPUT')} max_token`"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane>
          <template #label>
            <Collection class="tab-icon" />
            <span>{{ $t('KNOWLEDGE.SUMMARY') }}</span>
          </template>
          <el-form
            ref="summaryFormRef"
            :model="argumentForm.summary"
            :rules="summaryRules"
            label-width="145px"
            size="large"
          >
            <el-form-item prop="maxIteration">
              <template #label>
                <span>max iteration</span>
              </template>
              <el-input
                v-model="argumentForm.summary.maxIteration"
                :placeholder="`${$t('COMMON.PLS_INPUT')} max iteration`"
              />
            </el-form-item>
            <el-form-item prop="concurrencyLimit">
              <template #label>
                <span>concurrency limit</span>
              </template>
              <el-input
                v-model="argumentForm.summary.concurrencyLimit"
                :placeholder="`${$t('COMMON.PLS_INPUT')} concurrency limit`"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <el-button @click="dialogVisible = false">{{ $t('COMMON.CANCEL') }}</el-button>
        <el-button type="primary" @click="submitForm()">{{ $t('COMMON.SUBMIT') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.argument-dialog {
  :deep(.el-dialog__body) {
    padding: 0 20px;
    .el-tabs__item {
      font-size: 16px;
      .tab-icon {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }
    }
    .question-icon {
      width: 14px;
      height: 14px;
      margin: 13px 0 0 4px;
    }
  }
}
</style>
