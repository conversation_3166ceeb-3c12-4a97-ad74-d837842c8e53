<script setup>
import { ref } from 'vue'
import tabContent from './tabContent.vue'
import {getUsername} from "@/utils/auth"
import {useI18n} from 'vue-i18n'
const { locale }  = useI18n()
const userName = getUsername()
const activeName = ref('all')
const permissionDialog = ref(false)
</script>

<template>
  <div class="agent-chat-page">
    <div class="top-banner">
      <p class="hi-txt">{{locale === 'zh_CN' ? '你好' : 'Hi'}}, {{ userName }}</p>
      <p class="tip-txt">{{ $t('AGENT.WELCOME') }}</p>
    </div>
    <div class="main-content">
      <el-tabs v-model="activeName">
        <el-tab-pane :label="$t('COMMON.ALL')" name="all" class="tab-name">
        </el-tab-pane>
        <el-tab-pane :label="$t('COMMON.COLLECT')" name="collection" class="tab-name">
        </el-tab-pane>
      </el-tabs>
      <tab-content :currentTab="activeName" />
    </div>
  </div>

  <el-dialog v-model="permissionDialog" class="permission-dialog-wrap">
    <div class="permission-content">
      <el-icon class="permission-warn-icon"><WarningFilled /></el-icon>
      <p class="permission-tiptxt">您当前未获得该应用所依赖的数据源权限，所需数据来自Databricks中magellanedw.dwc_fi.ciop_kpi_all。</p>
      <p class="permission-link">
        请前往<a href="#">Horizon</a>获取相关数据权限
      </p>
    </div>
  </el-dialog>
</template>

<style lang="less" scoped>
.agent-chat-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-right: 20px;
}

.top-banner {
  height: 120px;
  padding: 28px 0 0 30px;
  border-radius: 16px;
  background: url("./imgs/bannerx2.png") center center no-repeat;
  background-size: cover;

  .hi-txt {
    height: 32px;
    font-weight: bold;
    font-size: 24px;
    line-height: 28px;
    letter-spacing: 1px;
    background-image: linear-gradient(0deg, #46B5F6 0%, #568DF4 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .tip-txt {
    height: 18px;
    margin-top: 14px;
    font-size: 14px;
    color: #455D83;
    line-height: 16px;
    letter-spacing: 1px;
  }
}

.main-content {
  flex: 1;
  height: 0;
  margin: 16px 0 20px 0;
  padding: 0 24px;
  background-color: #F8FAFF;
  border-radius: 12px;
  position: relative;

  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
  }
  :deep(.el-tabs__header.is-top) {
    margin-bottom: 24px;
  }
  :deep(.el-tabs__item) {
    width: 58px;
    bottom: -6px;
  }

  .tab-name {
    font-size: 14px;
  }
  .el-tabs {
    --el-tabs-header-height: 50px;
  }
}

.main-content {
  :deep(.tab-content) {
    height: calc(100% - 73px);
  }
}

.permission-dialog-wrap {
  width: 540px;
  height: 350px;
}
.permission-warn-icon {
  display: flex;
  font-size: 60px;
  color: #F18549;
  margin: 0 auto;
}
.permission-tiptxt,
.permission-link {
  font-size: 14px;
  color: #4B556A;
  text-align: center;
  margin: 20px 0;
}
</style>