<script setup>
import { ref, onMounted } from 'vue'
import * as pbi from 'powerbi-client'
import 'powerbi-report-authoring'
import markdownRender from '@/components/MarkdownRender/markdownRender.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

let props = defineProps({
  chartData: Object
})

onMounted(() => {
  initConfig()
  createPbiVisual()
})
let showErrorTips = ref(false)
let pbiVisualData = ref(null)
let changeVisualType = ref('tableEx')
let pbiDomId = ref('')

let showThinkContent = ref(false)

let reportData = ref({
  instance: null,
  report: null,
  id: props.chartData.pbiVisualParams.reportId,
  token: props.chartData.pbiVisualParams.accessToken
})
let embedConfig = ref({
  id: '',
  accessToken: '',
  type: 'report',
  embedUrl: 'https://app.powerbi.cn/reportEmbed',
  tokenType: pbi.models.TokenType.Aad,
  permissions: pbi.models.Permissions.All,
  pageName: 'pbi-chat',
  settings: {
    navContentPaneEnabled: false, // 底部页面导航窗格
    filterPaneEnabled: false // 筛选器窗格
  }
})
const initConfig = () => {
  pbiDomId.value = props.chartData.requestId
  pbiVisualData.value = props.chartData.pbiVisualParams
  changeVisualType.value = props.chartData.changeVisualType
  embedConfig.value.id = pbiVisualData.value.reportId
  // embedConfig.value.id = '4b3b113e-ff99-4a4c-942c-38ed044dc3e4'
  embedConfig.value.accessToken = pbiVisualData.value.accessToken
}
const createPbiVisual = async () => {
  reportData.value.instance = new pbi.service.Service(pbi.factories.hpmFactory, pbi.factories.wpmpFactory, pbi.factories.routerFactory)
  // let pbiDom = document.getElementById('pbiDom')
  let pbiDom = document.getElementById(props.chartData.requestId)
  // console.log(embedConfig, '配置项')
  reportData.value.report = reportData.value.instance.embed(pbiDom, embedConfig.value)
  reportData.value.report.on('loaded', async function() {
    showThinkContent.value = true
    // console.log('报表加载完成')
    // let visualType = pbiVisualData.value.createParams.visualType
    let visualField = pbiVisualData.value.createParams.visualValue
    let visualFilter = pbiVisualData.value.filterParams
    let pageName = 'chatPbi'
    let authoringPage = await reportData.value.report.addPage(pageName)
    await authoringPage.setActive()
    let visualLayout = {
      x: 0,
      y: 0,
      z: 0,
      width: 1000,
      height: 300,
      displayState: {
        mode: 0
      }
    }
    // let res = await authoringPage.createVisual(visualType, visualLayout)
    let res = await authoringPage.createVisual('tableEx', visualLayout)
    let visual = res.visual
    // console.log(visual, '创建的视觉对象')
    // await Promise.all(visualField.forEach(item =>
    //   visual.addDataField(item.dataRoles, item.dataField)
    // ));
    visualField.forEach(item => {
      visual.addDataField(JSON.parse(JSON.stringify(item.dataRoles)), JSON.parse(JSON.stringify(item.dataField)))
    })
    if (visualFilter.length > 0) {
      let filters = []
      visualFilter.forEach(item => {
        item.filterValue.filterType = transformModelType(item.filterValue.filterType)
        // console.log(JSON.parse(JSON.stringify(item.filterValue.values)), '测')
        // let filter = {
        //   $schema: item.filterValue.schema,
        //   target: {
        //     table: item.filterValue.target.table,
        //     column: item.filterValue.target.column
        //   },
        //   filterType: transformModelType(item.filterValue.filterType),
        //   // filterType: pbi.models.FilterType.Basic
        //   operator: item.filterValue.operator,
        //   values: item.filterValue.values
        // }
        filters.push(JSON.parse(JSON.stringify(item.filterValue)))
      })
      await visual.updateFilters(pbi.models.FiltersOperations.Add, filters)
    }

    // let filter = {
    //   $schema: 'http://powerbi.com/product/schema#basic',
    //   target: {
    //     table: 'leads_final',
    //     column: '客户分级final'i
    //   },
    //   filterType: pbi.models.FilterType.BasicFilter,
    //   operator: 'In',
    //   values: ['B2', 'B3']
    // }
    // await visual.updateFilters(pbi.models.FiltersOperations.Add, [filter])
    await visual.changeType(changeVisualType.value)
    let settings = {
      layoutType: pbi.models.LayoutType.Custom,
      customLayout: {
        pageSize: {
          type: pbi.models.PageSizeType.Custom,
          width: 1000,
          height: 300
        },
        displayOption: pbi.models.DisplayOption.FitToWidth
      }
    }
    reportData.value.report.updateSettings(settings)
    // Defining data fields
    // const regionColumn = { measure: 'ASTI.CA', table: '度量值表'};
    // const totalUnitsMeasure = { column: 'Bu_Level2', table: 'Dim_BU'};
    // let roles = await visual.getCapabilities()
    // console.log(roles, 'roles角色')
    // Adding visual data fields
    // await visual.addDataField('Category', totalUnitsMeasure);
    // await visual.addDataField('Y', regionColumn);
    // const dataField1 = await visual.getDataFields('Y');
    // const dataField2 = await visual.getDataFields('Category');
    // console.log("Visual 'Y' fields:\n", dataField1);
    // console.log("Visual 'Category' fields:\n", dataField2);
  })
  reportData.value.report.on("error", function (event) {
    // console.log(event.detail);
    if(Number(event.detail.errorCode) === 403) {
      showErrorTips.value = true
    }
  });

}
const transformModelType = (typeStr) => {
  switch (typeStr) {
    case 'BasicFilter':
      return pbi.models.FilterType.Basic // 文档是BasicFilter，文档是错误的。不能用BasicFilter
    case 'AdvancedFilter':
      return pbi.models.FilterType.Advanced
    case 'TopN':
      return pbi.models.FilterType.TopN
    case 'RelativeDate':
      return pbi.models.FilterType.RelativeDate
    case 'RelativeTime ':
      return pbi.models.FilterType.RelativeTime
    default:
      return pbi.models.FilterType.Basic
  }
}

</script>

<template>
<div class="pbi-render">
  <div v-if="showErrorTips" class="error-text">会话Token已过期，请重新询问。</div>
  <template v-else>
    <div class="thinking-content" v-if="props.chartData.aitext && showThinkContent">
      <p class="think-title">
        <i class="iconfont icon-shendusikao"></i>
        <span class="text">{{ $t('COMMON.THINK_PROCESS') }}：</span>
      </p>
      <markdown-render :context="props.chartData.aitext" />
    </div>
    <div class="pbi" :id="props.chartData.requestId"></div>
  </template>
</div>
</template>

<style scoped lang="less">
.thinking-content {
  padding: 10px;
  color: #9195a3;
  border-radius: 6px;
  background: linear-gradient(180deg, rgb(127 161 255 / 6%), rgb(203 228 255 / 5%));
  margin-bottom: 5px;
  border-radius: 6px;
  .think-title {
    display: flex;
    align-items: center;
    color: #0A121F;
    // color: #568cf4;
    font-size: 16px;
    .text {
      padding-left: 3px;
    }
  }
  .markdown-render {
    font-size: 14px;
  }
}
.pbi-render {
  width: 100%;
  height: 100%;
  .error-text {
    height: 40px;
    line-height: 40px;
    font-size: 12px;
    color: #0A121F;
  }
  .pbi {
    width: 100%;
    height: 450px; // Match or exceed the height you set in the visual layout
  }
}
</style>
