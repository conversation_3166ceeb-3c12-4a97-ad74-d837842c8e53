<script setup>
import { CirclePlus, Search } from '@element-plus/icons-vue'
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as api from './api'
import { camelToUnderline } from '@/utils/util'
import {useI18n} from "vue-i18n";
const { t } = useI18n()

const nameInputValue = ref('')
const pageNum = ref(1)
const pageSize = ref(10)
let orderBy = 'update_time desc'
const startRow = ref(0)
const endRow = ref(0)
const pageTotal = ref(0)
const tableData = ref([])
const getTableList = async () => {
  const params = {
    roleNames: [...new Set(nameInputValue.value ? nameInputValue.value.split('/') : [])], // 角色名称
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    orderBy // 排序字段
  }
  try {
    const res = await api.getTableList(params)
    startRow.value = res.startRow
    endRow.value = res.endRow
    pageTotal.value = res.total
    tableData.value = res.list || []
  } catch (e) {
    console.log(e)
  }
}
const handleClickSearch = () => {
  pageNum.value = 1
  getTableList()
}
const indexMethod = (index) => {
  return (pageNum.value - 1) * 10 + index + 1
}
const sortChange = (sortFlag) => {
  const { prop, order } = sortFlag
  if (sortFlag.order === null) {
    orderBy = 'update_time desc'
  } else {
    orderBy = `${camelToUnderline(prop)} ${order === 'ascending' ? 'asc' : 'desc'}`
  }
  getTableList()
}
const handleCurrentChange = (val) => {
  pageNum.value = val
  getTableList()
}

const createRoleObj = ref({
  dialogVisible: false,
  roleName: '',
  roleExplain: '',
  markerIsShow: false
})
const createRoleTitle = ref('')
const markerValue = ref('')
let selectedRowId = ''
const handleClickCreate = () => {
  createRoleTitle.value = t('ROLE.ADD_ROLE')
  createRoleObj.value.dialogVisible = true
}
const handleClickEdit = (index, row) => {
  // createRoleTitle.value = '编辑角色'
  createRoleTitle.value = t('ROLE.EDIT_ROLE')
  selectedRowId = row['id']
  const { roleName, description } = row
  createRoleObj.value.dialogVisible = true
  createRoleObj.value.roleName = roleName
  createRoleObj.value.roleExplain = description
}
const handleClickCopy = (index, row) => {
  createRoleTitle.value = t('ROLE.COPY_ROLE')
  const { roleName, description } = row
  selectedRowId = row['id']
  createRoleObj.value.dialogVisible = true
  createRoleObj.value.roleName = roleName
  createRoleObj.value.roleExplain = description
}
const deleteRole = async (roleIds) => {
  try {
    const haveRes = await api.haveUser({ roleIds })
    if (haveRes) {
      ElMessage.warning('有用户正在使用该角色，请先在用户管理中删除该角色再进行此操作。')
    } else {
      await api.deleteRole({ roleIds })
      await getTableList()
    }
  } catch (e) {
    console.log(e)
  }
}
const handleClickDelete = (index, row) => {
  const { id } = row
  ElMessageBox.confirm(
    t('ROLE.DELETE_TIPS'),
    '',
    { type: 'warning', closeOnClickModal: false }
  ).then(() => {
    deleteRole([id])
  }).catch(() => {
    console.log('取消删除')
  })
}
const createNewRole = async () => {
  const params = {
    roleName: createRoleObj.value.roleName.trim(), // 角色名称
    description: createRoleObj.value.roleExplain.trim() // 角色说明
  }
  try {
    const res = await api.createNewRole(params)
    const resData = res.data
    if (resData.success) {
      createRoleObj.value.dialogVisible = false
      permissionDialog.value.dialogVisible = true
      selectedRowId = resData.data.id
      await getTableList()
    } else {
      markerValue.value = resData.msg
      createRoleObj.value.markerIsShow = true
    }
  } catch (e) {
    console.log(e)
  }
}
const copyRole = async () => {
  const params = {
    id: selectedRowId, // 角色id
    roleName: createRoleObj.value.roleName.trim(), // 角色名称
    description: createRoleObj.value.roleExplain.trim() // 角色说明
  }
  try {
    const res = await api.copyRole(params)
    const resData = res.data
    if (resData.success) {
      createRoleObj.value.dialogVisible = false
      await getTableList()
    } else {
      markerValue.value = resData.msg
      createRoleObj.value.markerIsShow = true
    }
  } catch (e) {
    console.log(e)
  }
}
const updateRole = async () => {
  const params = {
    id: selectedRowId, // 角色id
    roleName: createRoleObj.value.roleName.trim(), // 角色名称
    description: createRoleObj.value.roleExplain.trim() // 角色说明
  }
  try {
    const res = await api.updateRole(params)
    const resData = res.data
    if (resData.success) {
      createRoleObj.value.dialogVisible = false
      await getTableList()
    } else {
      markerValue.value = resData.msg
      createRoleObj.value.markerIsShow = true
    }
  } catch (e) {
    console.log(e)
  }
}
const handleClickCreateSubmit = () => {
  createRoleObj.value.markerIsShow = false
  if (createRoleObj.value.roleName.trim() === '') {
    markerValue.value = '请输入角色名称'
    createRoleObj.value.markerIsShow = true
  } else if (createRoleObj.value.roleName.indexOf('/') !== -1) {
    markerValue.value = '角色名称不能包含/'
    createRoleObj.value.markerIsShow = true
  } else {
    if (createRoleTitle.value === t('ROLE.ADD_ROLE')) {
      createNewRole()
    } else if (createRoleTitle.value === t('ROLE.COPY_ROLE')) {
      copyRole()
    } else if (createRoleTitle.value === t('ROLE.EDIT_ROLE')) {
      updateRole()
    }
  }
}
const handleClickCreateCancel = () => {
  createRoleObj.value.dialogVisible = false
}
const handleDialogClosed = () => {
  createRoleObj.value.roleName = ''
  createRoleObj.value.roleExplain = ''
  createRoleObj.value.markerIsShow = false
  markerValue.value = ''
  if (createRoleTitle.value !== t('ROLE.ADD_ROLE')) {
    selectedRowId = ''
  }
}

const permissionDialog = ref({ dialogVisible: false })
const currentTab = ref(1)
const menuTreeData = ref([])
const defaultMenuChecked = ref([])
const defaultMenuProps = {
  children: 'children',
  label: 'menuName'
}
const dataTreeData = ref([])
const defaultDataChecked = ref([])
const defaultDataProps = {
  children: 'detailList',
  label: 'name'
}
const handleClickTab = (val) => {
  currentTab.value = val
}
const getMenuPermissionData = async () => {
  try {
    menuTreeData.value = await api.getMenuPermission()
  } catch (e) {
    console.log(e)
  }
}
const getDataPermissionData = async () => {
  try {
    dataTreeData.value = await api.getDataPermission()
  } catch (e) {
    console.log(e)
  }
}
const getTreeIds = (array) => {
  // 获取叶子节点
  let nodeIds = []
  const parseTreeJson = (array) => {
    for (let index = 0; index < array.length; index++) {
      const element = array[index]
      // 1.判断element.children是对象
      if (element.children && typeof (element.children) === 'object') {
        parseTreeJson(element.children)
      } else {
        // 判断是否为子节点
        if (!element.children || element.children.length === 0) {
          // 获得符合的 nodeId
          nodeIds.push(element.id)
        }
      }
    }
  }
  parseTreeJson(array)
  return nodeIds
}
const getAllPermissionData = async (id) => {
  try {
    const res = await api.getAllPermission(id)
    defaultMenuChecked.value = res['menuList'] ? getTreeIds(res['menuList']) : []
    defaultDataChecked.value = res['dataIds'] || []
  } catch (e) {
    console.log(e)
  }
}
const handleClickPermission = (index, row) => {
  createRoleTitle.value = t('ROLE.EDIT_PERMISION')
  const { id } = row
  selectedRowId = row['id']
  getAllPermissionData(id)
  permissionDialog.value.dialogVisible = true
}
const savePermission = async (menuList, dataDetailList) => {
  const params = {
    roleId: selectedRowId,
    menuList,
    dataDetailList
  }
  try {
    await api.savePermission(params)
    await getTableList()
    permissionDialog.value.dialogVisible = false
  } catch (e) {
    console.log(e)
  }
}
const menuTree = ref()
const dataTree = ref()
const handleClickPermissionSubmit = () => {
  const menuList = menuTree.value.getCheckedNodes(false, true).map(item => item.id)
  const dataDetailList = dataTree.value.getCheckedKeys(true)
  savePermission(menuList, dataDetailList)
}
const handleClickPermissionCancel = () => {
  permissionDialog.value.dialogVisible = false
}
const handlePermissionDialogClosed = () => {
  currentTab.value = 1
  selectedRowId = ''
  defaultMenuChecked.value = []
  defaultDataChecked.value = []
}

let multipleSelection = []
const handleSelectionChange = (val) => {
  multipleSelection = val
}
const handleBatchDelete = () => {
  if (multipleSelection.length === 0) {
    ElMessage.warning(t('ROLE.SELECT_ONE'))
    return
  }
  const ids = multipleSelection.map(item => item.id)
  ElMessageBox.confirm(
    t('ROLE.DELETE_TIPS'),
    '',
    { type: 'warning', closeOnClickModal: false }
  ).then(() => {
    deleteRole(ids)
  }).catch(() => {
    console.log('取消删除')
  })
}
const exportSelectOrAll = async (val) => {
  let roleList = []
  if (val === 'all') {
    roleList = [...new Set(nameInputValue.value ? nameInputValue.value.split('/') : [])]
  } else {
    if (multipleSelection.length === 0) {
      ElMessage.warning('请至少选择一个角色')
      return
    }
    roleList = multipleSelection.map(item => item.roleName)
  }
  const params = {
    roleNames: roleList, // 角色名称
    pageNum: 1,
    pageSize: 9999,
    orderBy, // 排序字段
    flag: val
  }
  try {
    const res = await api.exportRoles(params)
    if (res.data) {
      const url = window.URL.createObjectURL(new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }))
      const link = document.createElement('a')
      let fileName = ''
      const contentDisposition = res.headers['content-disposition']
      link.style.display = 'none'
      link.href = url
      if (contentDisposition) {
        fileName = window.decodeURI(res.headers['content-disposition'].split('=')[1], 'UTF-8')
      }
      link.setAttribute('download', fileName || 'export.xlsx')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } else {
      ElMessage({
        message: '导出失败，请重试',
        type: 'error'
      })
    }
  } catch (e) {
    console.log(e)
  }
}

getTableList()
getMenuPermissionData()
getDataPermissionData()
</script>

<template>
  <div class="role-view">
    <div class="role-management">
      <div class="select_box">
        
        <div class="name_input">
          <el-input
            v-model="nameInputValue"
            size="small"
            clearable
            :placeholder="$t('ROLE.INPUT_ROLENAME')"
            @keyup.enter="handleClickSearch"
          ></el-input>
        </div>
        <div class="search_button">
          <el-button
            size="small"
            type="primary"
            @click="handleClickSearch"
            :icon="Search"
          >{{$t('ROLE.SEARCH')}}</el-button>
        </div>
        <div class="icon_box">
          <span class="state_label">{{$t('ROLE.HANDLE')}}</span>
          <el-tooltip class="item" effect="dark" :content="$t('USER.DELETE_ALL_ROLE')" placement="top">
            <span class="span-btn">
              <i class="iconfont icon-shanchuyonghujiaose" @click="handleBatchDelete" />
            </span>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" :content="$t('USER.EXPORT')" placement="top">
            <el-dropdown trigger="click" @command="exportSelectOrAll" placement="bottom">
              <span>
                <i class="iconfont icon-xiazaiexcel" />
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="select">{{ $t('ROLE.SELECT_ROLE') }}</el-dropdown-item>
                  <el-dropdown-item command="all">{{ $t('ROLE.ALL_ROLE') }}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-tooltip>
        </div>
        <div class="create_user_button">
          <el-button
            size="small"
            type="primary"
            @click="handleClickCreate"
            :icon="CirclePlus"
          >{{$t('ROLE.ADD_ROLE')}}</el-button>
        </div>
      </div>
      <div class="table_box">
        <el-table
          height="100%"
          :data="tableData"
          :header-cell-style="{ background: '#e7ecf9', height: '55px' }"
          header-cell-class-name="my_table_header"
          @selection-change="handleSelectionChange"
          @sort-change="sortChange"
        >
          <el-table-column type="selection" width="40"></el-table-column>
          <el-table-column type="index" :index="indexMethod" :label="$t('ROLE.ROLE_NUM')" width="55"></el-table-column>
          <el-table-column prop="roleName" :label="$t('ROLE.ROLE_NAME')" sortable="custom" width="220"></el-table-column>
          <el-table-column prop="description" :label="$t('ROLE.ROLE_DESC')" sortable="custom" min-width="110"></el-table-column>
          <el-table-column prop="usageCount" :label="$t('ROLE.ROLE_USER')" sortable="custom" width="170"></el-table-column>
          <el-table-column prop="operator" :label="$t('ROLE.ROLE_OPERATOR')" sortable="custom" width="150"></el-table-column>
          <el-table-column prop="updateTime" :label="$t('ROLE.Updated_TIME')" sortable="custom" width="200"></el-table-column>
          <el-table-column :label="$t('ROLE.OPERATION')" width="240">
            <template #default="scope">
              <span class="table_button" @click="handleClickEdit(scope.$index, scope.row)">
                <el-tooltip class="item" effect="dark" :content="$t('ROLE.EDIT_ROLE')" placement="top">
                  <i class="iconfont icon-edit" />
                </el-tooltip>
              </span>
              <span class="table_button" @click="handleClickPermission(scope.$index, scope.row)">
                <el-tooltip class="item" effect="dark" :content="t('ROLE.EDIT_PERMISION')" placement="top">
                  <i class="iconfont icon-a-DataSecuritys" />
                </el-tooltip>
              </span>
              <span class="table_button" @click="handleClickCopy(scope.$index, scope.row)">
                <el-tooltip class="item" effect="dark" :content="t('ROLE.COPY_ROLE')" placement="top">
                  <i class="iconfont icon-copy" />
                </el-tooltip>
              </span>
              <span class="table_button" @click="handleClickDelete(scope.$index, scope.row)">
                <el-tooltip class="item" effect="dark" :content="t('ROLE.DELETE_ROLE')" placement="top">
                  <i class="iconfont icon-delete" />
                </el-tooltip>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination_box">
        <div class="pagination_title">
          <span>{{ startRow }}-{{ endRow }}</span> of
          <span> {{ pageTotal }} </span>
          result
        </div>
        <el-pagination
          @current-change="handleCurrentChange"
          layout="prev, pager, next"
          :page-size="pageSize"
          :current-page="pageNum"
          :total="pageTotal"
        ></el-pagination>
      </div>
      <el-dialog
        :title="createRoleTitle"
        v-model="createRoleObj.dialogVisible"
        class="createRoleDialog setting-page-dialog-wrap"
        width="480px"
        @closed="handleDialogClosed"
        :close-on-click-modal="false"
      >
        <div class="dialogContent">
          <div class="dialog_box name_box">
            <div class="title">
              <span class="marker">* </span>
              <span>{{$t('ROLE.ROLE_NAME')}}</span>
            </div>
            <div class="input_box">
              <el-input size="small" v-model="createRoleObj.roleName" :placeholder="$t('COMMON.PLS_INPUT')"></el-input>
              <span class="marker" v-show="createRoleObj.markerIsShow">
                ！{{ markerValue }}
              </span>
            </div>
          </div>
          <div class="dialog_box">
            <span class="title" style="margin-right: 22px">{{$t('ROLE.ROLE_DESC')}}</span>
            <div class="input_box">
              <el-input
                v-model="createRoleObj.roleExplain"
                type="textarea"
                :rows="4"
                resize="none"
                :placeholder="$t('COMMON.PLS_INPUT')"
              ></el-input>
            </div>
          </div>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button size="small" @click="handleClickCreateCancel">{{$t('COMMON.CANCEL')}}</el-button>
            <el-button size="small" type="primary" @click="handleClickCreateSubmit">{{$t('COMMON.SUBMIT')}}</el-button>
          </div>
        </template>
      </el-dialog>
      <el-dialog
        title=""
        class="permissionDialog"
        width="480px"
        @closed="handlePermissionDialogClosed"
        v-model="permissionDialog.dialogVisible"
        :close-on-click-modal="false"
      >
        <div>
          <div class="tab_box">
            <span
              class="target-tab"
              :class="{ active: currentTab === 1 }"
              @click="handleClickTab(1)"
            >{{$t('ROLE.PAGE_PERMISION')}}</span>
            <span
              class="target-tab"
              :class="{ active: currentTab === 2 }"
              @click="handleClickTab(2)"
            >{{$t('ROLE.DATA_PERMISION')}}</span>
          </div>
          <div class="tree_box" v-if="permissionDialog.dialogVisible">
            <el-tree
              v-show="currentTab === 1"
              :data="menuTreeData"
              ref="menuTree"
              show-checkbox
              node-key="id"
              :default-checked-keys="defaultMenuChecked"
              :props="defaultMenuProps"
            ></el-tree>
            <el-tree
              v-show="currentTab === 2"
              :data="dataTreeData"
              ref="dataTree"
              show-checkbox
              node-key="id"
              :default-checked-keys="defaultDataChecked"
              :props="defaultDataProps"
            ></el-tree>
          </div>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button size="small" @click="handleClickPermissionCancel">{{$t('COMMON.CANCEL')}}</el-button>
            <el-button size="small" type="primary" @click="handleClickPermissionSubmit">{{$t('COMMON.SUBMIT')}}</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<style lang="less" scoped>
.role-view {
  display: flex;
  flex-direction: column;
  flex: 1;
  // margin: 24px;
  // box-shadow: 0 2px 11px 0 rgba(14, 33, 66, 0.1);
  // border-radius: 6px;
  margin-top: 14px;
  height: 100%;
  background-color: #fff;
  overflow: hidden;
}
.role-management {
  margin: 10px 10px 0;
  font-family: PingFangSC-Semibold;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  height: 100%;
  .select_box {
    display: flex;
    margin-bottom: 20px;
    position: relative;
    .create_user_button {
      position: absolute;
      right: -10px;
    }
    div {
      margin-right: 10px;
    }
    .el-button {
      height: 32px;
    }
    :deep(.el-input) {
      height: 32px;
    }
    .el-select {
      width: 133px;
    }
    .name_input {
      width: 250px;
    }
    .icon_box {
      display: flex;
      align-items: center;
      &>div, &>.span-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 32px;
        height: 32px;
        margin-right: 10px;
        border: 1px solid #C6CCD6;
        border-radius: 4px;
        i {
          font-size: 20px;
          display: inline-block;
          cursor: pointer;
          padding: 0 10px;
          color: #767F8C;
        }
        &:hover {
          border-color: #B5CCF8;
          i {
            color: #568CF4;
          }
        }
      }
      
      .state_label {
        font-size: 16px;
        color: #333333;
        letter-spacing: 1px;
        font-weight: 400;
        display: inline-block;
        margin-right: 10px;
        margin-left: 5px;
        font-weight: bold;
      }
      .user-icon {
        font-size: 20px;
        cursor: pointer;
        vertical-align: middle;
        margin: 0 10px;
        color: #000;
        margin-right: 20px;

        &:hover {
          color: #409EFF;
        }
      }
    }
  }
  :deep(.table_box) {
    user-select: text;
    flex: auto;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    .el-table {
      .table_button {
        display: inline-block;
        margin-right: 30px;
        cursor: pointer;
      }
      .el-table__inner-wrapper::before {
        height: 0;
      }
    }
    .my_table_header {
      padding: 8px 0;
      // border-right: 1px solid #E5E4E4;
      .cell {
        font-size: 14px;
        color: #333433;
        letter-spacing: 0.88px;
        font-weight: 600;
      }
    }
  }
  .pagination_box {
    margin: 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    .pagination_title {
      font-size: 16px;
      background-color: #fff;
      height: 32px;
      padding-left: 10px;
      line-height: 32px;
      span {
        color: #0F91FF;
      }
    }
  }
  :deep(.createRoleDialog) {
    .dialogContent {
      display: flex;
      flex-direction: column;
      align-content: center;
      justify-content: center;
      align-items: center;
      .marker {
        color: #FF0000;
        font-size: 12px;
      }
      .input_box {
        width: 220px;
        position: relative;
        .marker {
          position: absolute;
          width: 100%;
          top: 36px;
          left: 0;
        }
      }
      .el-textarea__inner, .el-input__inner {
        font-family: element-icons;
      }
      .name_box {
        margin-bottom: 26px;
      }
      .title {
        display: inline-block;
        margin-right: 20px;
        text-align: right;
        width: 120px;
        &:nth-child(2) {
          margin-right: 25px;
        }
      }
      .dialog_box {
        display: flex;
        &:first-child {
          align-items: center;
        }
      }
    }
    .dialog-footer {
      text-align: center;
      .el-button {
        &:first-child {
          margin-right: 80px;
        }
      }
      .el-button--small, .el-button--small.is-round {
        padding: 9px 30px;
      }
    }
  }
  :deep(.permissionDialog) {
    .target-tab {
      display: inline-block;
      font-size: 16px;
      letter-spacing: -0.39px;
      border-bottom: 2px rgba(255,255,255,0) solid;
      margin-left: 25px;
      cursor: pointer;
      padding-bottom: 2px;
      color: #333333;
      font-weight: 400;
      &.active {
        border-bottom: 2px #0751A4 solid;
        color: #0073EB;
      }
    }
    .tab_box {
      margin-bottom: 10px;
    }
    .tree_box {
      display: flex;
      justify-content: center;
      height: 400px;
      overflow: auto;
    }
    .el-dialog__body {
      padding: 0;
    }
    .dialog-footer {
      text-align: center;
      .el-button {
        &:first-child {
          margin-right: 80px;
        }
      }
      .el-button--small, .el-button--small.is-round {
        padding: 9px 30px;
      }
    }
  }
}
.table_button i {
  color: #568CF4;
}
</style>
