<script setup>
import { ref, computed } from 'vue'
import { ElLoading, ElMessage } from 'element-plus'
import * as api from './api'

import { useI18n } from 'vue-i18n'
const { t } = useI18n()

// 声明props
const props = defineProps({
  knowledgeId: Number
})

// 接收传递的方法
const emit = defineEmits(['getKnowledgeList'])

// 是否显示对话框
const dialogVisible = ref(false)
// 对话框标题
const dialogTitle = computed(() => {
  return props.knowledgeId ? t('KNOWLEDGE.ADD_KNOW') : t('KNOWLEDGE.CREATE_KNOW')
})
// 已进行的步骤
const stepActive = ref(0)

// 知识库表单数据
const knowledgeObj = {
  name: '',
  owner: '',
  desc: '',
  vectorType: 'Chroma'
}
const knowledgeForm = ref(JSON.parse(JSON.stringify(knowledgeObj)))
const knowledgeRules = {
  name: [{ required: true, message: () => `${t('COMMON.PLS_INPUT')} ${t('KNOWLEDGE.KNOW_NAME')}`, trigger: 'blur' }],
  owner: [{ required: true, message: () => `${t('COMMON.PLS_INPUT')} ${t('KNOWLEDGE.CREATOR')}`, trigger: 'blur' }],
  desc: [{ required: true, message: () => `${t('COMMON.PLS_INPUT')} ${t('KNOWLEDGE.DESCRIPTION')}`, trigger: 'blur' }]
}
// 创建的知识库ID
const newKnowledgeId = ref(null)
// 创建知识库
const addKnowledge = async () => {
  const loading = ElLoading.service({ target: '.el-dialog' })
  try {
    newKnowledgeId.value = await api.addKnowledge(knowledgeForm.value)
    loading.close()
    stepActive.value++
    emit('getKnowledgeList')
  } catch (e) {
    loading.close()
  }
}
// 知识库表单实例
const knowledgeFormRef = ref()
// 知识库下一步
const nextStep = () => {
  knowledgeFormRef.value.validate((valid) => {
    if (valid) {
      addKnowledge()
    }
  })
}

// 数据源表单数据
const dataSourceObj = {
  docType: '',
  docName: '',
  source: '',
  content: '',
  url: '',
  docFile: null,
  sync: true
}
const dataSourceForm = ref(JSON.parse(JSON.stringify(dataSourceObj)))
const dataSourceRules = {
  docName: [{ required: true, message: () => `${t('COMMON.PLS_INPUT')} ${t('KNOWLEDGE.NAME')}`, trigger: 'blur' }],
  source: [{ required: true, message: () => `${t('COMMON.PLS_INPUT')} ${t('KNOWLEDGE.TEXT_SOURCE')}`, trigger: 'blur' }],
  content: [{ required: true, message: () => `${t('COMMON.PLS_INPUT')} ${t('KNOWLEDGE.TEXT_CONTENT')}`, trigger: 'blur' }],
  url: [{ required: true, message: () => `${t('COMMON.PLS_INPUT')} ${t('KNOWLEDGE.WEB_URL')}`, trigger: 'blur' }],
  docFile: [{ required: true, message: () => `${t('COMMON.PLS_SELECT')} ${t('KNOWLEDGE.DOCUMENT')}`, trigger: 'change' }]
}
// 选择数据源类型
const chooseDataSourceType = (type) => {
  dataSourceForm.value.docType = type
  stepActive.value++
}
// 数据源表单实例
const dataSourceFormRef = ref()
// 选择文件
const fileChange = (file) => {
  dataSourceForm.value.docName = dataSourceForm.value.docName || file.name
  dataSourceForm.value.docFile = file.raw
  dataSourceFormRef.value.validateField('docName')
  dataSourceFormRef.value.validateField('docFile')
}
// 移除文件
const fileRemove = () => {
  dataSourceForm.value.docFile = null
  dataSourceFormRef.value.validateField('docFile')
}
// 超出上传限制
const fileExceed = () => {
  ElMessage.warning(t('KNOWLEDGE.ONE_FILE'))
}
// 数据源上一步
const prevStep = () => {
  dataSourceForm.value = JSON.parse(JSON.stringify(dataSourceObj))
  stepActive.value--
}
// 添加知识
const addDataSource = async () => {
  const loading = ElLoading.service({ target: '.el-dialog' })
  const knowledgeId = props.knowledgeId || newKnowledgeId.value
  const { docType, docName, source, content, url, docFile, sync } = dataSourceForm.value
  let data
  if (docType === 'TEXT') {
    data = {
      docType,
      docName,
      source,
      content
    }
  } else if (docType === 'URL') {
    data = {
      docType,
      docName,
      content: url
    }
  } else if (docType === 'DOCUMENT') {
    const formData = new FormData()
    formData.append('docName', docName)
    formData.append('docFile', docFile)
    data = formData
  }
  try {
    let id
    if (docType === 'DOCUMENT') {
      id = await api.uploadDocument(knowledgeId, data)
    } else {
      id = await api.addDataSource(knowledgeId, data)
    }
    loading.close()
    dialogVisible.value = false
    if (sync && id && docType === 'TEXT') {
      api.syncDataSource(knowledgeId, { docIds: [id] })
    }
    emit('getKnowledgeList')
  } catch (e) {
    loading.close()
  }
}
// 数据源完成
const finishStep = () => {
  dataSourceFormRef.value.validate((valid) => {
    if (valid) {
      addDataSource()
    }
  })
}

// 关闭对话框时清除所有数据
const resetData = () => {
  knowledgeForm.value = JSON.parse(JSON.stringify(knowledgeObj))
  dataSourceForm.value = JSON.parse(JSON.stringify(dataSourceObj))
  stepActive.value = 0
  newKnowledgeId.value = null
}

// 暴露属性和方法
defineExpose({ dialogVisible })
</script>

<template>
  <div class="add-dialog">
    <el-dialog
      class="setting-page-dialog-wrap"
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      :close-on-click-modal="false"
      destroy-on-close
      @closed="resetData()"
    >
      <el-steps :active="stepActive" align-center finish-status="success" class="el-steps-wrap">
        <el-step v-if="!props.knowledgeId" :title="$t('KNOWLEDGE.KNOW_BASE_INFO')" />
        <el-step :title="$t('KNOWLEDGE.KNOW_TYPE')" />
        <el-step :title="$t('KNOWLEDGE.KNOW_IMPORT')" />
      </el-steps>
      <el-form
        v-if="!props.knowledgeId && stepActive === 0"
        ref="knowledgeFormRef"
        :model="knowledgeForm"
        :rules="knowledgeRules"
        label-width="100px"
        size="large"
      >
        <el-form-item :label="$t('KNOWLEDGE.KNOW_NAME')" prop="name">
          <el-input v-model="knowledgeForm.name" :placeholder="`${$t('COMMON.PLS_INPUT')} ${$t('KNOWLEDGE.KNOW_NAME')}`" />
        </el-form-item>
        <el-form-item :label="$t('KNOWLEDGE.CREATOR')" prop="owner">
          <el-input v-model="knowledgeForm.owner" :placeholder="`${$t('COMMON.PLS_INPUT')} ${$t('KNOWLEDGE.CREATOR')}`" />
        </el-form-item>
        <el-form-item :label="$t('KNOWLEDGE.DESCRIPTION')" prop="desc">
          <el-input v-model="knowledgeForm.desc" :placeholder="`${$t('COMMON.PLS_INPUT')} ${$t('KNOWLEDGE.DESCRIPTION')}`" />
        </el-form-item>
      </el-form>
      <ul
        v-if="
          (!props.knowledgeId && stepActive === 1) ||
          (props.knowledgeId && stepActive === 0)
        "
        class="data-source-type"
      >
        <li class="data-source-type-item" @click="chooseDataSourceType('TEXT')">
          <!-- <Document class="type-title-icon" /> -->
          <img src="./imgs/doc-2.png" alt="">
          <div>
            <div class="type-title">
              <span>{{$t('KNOWLEDGE.TEXT')}}</span>
            </div>
            <div class="tip-txt">{{$t('KNOWLEDGE.TEXT_DESC')}}</div>
          </div>
        </li>
        <li class="data-source-type-item" style="display: none;" @click="chooseDataSourceType('URL')">
          <!-- <Link class="type-title-icon" /> -->
           <img src="./imgs/net.png" alt="">
          <div>
            <div class="type-title">
              <span>{{$t('KNOWLEDGE.URL')}}</span>
            </div>
            <div class="tip-txt">{{$t('KNOWLEDGE.URL_DESC')}}</div>
          </div>
        </li>
        <li class="data-source-type-item" @click="chooseDataSourceType('DOCUMENT')">
          <!-- <Memo class="type-title-icon" /> -->
           <img src="./imgs/file.png" alt="">
          <div>
            <div class="type-title">
              <span>{{$t('KNOWLEDGE.DOCUMENT')}}</span>
            </div>
            <div class="tip-txt">{{$t('KNOWLEDGE.DOCUMENT_DESC')}}</div>
          </div>
        </li>
      </ul>
      <el-form
        v-if="
          (!props.knowledgeId && stepActive === 2) ||
          (props.knowledgeId && stepActive === 1)
        "
        ref="dataSourceFormRef"
        :model="dataSourceForm"
        :rules="dataSourceRules"
        label-width="100px"
        size="large"
      >
        <el-form-item :label="$t('KNOWLEDGE.NAME')" prop="docName">
          <el-input v-model="dataSourceForm.docName" :placeholder="`${$t('COMMON.PLS_INPUT')} ${$t('KNOWLEDGE.NAME')}`" />
        </el-form-item>
        <el-form-item v-if="dataSourceForm.docType === 'TEXT'" :label="$t('KNOWLEDGE.TEXT_SOURCE')" prop="source">
          <el-input v-model="dataSourceForm.source" :placeholder="`${$t('COMMON.PLS_INPUT')} ${$t('KNOWLEDGE.TEXT_SOURCE')}`" />
        </el-form-item>
        <el-form-item v-if="dataSourceForm.docType === 'TEXT'" :label="$t('KNOWLEDGE.TEXT_CONTENT')" prop="content">
          <el-input
            v-model="dataSourceForm.content"
            type="textarea"
            :rows="4"
            resize="none"
            :placeholder="`${$t('COMMON.PLS_INPUT')} ${$t('KNOWLEDGE.TEXT_CONTENT')}`"
          />
        </el-form-item>
        <el-form-item v-if="dataSourceForm.docType === 'URL'" :label="$t('KNOWLEDGE.WEB_URL')" prop="url">
          <el-input v-model="dataSourceForm.url" :placeholder="`${$t('COMMON.PLS_INPUT')} ${$t('KNOWLEDGE.WEB_URL')}`" />
        </el-form-item>
        <el-form-item v-if="dataSourceForm.docType === 'DOCUMENT'" :label="$t('KNOWLEDGE.DOCUMENT')" prop="docFile">
          <el-upload
            action=""
            drag
            :auto-upload="false"
            :limit="1"
            :on-change="fileChange"
            :on-remove="fileRemove"
            :on-exceed="fileExceed"
            style="width: 100%"
          >
            <!-- <el-icon class="el-icon--upload"><upload-filled /></el-icon> -->
            <img src="./imgs/doc.png" alt="" style="width: 36px;height:46px;margin: 0 auto;">
            <div class="el-upload__text">
              <div style="font-size: 20px">{{$t('KNOWLEDGE.UPLOAD_FILE')}}</div>
              <!-- <div>PDF, PowerPoint, Excel, Word, Text, Markdown</div> -->
              <div>{{$t('KNOWLEDGE.DOCUMENT_DESC')}}</div>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item :label="$t('KNOWLEDGE.SYNC')" prop="sync">
          <el-switch v-model="dataSourceForm.sync" :disabled="dataSourceForm.docType !== 'TEXT'" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button
          v-if="!props.knowledgeId && stepActive === 0"
          type="primary"
          @click="nextStep()"
        >{{$t('KNOWLEDGE.STEP_NEXT')}}</el-button>
        <el-button
          v-if="
            (!props.knowledgeId && stepActive === 2) ||
            (props.knowledgeId && stepActive === 1)
          "
          @click="prevStep()"
        >{{$t('KNOWLEDGE.BACK')}}</el-button>
        <el-button
          v-if="
            (!props.knowledgeId && stepActive === 2) ||
            (props.knowledgeId && stepActive === 1)
          "
          type="primary"
          @click="finishStep()"
        >{{$t('KNOWLEDGE.COMPLETE')}}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.add-dialog {
  :deep(.el-dialog__body) {
    padding: 0 20px;
    .el-form {
      padding-top: 20px;
    }
    .data-source-type {
      color: #000000;
      line-height: 1.5;
      .data-source-type-item {
        border: 1px solid #f0f0f0;
        border-radius: 8px;
        margin: 16px 0;
        cursor: pointer;
        padding: 24px;
        display: flex;
        align-items: center;
        &>img {
          width: 24px;
          height: 24px;
          margin-right: 20px;
        }
        .type-title {
          display: flex;
          align-items: center;
          font-weight: bold;
          color: #0A121F;
          .type-title-icon {
            width: 18px;
            height: 18px;
            margin-right: 8px;
            color: #409eff;
          }
        }
        .tip-txt {
          color: #E2E6EE;
        }
      }
    }
  }
  :deep(.el-textarea) {
    .el-textarea__inner::placeholder {
      font-size: 12px;
      color: #a8abb2;
    }
  }
}
</style>

<style lang="less">
.el-steps-wrap {
  padding: 20px 0;
  .el-step {
    .el-step__head.is-process {
      .el-step__line {
        background-color: #AAC5F9
      }
      .el-step__icon {
        width: 26px;
        height: 26px;
        background-color: #568CF4;
        color: #fff;
        border-color: transparent;
      }
    }
    .el-step__main .el-step__title.is-process {
      color: #568CF4;
      font-size: 12px;
    }

    .el-step__head.is-wait {
      .el-step__line {
        background-color: #ACB2BB
      }
      .el-step__icon {
        width: 26px;
        height: 26px;
        background-color: #fff;
        color: #ACB2BB;
        border-color: #ACB2BB;
      }
    }
    .el-step__main .el-step__title.is-wait {
      color: #5B6579;
      font-size: 12px;
    }

    .el-step__head.is-success {
      .el-step__line {
        background-color: #30C462;
      }
      .el-step__icon {
        width: 26px;
        height: 26px;
        background-color: #30C462;
        color: #fff;
      }
    }
    .el-step__main .el-step__title.is-success {
      color: #30C462;
      font-size: 12px;
    }
  }
}
</style>
