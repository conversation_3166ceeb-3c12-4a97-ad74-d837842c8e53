import request from '@/utils/axios'

const url = '/menu/management/'

// 菜单列表
function getMenuTableList(data) {
  return request({
    url: url + 'search',
    method: 'post',
    data
  })
}

// 添加菜单
function addMenuConfig(data) {
  return request({
    url: url + 'add',
    method: 'post',
    data
  })
}

// 修改菜单
function editMenuConfig(data) {
  return request({
    url: url + 'update',
    method: 'post',
    data
  })
}

// 删除菜单
function deleteMenuConfig(data) {
  return request({
    url: url + 'delete',
    method: 'post',
    data
  })
}

export {
  getMenuTableList,
  addMenuConfig,
  editMenuConfig,
  deleteMenuConfig
}
