export default {
  COMMON: {
    THINK_PROCESS: '思考过程',
    SEARCH: '查询',
    RESET: '重置',
    CREATE: '创建',
    CREATOR: '创建人',
    LAST_UPDATE: '更新时间',
    OPERATIONS: '操作',
    EDIT: '编辑',
    COPY: '复制',
    DELETE: '删除',
    SETTING: '设置',
    RENAME: '重命名',
    ALL: '全部',
    COLLECT: '收藏',
    DETAIL: '详情',
    DELETE_CONFIRM: '是否确认删除?',
    DELETE_SUCCESS: '删除 成功',
    CONFIRM: '是否确认',
    SUCCESS: '成功',
    FAIL: '失败',
    STATUS: '状态',
    RESULT: '结果',
    PLS_INPUT: '请输入',
    PLS_SELECT: '请选择',
    MUST_BE_NUM: '必须是数字',
    NEXT: '下一步',
    BACK: '上一步',
    CANCEL: '取消',
    SUBMIT: '提交',
    CLOSE: '关闭',
    NO: '否',
    YES: '是',
    AGENT_MARKET: '智能体市场',
    INTELLIGENT_ANALYSIS: '智能分析',
    KNOWLEDGE_SERVICE: '知识服务',
    InsightSuccess: '数据洞察完成',
    InsightFailed: '数据洞察失败，请尝试刷新页面或联系管理员'
  },
  LEFT_BAR: {
    NEW_CHAT: '新对话',
    SEARCH_HISTORY: '搜索历史对话',
    START_DATE: '开始日期',
    TO: '至',
    END_DATE: '结束日期',
    LAST_1_DAY: '近1天',
    LAST_7_DAYS: '近7天',
    LAST_30_DAYS: '近30天',
    SHARE: '分享',
    PIN: '置顶',
    UNPIN: '取消置顶',
    LANGUAGE: '语言'
  },
  MENUS: {
    PROMPT: '提示语',
    DATA_SOURCE: '数据源',
    KNOWLEDGE: '知识库',
    PLUGIN: '插件管理',
    APP: '应用管理',
    USER: '用户管理',
    ROLE: '角色管理',
    MENU: '菜单管理',
    CREATE: '新增',
    MENUNAME: '菜单名称',
    RESET: '重置',
    PATH: '路径',
    SORT: '排序',
    PERMISSION: '是否开放权限',
    OPERATOR: '维护人',
    UPDATE: '更新时间',
    OPERATION: '操作',
    CREATE_MENU: '新增菜单',
    CREATE_SUCCESS: '新增成功',
    DELETE_SUCCESS: '删除成功',
    EDIT_SUCCESS: '修改成功',
    EDIT_MENU: '修改菜单',
    COPY_MENU: '复制菜单',
    DELETE_MENU: '删除菜单',
    KFQX: '开发权限',
    ACTIVE: '启用',
    NO_ACTIVE: '禁用',
    CANCEL: '取 消',
    SUBMIT: '提 交',
    DELETE_TIPS: '此操作将永久删除该菜单，是否继续?',
    RULE_MENU: '请输入菜单名称',
    RULE_PATH: '请输入路径',
    RULE_SORT: '请输入排序',
  },
  HOME: {
    QUICK_STRAT: '快速开始',
    CHAT_DATA_DESC: '通过自然语言与您的私人数据对话。',
    CHAT_KNOW_DESC: '通过自然语言与您的私人知识对话。',
    CHAT_DATA_APP: '通过自然语言与您的私人应用对话。'
  },
  CHAT: {
    START_CHAT: '开始对话',
    DIALOG_WINDOW: '对话窗口',
    INPUT_WARNING: '用户查询生成的内容，是大语言模型基于用户问题和私有化知识推断而来，不能保证100%准确性，请验证后再借鉴使用。',
    EMPTY_WARNING: '发送的消息不能为空',
    CHAT_ERROR: '对不起，我们遇到了一些错误，请稍后再试。',
    RATING: '评分',
    QA_CATEGORY: '问答类别',
    QA_RATING: '问答评分',
    CLICK_PROMPT: '点击选择提示语',
    ADVICES: '自动推荐',
    CHART_TYPE: '图表类型',
    DIMENSION_FILTER: '筛选条件',
    DIMENSION_GROUP: '分组维度',
    NO_SUPPORT_PIE: '当前数据不适用饼图',
    VIEW_SQL: '查看sql语句',
    DATA_INSIGHT: '数据洞察',
    INTELLIGENT_PREDICTION: '智能预测',
    RENAME_SUCCESS: '重命名成功',
    DATA_SOURCE: '数据源',
    SYSTEM_PROMPT: '系统提示词',
    APP_PROMPT: '应用提示词',
    NEW_NAME: '新命名',
    OTHER_REASON: '请输入其他原因',
    LIKE_REASON: '请选择让您满意的原因',
    DISLIKE_REASON: '请选择让您不满意的原因',
    FEEDBACK_SUCCESS: '反馈成功',
    TIPS_FEEDBACK: '请选择反馈内容',
    WELCOME_HI: '您好，欢迎进入',
    WELCOME_DATASOURCE: '该应用的数据源信息如下',
    WELCOME_QUESTION: '您可以这样问我',
    MORE_QUESTION: '更多问题',
    INSIGHT_SUCCESS: '数据洞察完成',
    INSIGHT_FAIL: '数据洞察失败'
  },
  CHART: {
    CARD_CHART: '卡片图',
    LINE_CHART: '折线图',
    STEP_LINE_CHART: '阶梯折线图',
    AREA_CHART: '面积图',
    COLUMN_CHART: '柱状图',
    BAR_CHART: '条形图',
    PIE_CHART: '饼状图',
    DONUT_CHART: '环形图',
    FUNNEL_CHART: '漏斗图',
    SCATTER_PLOT: '散点图',
    BUBBLE_CHART: '气泡图'
  },
  PROMPT: {
    VIEW: '查看',
    VIEW_PROMPT: '查看提示语',
    INPUT_NAME: '请输入提示语名称',
    SELECT_TYPE: '请选择提示语类型',
    TYPE_KNOW: '知识库',
    TYPE_DATA: '数据源',
    TYPE_INSIGHT: '洞察',
    SELECT_SCENE: '请选择应用场景',
    SELECT_CREATOR: '请选择提示语创建人',
    SYSTEM: '系统',
    APPLICATION: '应用',
    PROMPT_NAME: '提示语名称',
    PROMPT_TYPE: '提示语类型',
    APP_SCENE: '应用场景',
    ROLE_DESC: '身份描述',
    CONTENT: '内容',
    CREATE_PROMPT: '创建提示语',
    EDIT_PROMPT: '编辑提示语',
    CONTENT_TIP: '以下内容作为提示词将会提升生成sql的准确性。1. 与该场景相关的业务知识。2. 与该数据源相关的常见问答对样本。问题为查询自然语言，回答为正确的sql语句。',
    DEFAULT_CONTENT: '基于以下已知的信息, 专业、简要的回答用户的问题,\n            如果无法从提供的内容中获取答案, 请说: "知识库中提供的内容不足以回答此问题" 禁止胡乱编造, 回答的时候最好按照1.2.3.点进行总结。 \n            已知内容: \n            {\'{\'}context{\'}\'}\n            问题:\n            {\'{\'}question{\'}\'},请使用和用户相同的语言进行回答.'
  },
  DATA_SOURCE: {
    UpdateSuccess: '同步成功',
    UpdateFaile: '同步失败',
    UPDATE: '同步',
    INPUT_NAME: '请输入名称',
    INPUT_DESC: '请输入描述',
    SELECT_TYPE: '请选择类型',
    SELECT_CREATOR: '请选择创建人',
    DATA_SOURCE_TYPE: '类型',
    DATA_SOURCE_NAME: '名称',
    DATA_SOURCE_DESC: '描述',
    METADATA_OWNER: 'Metadata负责人',
    BIND_API: '绑定API',
    METADATA_MGT: 'Metadata管理',
    CREATE_DATA_SOURCE: '创建数据源',
    EDIT_DATA_SOURCE: '编辑数据源',
    COPY_DATA_SOURCE: '复制数据源',
    NO_CLUSTERS: '未查询出任何资源',
    NO_TABLES: '未查询出任何表',
    ENVIRONMENT: '环境',
    PROD: '生产',
    TEST: '测试',
    HOST: '主机',
    PORT: '端口',
    DATABASE: '数据库',
    USERNAME: '账号',
    PASSWORD: '密码',
    VNET: '世纪互联',
    WORKSPACE: '工作区',
    REPORT_NAME: '报表名称',
    RLS_ROLE_NAME: 'RLS角色名称',
    CLUSTER: '资源',
    TABLE_NAME: '表名',
    TABLE_DESC: '表描述',
    TABLE_ENTER: '请输入名称',
    COL_NAME: '字段名称',
    BUSI_NAME: '业务名称',
    MEASURE: '度量',
    DIMENSION: '维度',
    TIME: '时间',
    OTHER: '其他',
    AI_RECOM: 'AI智能推荐',
    DESCRIPTION: '描述',
    OPERATION: '操作',
    TABLE_INFO: '表信息',
    TABLE_BUSINESS: '表业务名称',
    Attribute_Info: '字段信息',
    NAME: '名称',
    COLUMN_NAME: '名称',
    COLUMN_TYPE: '类型',
    COLUMN_VIEIBILITY: '是否可见',
    TYPE: '类型',
    VIEIBILITY: '是否可见',
    ATTRIBUTE_NAME: '属性名称',
    ADD_INFO: '补充描述',
    LANGUAGE_TYPE: '语义类型',
    VISIBLESWITCH: '全可见',
    GETBUTTON: '获取',
    DEMO_TIPS: '建议包含表的业务含义描述、包含字段、表用途。比如：这是一张记录客户订单明细的表，包含财年、财月、销售金额、销售数量等字段，该表可在用户提问订单收入时使用。',
    DEMO_TIPS2: '若存在多个，以逗号分隔',
    DEMO_TIPS3: '“是否可见”控制大模型能否获取到该字段的Metadata信息。可将回答问题所需的字段设置为可见，其余字段设置为不可见。引入不必要的字段可能会导致大模型幻觉、降低查询准确率。',
    ROLE: '角色'
  },
  PLUGIN: {
    INPUT_NAME: '请输入插件名称',
    SELECT_TYPE: '请选择插件类型',
    PLUGIN_NAME: '插件名称',
    PLUGIN_TYPE: '插件类型',
    PLUGIN_DESC: '插件描述',
    VERIFY: '验证',
    PUBLISH: '发布',
    OFFLINE: '下线',
    CREATE_PLUGIN: '创建插件',
    EDIT_PLUGIN: '编辑插件',
    API_TYPE: '接口类型',
    CALL_METHOD: '调用方式',
    PLUGIN_URL: '插件地址',
    HTTP_TIPS: '地址需要带上http/https协议头',
    HTTP_ERROR: 'URL必须以http/https开头',
    REQUEST_INFO: '请求信息',
    REQUEST_HEAD: '请求头',
    PARAM_NAME: '参数名',
    PARAM_TYPE: '参数类型',
    PARAM_DESC: '参数描述',
    REQUIRED: '是否必填',
    DEFAULT_VALUE: '默认值',
    REQUEST_DATA: '请求数据',
    VALUE_SOURCE: '取值来源',
    RESPONSE_INFO: '响应信息',
    RESPONSE_DATA: '响应数据',
    DATA_DESC: '返回参数的描述',
    STATUS_DESC: '成功失败状态',
    MSG_DESC: '若失败，返回失败原因，否则为空',
    SUBMIT_TIPS: '插件验证成功后再提交',
    VERIFY_PLUGIN: '验证插件'
  },
  USER: {
    INPUT_ITCODE: '请输入ITCode，多个以 / 分隔',
    ERROR_ITCODE: 'ITCode存在非法字符，多个以 / 分隔',
    INPUT_ITCODE_ONCE: '请输入ITCode',
    INPUT_ROLE: '请输入角色名称',
    SELECT_STATUS: '请选择状态',
    NUM: '序号',
    NAME: '姓名',
    DEPARMENT: '部门',
    ROLE_NAME: '角色名称',
    COMMENT: '备注',
    OPERAOTOR: '操作者',
    Updated_TIME: '更新时间',
    OPERATION: '操作',

    DELETE: '删除',
    CREATE: '新增',
    EDIT: '编辑',
    EDIT_USER: '编辑用户',
    COPY_USER: '复制用户',
    DELETE_USER: '删除用户',
    COPY: '复制',
    USER: '用户',
    USERNAME: '用户名',
    TIPS1: '请添加角色',
    TIPS2: '请至少添加一个角色',
    TIPS3: '请至少选择一个用户',
    TIPS4: '上传失败，请尝试重新上传',
    TIPS5: '正在上传中，请勿关闭页面',
    TIPS6: '上传文件只能是 XLS / XLSX 格式!',
    TIPS7: '上传成功',
    TIPS8: '您输入的姓名和itcode数量不匹配，请调整后提交',
    TIPS9: '删除成功',
    DELETE_ALL: '批量删除用户',
    EXPORT: '导出',
    UploadFile: '上传文件',
    ADD_ALL_ROLE: '批量添加角色',
    DELETE_ALL_ROLE: '批量删除角色',
    SelectUser: '选中用户',
    AllUser: '全部用户',
  },
  APP: {
    INPUT_APPNAME: '应用名称',
    APP_TYPE: '应用类型',
    SELECT_SCENE: '场景',
    MODEL_NAME: '模型',
    AGENT_SETTING: '智能体设置',
    AGENT_TYPE: '智能体类型',
    ADD_AGENT: '添加智能体',
    SELECT_KPITYPE: 'KPI类型',
    DESC: '描述',
    STATE: '状态',
    CREATE_APP: '创建应用',
    EDIT_APP: '编辑应用',
    VIEW_APP: '查看应用',
    DATA_SOURCE: '数据源',
    PROMPT: '提示语',
    DEMO_QUESTION: '示例问题',
    OWNER: '负责人',
    PLUGIN: '插件',
    RESOURCE: '资源',
    PUBLISHED: '已发布',
    UNPUBLISHED: '未发布',
    MULTI_AGENT_APP: '多智能体应用',
    NATIVE_APP: '原生应用',
    APPLICATION_TYPE: '应用类型',
    NATIVE_APPLICATION: '原生应用',
    MULTI_AGENT_APPLICATION: '多智能体应用',
    NATIVR_DESC: '采用标准问数模板创建应用',
    MULTI_DESC: '创建配置了多个智能体的应用，规划并协同完成任务',
    SELECT_APP_TYPE: '请选择应用类型',
    AGENT_CONFIG: ' 请配置至少一项智能体',
    INSIGHT_PROMPT: '洞察提示语',
    INSIGHT_KNOWLEDGE: '洞察知识库',
    INSIGHT_MODEL: '洞察模型'
  },
  ROLE: {
    ADD_ROLE: '新建角色',
    INPUT_ROLENAME: '请输入角色名称，多个以/分隔',
    SEARCH: '搜索',
    HANDLE: '批量操作',
    ROLE_NUM: '序号',
    ROLE_NAME: '角色名称',
    ROLE_DESC: '角色说明',
    ROLE_USER: '角色使用人数',
    ROLE_OPERATOR: '操作者',
    Updated_TIME: '更新时间',
    OPERATION: '操作',
    EDIT_ROLE: '编辑角色',
    COPY_ROLE: '复制角色',
    DELETE_ROLE: '删除角色',
    DELETE_TIPS: '此操作将永久删除该条记录，是否继续?',
    EDIT_PERMISION: '编辑权限',
    PAGE_PERMISION: '页面权限',
    DATA_PERMISION: '数据权限',
    SELECT_ROLE: '选中角色',
    ALL_ROLE: '全部角色',
    SELECT_ONE: '请至少选择一个角色'
  },
  AGENT: {
    INPUT_NAME: '请输入应用名称',
    SELECT_SCENE: '请选择场景',
    WELCOME: '您可以选择以下某一应用对数据进行提问。',
    COPY_SUCCESS: '复制成功',
    COPY_FAIL: '复制失败',
    ADD_FAVORITE: '添加收藏',
    CANCEL_FAVORITE: '取消收藏'
  },
  KNOWLEDGE: {
    CREATE: '创建',
    CREATOR: '创建人',
    DESCRIPTION: '描述',
    LAST_TIEM: '最后修改时间',
    CONVERSATION: '对话',
    DELETE_KNOW: '删除知识库',
    SURE_DELETE_KNOW: '确认删除改知识库？',
    DATA_SAFE: '数据合规提示',
    DATA_SAFE_CONTENT: '1. 仅限于联想内部的数据，基于中国区数据安全和隐私保护的要求，为了避免数据隐私保护的不合规风险，不适合上传包含任何敏感数据(包含PII/CI/LR)的数据，如需上传，需联系安全和法务进行评估 <br> <br> 2. 涉及第三方版权的不应作为输入 <br> <br> 3. 内容不符合国家政策的(黄赌毒或者反动言论之类的)不应作为输入',
    CREATE_KNOW: '创建知识库',
    KNOW_BASE_INFO: '知识库基础信息',
    KNOW_TYPE: '知识库类型',
    KNOW_IMPORT: '知识库导入',
    KNOW_NAME: '知识库名称',
    STEP_NEXT: '下一步',
    TEXT: '文本',
    TEXT_DESC: '填写您的原始文本',
    URL: '网址',
    URL_DESC: '获取URL的内容',
    DOCUMENT: '文档',
    DOCUMENT_DESC: '上传文档，文档类型可以是PDF、CSV、Text、PowerPoint、Word、Markdown',
    NAME: '名称',
    TEXT_SOURCE: '文本来源',
    TEXT_CONTENT: '内容',
    SYNC: '同步',
    BACK: '上一步',
    COMPLETE: '完成',
    WEB_URL: '网页网址',
    UPLOAD_FILE: '选择或拖拽文件',
    PARAMS: '参数',
    EMBED: '嵌入',
    PROMPT: '提示语',
    SUMMARY: '总结',
    ADD_KNOW: '添加知识',
    CHUNKING: '切片',
    LAST_SYNC_TIME: '上次同步时间',
    DETAIL: '详情',
    DELETE: '删除',
    TOPK: '基于相似度得分的前 k 个向量',
    RECALL_TYPE: '回忆类型',
    CHUNK_SIZE: '处理中使用的数据块的大小',
    RECALL_SCORE: '设置相似向量检索的阈值分数',
    MODEL: '用于创建文本或其他数据的矢量表示的模型',
    CHUNK_OVERLAP: '相邻数据块之间的重叠',
    SCENE: '用于定义使用提示的设置或环境的上下文参数',
    TEMPLATE: '预定义的提示结构或格式，有助于确保人工智能系统生成与所需风格或语气一致的响应',
    MAX_TOKEN: '提示词中允许的最大单词数',
    CONTENT: '内容',
    METADATA: '元数据',
    DELETE_DATA_SOURCE: '删除数据源',
    SURE_DELETE_DATA_SOURCE: '是否确认删除该数据源？',
    ONE_FILE: '一次只能上传一个文件'
  }
}
