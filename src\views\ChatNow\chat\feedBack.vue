<template>
<div :class="{feedback: true, active: isActive}">
  <el-popover v-model:visible="propData.resData[feedbackTypeVisible]"
    @before-enter="handleClick"
    @after-leave="resetLikeForm"
    transition="el-zoom-in-top"
    placement="top"
    width="420"
    trigger="click"
  >
    <template #reference>
      <slot name="optBtn"></slot>
    </template>
    <div class="like-no">
      <span class="close-btn" @click="handleClose">
        <el-icon>
          <Close />
        </el-icon>
      </span>
      <p class="reason">{{ propData.title }}</p>
      <el-checkbox-group v-model="likeForm.tag" size="small">
        <el-checkbox-button v-for="item in propData.reasonList" :key="item.id" :label="item.zhTxt">
          {{ locale === 'en_US' ? item.enTxt : item.zhTxt }}
        </el-checkbox-button>
      </el-checkbox-group>
      <el-input v-if="showOther"
        type="textarea"
        v-model="likeForm.text"
        :rows="2"
        :autosize="{ minRows: 2, maxRows: 3 }"
        resize="none"
        :placeholder="$t('CHAT.OTHER_REASON')"
      />
      <div class="opt-block" v-if="!propData.resData.evaluateStatus">
        <el-button type="primary" size="small" @click="submitInfo">{{$t('COMMON.SUBMIT')}}</el-button>
      </div>
    </div>
  </el-popover>
</div>
  
</template>
<script setup>
import { ElMessage } from 'element-plus'
import { reactive, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import {useRoute} from 'vue-router'

import * as api from './api/index.js'

const { t, locale } = useI18n()
const route = useRoute()

const propData = defineProps({
  title: String,
  requestId: String,
  reasonList: Array,
  resData: Object, // 当前问题返回的数据
  type: Number // 1 zan 2 cai
})

const emit = defineEmits(['updatedItemData', 'hidePopover'])

// 赞/踩按钮反馈内容
const likeForm = reactive({
  tag: [],
  text: ''
})

const getLocalStorageId = () => {
  let currentId = localStorage.getItem('currentId')
  if(currentId === "undefined") {
    return undefined
  }
  return currentId
}

const feedbackTypeVisible = computed(() => {
  return propData.type === 1 ? 'likeVisible' : 'disLikeVisible'
})

// 其他原因 输入框控制
const showOther = computed(() => {
  return likeForm.tag.includes('其他')
})

// 0 初始状态
const isActive = computed(() => propData.resData.evaluateType !== 0)

const handleClose = () => {
  emit('hidePopover', feedbackTypeVisible.value)
}

// 点击赞或踩
const handleClick = async () => {
  // 点击过 初始化反馈信息
  if(isActive.value) {
    likeForm.text = propData.resData.otherMessage
    likeForm.tag = propData.resData.reason ? propData.resData.reason.split(',') : []
    return
  }
  const params = {
    convUid: route.query.id || getLocalStorageId(),
    requestId: propData.requestId,
    evaluateType: propData.type
  }
  // 创建反馈信息 +踩清理缓存
  await api.createFeedback(params)
  emit('updatedItemData', propData.type)
}

const submitInfo = async () => {
  if(likeForm.tag.length === 0) {
    ElMessage({
      message: t('CHAT.TIPS_FEEDBACK'),
      type: 'warning'
    })
    return
  }

  const params = {
    convUid: route.query.id || getLocalStorageId(),
    requestId: propData.requestId,
    reason: [...likeForm.tag].join(','),
    otherMessage: likeForm.text
  }

  await api.updateFeedbackInfo(params)

  // emit('updatedItemData', 'evaluateStatus')
  emit('updatedItemData', {
    tag: params.reason,
    text: params.otherMessage
  })
  emit('hidePopover', feedbackTypeVisible.value)
  ElMessage({
    message: t('CHAT.FEEDBACK_SUCCESS'),
    type: 'success'
  })
}

const resetLikeForm = () => {
  likeForm.tag = []
  likeForm.text = ''
}

</script>
<style lang='less' scoped>
.like-no {
  position: relative;

  .close-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    cursor: pointer;
  }

  .reason {
    font-size: 12px;
    margin-bottom: 10px;
  }

  .opt-block {
    display: flex;
    justify-content: end;
    margin-top: 10px;
  }

  .el-checkbox-group {
    .el-checkbox-button {
      margin: 0 5px 5px 0;
      --el-checkbox-button-checked-border-color: #dcdfe6;
    }

    :deep(.el-checkbox-button__inner) {
      border-radius: 4px;
      border-left-color: #dcdfe6;
      box-shadow: none;
    }
  }

  .el-textarea {
    margin-top: 6px;
  }

  :deep(.el-textarea__inner)::placeholder {
    font-size: 12px;
  }
}

.feedback {
  &.active :deep(.opt-icon-block) {
    color: #568cf3;
    font-weight: bold; 
  }
  :deep(.opt-icon-block) {
    width: 40px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    cursor: pointer;
    .icon {
      font-size: 16px;
      color: #606266;
    }
  }
}

</style>