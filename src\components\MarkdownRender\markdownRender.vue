<script setup>
import { Marked } from 'marked'
import { markedHighlight } from 'marked-highlight'
import hljs from 'highlight.js'
import 'highlight.js/styles/monokai.min.css'
import { computed } from 'vue'

const marked = new Marked(
  markedHighlight({
    langPrefix: 'hljs language-',
    highlight(code, lang) {
      const language = hljs.getLanguage(lang) ? lang : 'plaintext'
      return hljs.highlight(code, { language }).value
    }
  })
)
const renderer = new marked.Renderer()
const linkRenderer = renderer.link
renderer.link = (href, title, text) => {
  const html = linkRenderer.call(renderer, href, title, text)
  // marked生成的a标签改为新窗口跳转
  return html.replace(/^<a /, '<a target="_blank" ')
}
marked.setOptions({ renderer })

const props = defineProps({
  context: String
})

const html = computed(() => {
  // 将转义的\\n恢复为\n 并处理table和tr标签 否则marked无法解析
  const text = props.context
    .replaceAll('\\n', '\n')
    .replace(/<table(\w*=[^>]+)>/gi, '<table $1>')
    .replace(/<tr(\w*=[^>]+)>/gi, '<tr $1>')
    .split('<references')[0] // 去除references标签
  return marked.parse(text)
})
</script>

<template>
  <div class="markdown-render" v-html="html"></div>
</template>

<style lang="less">
.markdown-render {
  // line-height: 32px;
  line-height: 24px;
  li {
    color: #4b5563;
    font-size: 14px;
    // line-height: 28px;
    line-height: 24px;
    padding-left: 8px;
    margin-left: 20px;
  }
  ul {
    padding: 4px 0;
    li {
      list-style: disc;
    }
  }
  ol {
    padding: 4px 0;
    li {
      list-style: decimal;
    }
  }
  a {
    color: #2563eb;
  }
  code {
    font-family: ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;
    border-radius: 4px;
    font-size: 14px;
  }
  p>code, li>code {
    background: #374151;
    color: #f3f4f6;
    padding: 2px 6px;
    line-height: 20px;
  }
  pre {
    margin: 8px 0;
    padding: 6px 16px 10px;
    background: #272822;
    color: #dddddd;
    border-radius: 5px;
    overflow-x: scroll;
    code {
      line-height: 1.5;
    }
  }
}
</style>
