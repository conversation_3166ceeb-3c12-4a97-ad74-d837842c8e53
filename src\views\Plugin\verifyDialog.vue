<script setup>
import { useI18n } from 'vue-i18n'
import { ref } from 'vue'
import * as api from './api'
import { ElLoading, ElMessage } from 'element-plus'

const { locale, t } = useI18n()

// 声明props
const props = defineProps({
  verifyData: Object
})

// 接收传递的方法
const emit = defineEmits([
  'changeVerified',
  'getPluginList'
])

// 是否显示对话框
const dialogVisible = ref(false)

// 验证插件表单数据
const verifyObj = {
  apiName: '',
  callingMethod: '',
  apiPath: '',
  apiHeadersList: [],
  apiRequestList: [
    {
      requestName: 'user_q',
      requestValue: '',
      isMust: '1'
    }
  ],
  responseData: ''
}
const verifyForm = ref(JSON.parse(JSON.stringify(verifyObj)))
// 根据id查询插件明细
const getPluginDetail = async (id) => {
  const loading = ElLoading.service({ target: '.el-dialog' })
  let data = {}
  try {
    const res = await api.getPluginDetail({ id })
    loading.close()
    data = res || {}
  } catch (e) {
    loading.close()
  }
  return data
}
// 填充数据
const resetData = async (isOpen) => {
  if (isOpen) {
    const res = props.verifyData.id ? await getPluginDetail(props.verifyData.id) : props.verifyData
    verifyForm.value = {
      apiName: res.apiName || '',
      callingMethod: res.callingMethod || '',
      apiPath: res.apiPath || '',
      apiHeadersList: res.apiHeadersList?.length ? res.apiHeadersList.map((item) => {
        return {
          headerName: item.headerName,
          defaultValue: item.defaultValue,
          isMust: item.isMust
        }
      }) : [],
      apiRequestList: res.apiRequestList?.length ? res.apiRequestList.map((item) => {
        return {
          requestName: item.requestName,
          requestValue: '',
          isMust: item.isMust
        }
      }) : [{ requestName: 'user_q', requestValue: '', isMust: '1' }],
      responseData: ''
    }
  } else {
    verifyForm.value = JSON.parse(JSON.stringify(verifyObj))
  }
}

// 验证插件表单实例
const verifyFormRef = ref()
// 验证插件
const verifyPlugin = () => {
  verifyFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const data = JSON.parse(JSON.stringify(verifyForm.value))
        const res = await api.verifyPlugin({
          callingMethod: data.callingMethod,
          apiPath: data.apiPath,
          apiHeadersList: data.apiHeadersList.map((item) => {
            return {
              headerName: item.headerName,
              defaultValue: item.defaultValue
            }
          }),
          apiRequestList: data.apiRequestList.map((item) => {
            return {
              requestName: item.requestName,
              requestValue: item.requestValue
            }
          })
        })
        ElMessage({
          message: `${t('PLUGIN.VERIFY')} ${t('COMMON.SUCCESS')}`,
          type: 'success'
        })
        verifyForm.value.responseData = `${t('COMMON.STATUS')}: ${t('COMMON.SUCCESS')}\n${t('COMMON.RESULT')}: ${res}`
        emit('changeVerified', true)
      } catch (e) {
        verifyForm.value.responseData = `${t('COMMON.STATUS')}: ${t('COMMON.FAIL')}\n${t('COMMON.RESULT')}: ${e.data.msg}`
        emit('changeVerified', false)
      }
    }
  })
}

// 暴露属性和方法
defineExpose({ dialogVisible })
</script>

<template>
  <div class="verify-dialog">
    <el-dialog
      class="setting-page-dialog-wrap"
      v-model="dialogVisible"
      :title="$t('PLUGIN.VERIFY_PLUGIN')"
      width="800px"
      :close-on-click-modal="false"
      destroy-on-close
      @open="resetData(true)"
      @closed="resetData(false)"
    >
      <el-scrollbar class="verify-dialog-inner">
        <el-form
          ref="verifyFormRef"
          :model="verifyForm"
          :label-width="locale === 'en_US' ? '125px' : '86px'"
        >
          <div style="display: flex; margin-bottom: 14px;">
            <el-text tag="b">{{ $t('PLUGIN.PLUGIN_NAME') }}</el-text>
            <span>{{ verifyForm.apiName }}</span>
          </div>
          <el-text tag="b">{{ $t('PLUGIN.REQUEST_INFO') }}</el-text>
          <el-form-item
            v-if="verifyForm.apiHeadersList.length"
            :label="$t('PLUGIN.REQUEST_HEAD')"
          >
            <el-form-item
              v-for="(item, index) in verifyForm.apiHeadersList"
              :key="index"
              :prop="`apiHeadersList.${index}.defaultValue`"
              :rules="[{ required: item.isMust === '1', message: $t('COMMON.PLS_INPUT'), trigger: 'blur' }]"
              label-width="100px"
            >
              <template #label>
                <div class="overflow-tooltip-text">{{ item.headerName }}</div>
              </template>
              <el-input
                v-model="item.defaultValue"
                :placeholder="$t('COMMON.PLS_INPUT')"
              />
            </el-form-item>
          </el-form-item>
          <el-form-item :label="$t('PLUGIN.REQUEST_DATA')" required>
            <el-form-item
              v-if="verifyForm.apiRequestList[0].requestName === 'user_q'"
              prop="apiRequestList.0.requestValue"
              :rules="[{ required: true, message: $t('COMMON.PLS_INPUT'), trigger: 'blur' }]"
            >
              <el-input
                v-model="verifyForm.apiRequestList[0].requestValue"
                type="textarea"
                :rows="3"
                resize="none"
                :placeholder="$t('COMMON.PLS_INPUT')"
              />
            </el-form-item>
            <template v-else>
              <el-form-item
                v-for="(item, index) in verifyForm.apiRequestList"
                :key="index"
                :prop="`apiRequestList.${index}.requestValue`"
                :rules="[{ required: item.isMust === '1', message: $t('COMMON.PLS_INPUT'), trigger: 'blur' }]"
                label-width="100px"
              >
                <template #label>
                  <div class="overflow-tooltip-text">{{ item.requestName }}</div>
                </template>
                <el-input
                  v-model="item.requestValue"
                  :placeholder="$t('COMMON.PLS_INPUT')"
                />
              </el-form-item>
            </template>
          </el-form-item>
          <div class="verify-btn">
            <el-button type="primary" @click="verifyPlugin()">{{ $t('PLUGIN.VERIFY') }}</el-button>
          </div>
          <el-text tag="b">{{ $t('PLUGIN.RESPONSE_INFO') }}</el-text>
          <el-form-item :label="$t('PLUGIN.RESPONSE_DATA')">
            <el-input
              v-model="verifyForm.responseData"
              type="textarea"
              :rows="3"
              resize="none"
            />
          </el-form-item>
        </el-form>
      </el-scrollbar>
      <template #footer>
        <el-button @click="dialogVisible = false">{{ $t('COMMON.CLOSE') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.verify-dialog {
  :deep(.el-dialog) {
    margin-top: 15vh;
    .el-dialog__body {
      padding: 0;
      .verify-dialog-inner {
        .el-scrollbar__wrap {
          max-height: calc(70vh - 116px);
          .el-form {
            padding: 18px 20px;
            .el-form-item {
              width: 100%;
              margin-bottom: 0;
              .el-form-item {
                margin-bottom: 18px;
              }
              .overflow-tooltip-text {
                display: inline-block;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                text-align: right;
              }
            }
            .verify-btn {
              display: flex;
              justify-content: flex-end;
              margin-bottom: 20px;
            }
          }
        }
      }
      b {
        display: block;
        margin: 0 20px 12px 10px;
        color: #303133;
      }
    }
  }
}
</style>
