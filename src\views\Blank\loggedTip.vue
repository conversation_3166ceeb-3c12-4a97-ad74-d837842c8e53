<template>
<div>
  <div class="logged">
    <h1>您已获取Azure访问权限，可以返回继续操作</h1>
  </div>
</div>
</template>
<script setup>
// import { onMounted, onUnmounted, ref } from 'vue';
// let timeout = null
// let time = ref(3)
// onMounted(() => {
//   timeout = setInterval(() => {
//     if(time.value > 0) {
//       time.value--
//     }else {
//       window.close()
//       clearInterval(timeout)
//     }
//   }, 1000)
// })

// onUnmounted(() => {
//   clearTimeout(timeout)
// })
</script>
<style lang='less' scoped>
.logged {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}
</style>