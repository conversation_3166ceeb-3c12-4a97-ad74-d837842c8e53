import { createRouter, createWebHistory } from 'vue-router'
import { staticRoutes } from './menuRoutes'
import { getToken, removeUserInfo } from '@/utils/auth'
import { useUserStore } from '@/stores/user'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: staticRoutes
})

const Whitelist = ['/login', '/loggedTips', '/aihub', '/fifa', '/sphere', '/pbiEmbed', '/reportRecommend'] // 不重定向白名单

router.beforeEach(async (to) => {
  if (Whitelist.includes(to.path)) {
    return
  } else {
    if (getToken()) {
      if (to.path === '/login') {
        removeUserInfo()
        return
      } else {
        const user = useUserStore()
        if (user.routes) {
          // 有路由菜单，直接跳转
          return
        } else {
          user.setFeedbackSelect()
          // 没有路由，则需要重新请求路由菜单与数据权限
          await user.setRoutes()
          await user.setDataPermission()
          // 因无路由进入404页面后重定向一次，以解决vue-router4的warning
          if (to.name === '404') {
            return { path: to.path, query: to.query }
          } else {
            return { ...to, replace: true }
          }
        }
      }
    } else {
      if (Whitelist.includes(to.path)) {
        // 白名单路由直接跳转
        return
      } else {
        // 非白名单路由跳转登录
        if (location.href.indexOf('redirectUrl') < 0) {
          // 如果没跳转过登录，先跳转adfs登录
          const redirectUrl = encodeURIComponent(location.href)
          location.href = `${location.origin}/adfs/login?redirectUrl=${redirectUrl}`
          return false
        } else {
          // 如果无法进行adfs登录，则重定向到登录页
          return {
            path: '/login',
            query: { redirect: to.fullPath }
          }
        }
      }
    }
  }
})

export default router
