export const staticRoutes = [
  {
    path: '/',
    name: '首页',
    component: () => import('@/views/Home/index.vue')
    // component: () => import('@/views/Home/homeView.vue')
    // component: () => import('@/views/Blank/blankView.vue')
  },
  {
    path: '/settings',
    name: '设置',
    component: () => import('@/views/Settings/index.vue')
  },
  {
    path: '/chatNow',
    name: 'chatNow',
    component: () => import('@/views/ChatNow/chatNow.vue')
  },
  {
    path: '/chatPbi',
    name: 'chatPbi',
    component: () => import('@/views/ChatNow/chatPbi.vue')
  },
  {
    path: '/agentChat',
    name: 'agentChat',
    component: () => import('@/views/AgentChat/agentChatView.vue')
  },
  // 非本地环境不加载Login路由
  ...import.meta.env.VITE_USER_NODE_ENV === 'development' ? [{
    path: '/login',
    name: '登录',
    component: () => import('@/views/Login/loginView.vue')
  }] : [],
  {
    path: '/chat',
    name: '对话',
    component: () => import('@/views/Chat/chatView.vue')
  },
  {
    path: '/sphere',
    name: 'Sphere',
    component: () => import('@/views/AIHub/sphere.vue')
  },
  {
    path: '/pbiEmbed',
    name: 'PBI嵌入',
    component: () => import('@/views/Pbi/pbiEmbed.vue')
  },
  {
    path: '/reportRecommend',
    name: '报表推荐',
    component: () => import('@/views/Pbi/reportRecommend/index.vue')
  },
  {
    path: '/loggedTips',
    name: '登陆成功',
    component: () => import('@/views/Blank/loggedTip.vue')
  },
  { // 404白页面
    path: '/:catchAll(.*)',
    name: '404',
    component: () => import('@/views/Blank/blankView.vue')
  }
]

export const menuRoutes = [
  {
    path: '/prompt',
    name: '提示语',
    component: () => import('@/views/Prompt/promptView.vue')
  },
  {
    path: '/database',
    name: '数据源',
    component: () => import('@/views/Database/databaseView.vue')
  },
  {
    path: '/knowledge',
    name: '知识库',
    component: () => import('@/views/Knowledge/knowledgeView.vue')
  },
  {
    path: '/knowledge/detail',
    name: '数据源详情',
    component: () => import('@/views/Knowledge/knowledgeDetail.vue')
  },
  {
    path: '/plugin',
    name: '插件管理',
    component: () => import('@/views/Plugin/pluginView.vue')
  },
  {
    path: '/application',
    name: '应用管理',
    component: () => import('@/views/Application/appView.vue')
  },
  {
    path: '/user',
    name: '用户管理',
    component: () => import('@/views/User/userView.vue')
  },
  {
    path: '/role',
    name: '角色管理',
    component: () => import('@/views/Role/roleView.vue')
  },
  {
    path: '/menu',
    name: '菜单管理',
    component: () => import('@/views/Menu/menuView.vue')
  }
]
