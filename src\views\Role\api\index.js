import request from '@/utils/axios'

const url = '/'

function createNewRole(data) {
  return request({
    url: url + 'role/add',
    method: 'post',
    data,
    noInlayerData: true
  })
}

function updateRole(data) {
  return request({
    url: url + 'role/update',
    method: 'post',
    data,
    noInlayerData: true
  })
}

function getTableList(data) {
  return request({
    url: url + 'role/list',
    method: 'post',
    data
  })
}

function getMenuPermission() {
  return request({
    url: url + 'menu/all',
    method: 'get'
  })
}

function getDataPermission() {
  return request({
    url: url + 'data/all',
    method: 'get'
  })
}

function getAllPermission(id) {
  return request({
    url: url + 'role/permission/' + id,
    method: 'get'
  })
}

function deleteRole(data) {
  return request({
    url: url + 'role/batchDel',
    method: 'post',
    data
  })
}

function haveUser(data) {
  return request({
    url: url + 'role/haveUser',
    method: 'post',
    data
  })
}

function copyRole(data) {
  return request({
    url: url + 'role/copy',
    method: 'post',
    data,
    noInlayerData: true
  })
}

function savePermission(data) {
  return request({
    url: url + 'role/permission/save',
    method: 'post',
    data
  })
}

function exportRoles(data) {
  return request({
    url: url + 'role/export',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    noInlayerData: true
  })
}

export {
  createNewRole,
  updateRole,
  getTableList,
  getMenuPermission,
  getDataPermission,
  getAllPermission,
  copyRole,
  savePermission,
  deleteRole,
  exportRoles,
  haveUser
}
