@import "./iconfont/iconfont.css";

html, body {
  font-size: 14px;
  font-family: 'Helvetica Neue','PingFang SC','Hiragino Sans GB','Microsoft YaHei','微软雅黑',Arial,sans-serif;
  color: #505050;
}
html, body, ul, li, p, h1, h2, h3, h4, h5, h6 {
  margin: 0;
  padding: 0;
}
ul, li {
  list-style: none;
}
html {
  height: 100%;
  box-sizing: border-box;
  line-height: 1.15;
}
body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
}
a:focus, a:active {
  outline: none;
}
a, a:focus, a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}
*, *:before, *:after {
  box-sizing: inherit;
}
img {
  display: block;
}
iframe {
  display: block;
  border: none;
}
button {
  font-family: 'Helvetica Neue','PingFang SC','Hiragino Sans GB','Microsoft YaHei','微软雅黑',Aria<PERSON>,sans-serif;
}
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}
::-webkit-scrollbar-thumb {
  background-color: rgba(144,147,153,.5);
  border-radius: 10px;
}
svg:focus {
  outline: none;
}
.el-tabs__item:focus-visible {
  box-shadow: none !important;
}

/* 分页查询表格页面样式 */
.search-box {
  padding: 20px 0 10px;
  overflow: hidden;
}
.search-box>.el-input, .search-box>.el-select {
  float: left;
  /*width: 240px;*/
  width: 162px;
  margin: 0 12px 10px 0;
}
.search-btn-left {
  float: left;
  display: flex;
  margin-bottom: 10px;
}
.search-btn-right {
  float: right;
  display: flex;
  margin: 0 0 10px 12px;
}
.table-box {
  flex: 1;
}
.table-box-header {
  /*height: 55px;*/
  /*background: #e7ecf9 !important;*/
  /*border-right: 1px solid #e5e4e4 !important;*/
  font-size: 14px;
  font-weight: 600 !important;
  letter-spacing: 0.88px;
  height: 46px;
  color: #333433;
  background: #F2F6FE !important;
}
.table-box-btn {
  display: flex;
  flex-wrap: wrap;
}
.table-box-btn>.el-button {
  margin-right: 12px;
}
.table-box-btn>.el-button:last-child {
  margin-right: 0;
}
.table-box-btn>.el-button+.el-button {
  margin-left: 0;
}
.pagination-box {
  margin: 20px 0;
  justify-content: flex-end;
}
/* 配置弹窗样式 */
.setting-page-dialog-wrap .el-dialog__header {
  position: relative;
  border-bottom: 1px solid #ccc;
  margin-right: 0;
  height: 30px;
  display: flex;
  align-items: center;
  padding-bottom: 20px;
}

.setting-page-dialog-wrap .el-dialog__header::before {
  position: absolute;
  top: 13px;
  left: 11px;
  content: '';
  width: 4px;
  height: 14px;
  background-color: #568CF4;
  border-radius: 2px;
}

.setting-page-dialog-wrap .el-dialog__header .el-dialog__title {
  font-size: 14px;
  color: #131313;
}

.setting-page-dialog-wrap .el-dialog__header .el-dialog__headerbtn {
  width: 30px;
  height: 30px;
  top: 8px;
  right: 3px;
}