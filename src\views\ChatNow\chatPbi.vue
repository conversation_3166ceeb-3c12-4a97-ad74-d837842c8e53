<script setup>
import {
  Search,
  ChatDotRound,
  Coin,
  Collection,
  Files,
  Operation,
  House,
  User,
  Menu
} from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import { ref, computed, reactive, watch } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { copyToClipboard } from '@/utils/util'
import { useUserStore } from '@/stores/user'
import { useI18n } from 'vue-i18n'
import * as api from './api'

import topIcon from './icons/topIcon.vue'
import unpinIcon from './icons/unpinIcon.vue'
import ChatMain from './chat/chatMain.vue'
import renameIcon from './icons/renameIcon.vue'
import deleteIcon from './icons/deleteIcon.vue'

const router = useRouter()
const route = useRoute()
const user = useUserStore()
const { locale, t } = useI18n()

// 搜索名称
const searchInput = ref('')
// 筛选日期
const searchDate = ref(null)
// 日期快捷选项
const shortcuts = computed(() => [
  {
    text: t('LEFT_BAR.LAST_1_DAY'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)
      return [start, end]
    }
  },
  {
    text: t('LEFT_BAR.LAST_7_DAYS'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: t('LEFT_BAR.LAST_30_DAYS'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  }
])

const getLocalStorageId = () => localStorage.getItem('currentId')

// 滚动栏实例
const scrollbarRef = ref()
let searchTimer = null
let immediate = true
// 获取聊天列表(函数防抖 延时0.5秒 立即执行)
const setChatList = (pageNum) => {
  clearTimeout(searchTimer)
  if (immediate) {
    user.setChatList(pageNum, {
      chatMode: route.query.scene,
      selectParam: route.query.scene === 'chat_with_scene' ? route.query.select : '',
      userInput: searchInput.value,
      startDate: searchDate.value ? searchDate.value[0] : '',
      endDate: searchDate.value ? searchDate.value[1] : ''
    }).then(() => {
      scrollbarRef.value && scrollbarRef.value.setScrollTop(0)
    })
    immediate = false
    searchTimer = setTimeout(() => {
      immediate = true
    }, 500)
  } else {
    searchTimer = setTimeout(() => {
      user.setChatList(pageNum, {
        chatMode: route.query.scene,
        selectParam: route.query.scene === 'chat_with_scene' ? route.query.select : '',
        userInput: searchInput.value,
        startDate: searchDate.value ? searchDate.value[0] : '',
        endDate: searchDate.value ? searchDate.value[1] : ''
      }).then(() => {
        scrollbarRef.value && scrollbarRef.value.setScrollTop(0)
      })
      immediate = true
    }, 500)
  }
}
const handleCurrentChange = (pageNum) => {
  setChatList(pageNum)
}

// 复制分享链接
const copyUrl = (item) => {
  const url = `${location.origin}/chatNow?scene=${item.chatMode}&select=${item.selectParam}&id=${item.convUid}`
  copyToClipboard(url)
}

// 置顶
const toTop = async (item) => {
  await api.toTop({
    convUid: item.convUid,
    toTop: item.toTop === '1' ? '0' : '1'
  })
  ElMessage.success(`${item.toTop === '1' ? t('LEFT_BAR.UNPIN') : t('LEFT_BAR.PIN')} ${t('COMMON.SUCCESS')}`)
  setChatList(1)
}

// 删除对话
const deleteChat = (convUid) => {
  ElMessageBox.confirm(
    t('COMMON.DELETE_CONFIRM'),
    t('COMMON.DELETE'),
    { type: 'warning', closeOnClickModal: false }
  ).then(async () => {
    await api.deleteChat({ convUid })
    ElMessage.success(t('COMMON.DELETE_SUCCESS'))
    let pageNum = user.chatPage.pageNum
    if (pageNum !== 1 && user.chatList.length === 1) {
      pageNum--
    }
    if (route.query.id === convUid || getLocalStorageId() === convUid) {
      if(route.query.scene === 'chat_with_scene') {
        router.push(`/chatNow?scene=${route.query.scene}&select=${route.query.select}&model=${route.query.model}`)
        if(!route.query.id) router.go(0)
      }else if(route.query.scene === 'chat_knowledge' || route.query.scene === 'chat_with_data') {
        router.push(`/chatNow?scene=${route.query.scene}`)
        if(!route.query.id) router.go(0)
      }
    }else {
      setChatList(pageNum)
    }
  })
}
const currentRenameChatInfo = reactive({
  convUid: '',
  modelName: '',
  selectParam: '',
  userInput: ''
})
const renameDialogFormVisible = ref(false)
const renameForm = reactive({
  name: ''
})
// 重命名聊天历史
const renameChat = (data) => {
  renameForm.name = data.userInput

  currentRenameChatInfo.convUid = data.convUid
  currentRenameChatInfo.modelName = data.modelName
  currentRenameChatInfo.selectParam = data.selectParam
  // currentRenameChatInfo.userInput = data.userInput
  // currentRenameChatInfo.createdBy = data.createdBy

  renameDialogFormVisible.value = true
}
const handleRenameSubmit = async() => {
  if(renameForm.name.trim() === '') {
    ElMessage.error('重命名内容不能为空!')
    return
  }
  currentRenameChatInfo.userInput = renameForm.name
  try {
    const res = await api.renameChat({
      ...currentRenameChatInfo
    })
    ElMessage.success(t('CHAT.RENAME_SUCCESS'))
    let pageNum = user.chatPage.pageNum
    setChatList(pageNum)
    renameDialogFormVisible.value = false
  } catch (error) {
    ElMessage.error(error)
  }

}
const handleRenameCancle = () => {
  currentRenameChatInfo.convUid = ''
  currentRenameChatInfo.modelName = ''
  currentRenameChatInfo.selectParam = ''
  currentRenameChatInfo.userInput = ''
  currentRenameChatInfo.createdBy = ''
  renameDialogFormVisible.value = false
}
// 下拉按钮
const handleCommand = (command, item) => {
  switch (command) {
    // case 'share':
    //   copyUrl(item)
    //   break
    case 'toTop':
      toTop(item)
      break
    case 'rename':
      renameChat(item)
      break
    case 'delete':
      deleteChat(item.convUid)
      break
    default:
      break
  }
}

// 左下全部菜单
const allMenuList = computed(() => [
  { icon: ChatDotRound, text: t('MENUS.PROMPT'), path: '/prompt' },
  { icon: Coin, text: t('MENUS.DATA_SOURCE'), path: '/database' },
  { icon: Collection, text: t('MENUS.KNOWLEDGE'), path: '/knowledge' },
  { icon: Files, text: t('MENUS.PLUGIN'), path: '/plugin' },
  { icon: Operation, text: t('MENUS.APP'), path: '/application' },
  { icon: House, text: t('MENUS.USER'), path: '/user' },
  { icon: User, text: t('MENUS.ROLE'), path: '/role' },
  { icon: Menu, text: t('MENUS.MENU'), path: '/menu' },
])
// 左下实际菜单
const menuList = computed(() => {
  return allMenuList.value.filter(menu => {
    return user.routes.find(item => menu.path.indexOf(item.menuPath) > -1)
  })
})

setChatList()

const jumpNewChat = () => {
  if(route.query.scene === 'chat_with_scene' && !route.query.id) {
    router.push(`/chatPbi?scene=${route.query.scene}&select=${route.query.select}&model=${route.query.model}`)
    router.go(0)
  }else if(route.query.scene === 'chat_with_scene' && route.query.id) {
    router.push(`/chatPbi?scene=${route.query.scene}&select=${route.query.select}&model=${route.query.model}`)
  }
}

watch(() => route.query.id,
  (val) => {
    localStorage.setItem('currentId', val)
  },
  {
    immediate: true
  }
)
</script>

<template>
  <div class="agent-chat-main">
    <div class="left-bar">
      <div class="top-btn">
        <!-- <button class="newchat-btn" @click="router.push('/')"> -->
        <button class="newchat-btn" @click="jumpNewChat">
          <i class="iconfont icon-circlePlus"></i>
          <span>
              {{
              $t('LEFT_BAR.NEW_CHAT')
            }}
            </span>
        </button>
        <el-input v-model="searchInput" :placeholder="$t('LEFT_BAR.SEARCH_HISTORY')" :prefix-icon="Search" clearable
                  @input="setChatList(1)" />
        <el-date-picker v-model="searchDate" type="daterange" unlink-panels :range-separator="$t('LEFT_BAR.TO')"
                        :start-placeholder="$t('LEFT_BAR.START_DATE')" :end-placeholder="$t('LEFT_BAR.END_DATE')"
                        value-format="YYYY-MM-DD" :shortcuts="shortcuts" @change="setChatList(1)" />
      </div>
      <el-scrollbar ref="scrollbarRef" class="middle-chat" v-loading="user.chatLoading">
        <ul class="chat-list">
          <li v-for="item in user.chatList" :key="item.convUid" class="chat-item" :class="{
            isTop: item.toTop === '1',
            active: route.query.id === item.convUid || getLocalStorageId() === item.convUid
          }" @click="router.push({
            path: '/chatPbi',
            query: {
              scene: item.chatMode,
              select: item.selectParam,
              id: item.convUid,
              model: item.modelName
            }
          })">
            <!-- <ChatDotRound class="chat-item-icon" /> -->
            <div class="left-icon chat-item-icon">
              <i class="iconfont icon-duihua2"></i>
            </div>
            <div class="chat-item-title">
              <p class="top-title">{{ item.userInput }}</p>
              <p class="bottom-time">{{ item.updatedTime.substr(0, 10) }}</p>
            </div>
            <div class="chat-item-more-wrap" @click.stop>
              <el-dropdown trigger="click" @command="(command) => handleCommand(command, item)">
                <MoreFilled class="chat-item-more" />
                <template #dropdown>
                  <el-dropdown-menu class="chat-item-more-drop">
                    <!-- <el-dropdown-item :icon="Share" command="share">
                      {{ $t('LEFT_BAR.SHARE') }}
                    </el-dropdown-item> -->
                    <!-- <el-dropdown-item :icon="item.toTop === '1' ? Minus : Top" command="toTop">
                      {{ item.toTop === '1' ? $t('LEFT_BAR.UNPIN') : $t('LEFT_BAR.PIN') }}
                    </el-dropdown-item> -->
                    <el-dropdown-item :icon="item.toTop === '1' ? unpinIcon : topIcon" command="toTop">
                      {{ item.toTop === '1' ? $t('LEFT_BAR.UNPIN') : $t('LEFT_BAR.PIN') }}
                    </el-dropdown-item>
                    <el-dropdown-item :icon="renameIcon" command="rename">
                      {{ $t('COMMON.RENAME') }}
                    </el-dropdown-item>
                    <el-dropdown-item :icon="deleteIcon" command="delete">
                      {{ $t('COMMON.DELETE') }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </li>
        </ul>
        <el-empty v-if="user.chatList.length === 0" :image-size="50" />
      </el-scrollbar>
      <el-pagination :current-page="user.chatPage.pageNum" :page-size="user.chatPage.pageSize"
                     :total="user.chatPage.pageTotal" :pager-count="5" layout="prev, pager, next"
                     @current-change="handleCurrentChange" />


    </div>
    <div class="chat-window">
      <!-- <div class="chat-title"> -->
      <chat-main />
    </div>

    <el-dialog v-model="renameDialogFormVisible" :close-on-click-modal="false" :show-close="false" :title="$t('COMMON.RENAME')" width="500">
      <el-form :model="renameForm" @submit.native.prevent>
        <el-form-item :label="$t('CHAT.NEW_NAME')" label-width="70">
          <el-input v-model="renameForm.name" autocomplete="off" @keyup.enter.native="handleRenameSubmit" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleRenameCancle">{{ $t('COMMON.CANCEL') }}</el-button>
          <el-button type="primary" @click="handleRenameSubmit">
            {{ $t('COMMON.SUBMIT') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
// @import url('//at.alicdn.com/t/c/font_4728306_ekwp8yphsul.css');

// .datasource-select,
// .tipword-select {
//   width: 182px;
// }
.chat-item-more-drop {
  padding: 7px;

  :deep(li) {
    font-size: 12px;
    color: #0A121F;
  }

  :deep(li:hover) {
    border-radius: 4px;
    background: #F0F5FF;
  }
}

.agent-chat-main {
  display: flex;
  height: 100%;
  padding: 0 20px 20px 0;
}

.left-bar {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  // width: 240px;
  width: 265px;
  border-radius: 12px;
  background-color: #fff;
  // height: calc(100vh - 50px);
  // padding-bottom: 68px;

  .newchat-btn {
    width: 100%;
    height: 32px;
    border: 2px solid transparent;
    color: #568CF4;
    cursor: pointer;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 10px;
    position: relative;
    background: linear-gradient(#fff, #fff) padding-box, linear-gradient(92deg, rgba(104, 67, 224, 1), rgba(86, 140, 244, 1), rgba(82, 211, 240, 1)) border-box;

    span,
    i {
      font-weight: bold;
      font-size: 14px;
    }

    i {
      margin-right: 6px;
    }
  }

  .top-btn {
    padding: 17px 12px 16px;

    // .el-button {
    //   width: 100%;
    //   height: 44px;
    //   font-size: 16px;
    //   margin-bottom: 8px;
    // }
    :deep(.el-input__wrapper) {
      border-radius: 2px;
    }

    :deep(.el-date-editor) {
      width: 100%;
      margin-top: 10px;
    }
  }

  :deep(.el-input__inner::placeholder),
  :deep(.el-range-input) {
    font-size: 12px;
  }

  .middle-chat {
    flex: 1;

    .chat-list {

      .chat-item {
        position: relative;
        display: flex;
        // align-items: center;
        // margin: 8px 0;
        padding: 0 8px;
        height: 60px;
        // border-radius: 4px;
        cursor: pointer;
        // transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
        // transition-timing-function: cubic-bezier(.4, 0, .2, 1);
        // transition-duration: .15s;
        margin-top: 4px;

        .chat-item-icon {
          position: absolute;
          top: 9px;
          left: 12px;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 24px;
          height: 24px;
          border-radius: 12px;
          background: linear-gradient(180deg, #5490F5 0%, #45B1FE 100%);

          i {
            color: #E9F4FF;
            font-size: 16px;
          }
        }

        .chat-item-title {
          flex: 1;
          margin: 12px 30px 11px 35px;
          white-space: nowrap;
          overflow: hidden;

          // line-height: 1.5;
          .top-title {
            font-size: 14px;
            color: #0A121F;
            min-height: 16px;
          }

          .bottom-time {
            padding-top: 10px;
            font-size: 12px;
            color: #4B556A;
            line-height: 12px;
          }
        }

        .chat-item-more-wrap {
          position: absolute;
          top: 4px;
          right: 14px;
        }

        .chat-item-more {
          width: 16px;
          height: 16px;
        }
      }

      .chat-item.isTop {
        background: #e7ecf9;

        // .chat-item-icon {
        //   background: linear-gradient(180deg, #34C057 0%, #63E88F 100%);
        // }
      }

      .chat-item:hover {
        background-color: #F0F5FF;

        // .chat-item-more {
        //   opacity: 1;
        // }
      }

      .chat-item.active {
        background-color: #F0F5FF;
      }
    }
  }

  :deep(.el-pagination) {
    // border-top: 1px solid #e5e7eb;
    padding: 2px 0;
    justify-content: center;

    .el-pager li {
      min-width: 28px;
    }
  }

  .bottom-menu {
    border-top: 1px solid #e5e7eb;
    padding: 8px 0;

    .menu-list {
      padding: 0 8px;

      .menu-item {
        display: flex;
        align-items: center;
        margin: 8px 0;
        padding: 0 8px;
        height: 32px;
        border-radius: 4px;
        cursor: pointer;
        transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
        transition-timing-function: cubic-bezier(.4, 0, .2, 1);
        transition-duration: .15s;

        .menu-item-icon {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }
      }

      .menu-item:hover {
        background-color: #e7ecf9;
      }

      .menu-item.active {
        background-color: #e7ecf9;
      }
    }
  }

  .language-button {
    position: absolute;
    bottom: 25px;
    right: 25px;
    width: 40px;
    height: 40px;

    .el-button {
      font-size: 18px;
      width: 40px;
      height: 40px;
      box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    }
  }
}

.chat-window {
  display: flex;
  flex-direction: column;
  // width: calc(100% - 240px);
  width: calc(100% - 265px);
  height: 100%;
  margin-left: 16px;
  background-color: #fff;
  border-radius: 12px;
}
</style>
