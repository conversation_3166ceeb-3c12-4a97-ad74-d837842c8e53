<script setup>
import { Plus, ChatDotRound, Tools } from '@element-plus/icons-vue'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import addDialog from './addDialog.vue'
import argumentDialog from './argumentDialog.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import * as api from './api'

import { useI18n } from 'vue-i18n'
const { t } = useI18n()

const router = useRouter()

// 所选知识库ID
const knowledgeId = ref(null)
// 添加对话框组件实例
const addDialogRef = ref()
// 弹出数据合规提示
const openDataAlert = () => {
  ElMessageBox.alert(
    t('KNOWLEDGE.DATA_SAFE_CONTENT'),
    t('KNOWLEDGE.DATA_SAFE'),
    { type: 'warning', closeOnClickModal: false, customClass: 'data-alert', dangerouslyUseHTMLString: true }
  ).then(() => {
    openAddDialog()
  })
}
// 打开添加对话框
const openAddDialog = (id) => {
  knowledgeId.value = id || null
  addDialogRef.value.dialogVisible = true
}

const formatTime = (time) => {
  return time ? time.replace('T', ' ').substring(0, 19) : ''
}
// 知识库列表
const knowledgeList = ref([])
// 获取知识库列表
const getKnowledgeList = async () => {
  const res = await api.getKnowledgeList()
  knowledgeList.value = res || []
}
// 获取数据源列表
const getDataSourceList = async (item) => {
  const res = await api.getDataSourceList(item.id, {})
  item.dataSource = res || []
}

// 删除知识库
const deleteKnowledge = (id) => {
  ElMessageBox.confirm(
    t('KNOWLEDGE.SURE_DELETE_KNOW'),
    t('KNOWLEDGE.DELETE_KNOW'),
    { type: 'warning', closeOnClickModal: false }
  ).then(async () => {
    await api.deleteKnowledge({ id })
    ElMessage.success(t('COMMON.DELETE_SUCCESS'))
    getKnowledgeList()
  })
}

// 参数对话框实例
const argumentDialogRef = ref()
// 打开参数对话框
const openArgumentDialog = (id) => {
  knowledgeId.value = id || null
  argumentDialogRef.value.dialogVisible = true
}

// 同步数据源
const syncDataSource = async (item, id) => {
  await api.syncDataSource(item.id, { docIds: [id] })
  ElMessage.success(t('DATA_SOURCE.UpdateSuccess'))
  getDataSourceList(item)
}
// 删除数据源
const deleteDataSource = (knId, docId) => {
  ElMessageBox.confirm(
    t('KNOWLEDGE.SURE_DELETE_DATA_SOURCE'),
    t('KNOWLEDGE.DELETE_DATA_SOURCE'),
    { type: 'warning', closeOnClickModal: false }
  ).then(async () => {
    await api.deleteDataSource(knId, { id: docId })
    ElMessage.success(t('COMMON.DELETE_SUCCESS'))
    getKnowledgeList()
  })
}

getKnowledgeList()
</script>

<template>
  <div class="knowledge-view">
    <div class="create-btn">
      <el-button
        type="primary"
        :icon="Plus"
        @click="openDataAlert()"
      >{{ $t('KNOWLEDGE.CREATE') }}</el-button>
    </div>
    <el-scrollbar>
      <ul class="knowledge-list">
        <el-popover
          v-for="item in knowledgeList"
          :key="item.id"
          placement="bottom"
          :width="800"
          trigger="click"
          @before-enter="getDataSourceList(item)"
        >
          <template #reference>
            <li class="knowledge-item isReal">
              <div class="knowledge-item-top">
                <Connection class="knowledge-icon" />
                <div class="knowledge-title">{{ item.name }}</div>
                <Delete
                  v-if="item.delAuth"
                  class="knowledge-delete"
                  @click.stop="deleteKnowledge(item.id)"
                />
              </div>
              <div class="knowledge-item-content">
                <p class="content-title">{{ $t('KNOWLEDGE.CREATOR') }}:</p>
                <p class="content-text">{{ item.owner }}</p>
                <p class="content-title">{{ $t('KNOWLEDGE.DESCRIPTION') }}:</p>
                <p class="content-text">{{ item.desc }}</p>
                <p class="content-title">{{ $t('KNOWLEDGE.LAST_TIEM') }}:</p>
                <p class="content-text">{{ formatTime(item.gmtModified) }}</p>
              </div>
              <div class="knowledge-item-chat">
                <el-button
                  class="chat-button"
                  :icon="ChatDotRound"
                  round
                  @click.stop="router.push({
                    path: '/chatNow',
                    query: {
                      scene: 'chat_knowledge',
                      select: item.name
                    }
                  })"
                >{{ $t('KNOWLEDGE.CONVERSATION') }}</el-button>
              </div>
              <div v-if="item.docs" class="knowledge-red-number">{{ item.docs }}</div>
            </li>
          </template>
          <div class="popover-container">
            <div v-if="item.editAuth" class="popover-top-btn">
              <el-button
                type="primary"
                :icon="Plus"
                @click="openAddDialog(item.id)"
              >{{ $t('KNOWLEDGE.ADD_KNOW') }}</el-button>
              <el-button
                :icon="Tools"
                @click="openArgumentDialog(item.id)"
              >{{ $t('KNOWLEDGE.PARAMS') }}</el-button>
            </div>
            <el-divider v-if="item.editAuth" />
            <el-scrollbar v-if="item.dataSource && item.dataSource.length" max-height="400px">
              <el-row :gutter="20">
                <el-col v-for="data in item.dataSource" :key="data.id" :span="8">
                  <div class="popover-source-item">
                    <div class="source-item-head">
                      <el-tooltip :content="data.docName" placement="top">
                        <div class="source-item-title">
                          <Document class="title-icon" />
                          <div class="title-text">{{ data.docName }}</div>
                        </div>
                      </el-tooltip>
                      <div class="source-item-btns">
                        <el-tooltip :content="$t('KNOWLEDGE.DETAIL')" placement="top">
                          <View class="btns-icon" @click="router.push({
                            path: '/knowledge/detail',
                            query: {
                              knId: item.id,
                              knName: item.name,
                              docId: data.id
                            }
                          })" />
                        </el-tooltip>
                        <el-tooltip v-if="item.editAuth && data.docType === 'TEXT'" :content="$t('KNOWLEDGE.SYNC')" placement="top">
                          <Refresh class="btns-icon" @click="syncDataSource(item, data.id)" />
                        </el-tooltip>
                        <el-tooltip v-if="item.editAuth" :content="$t('KNOWLEDGE.DELETE')" placement="top">
                          <Delete class="btns-icon" style="color: red" @click="deleteDataSource(item.id, data.id)" />
                        </el-tooltip>
                      </div>
                    </div>
                    <div class="source-item-content">
                      <p class="content-title">{{ $t('KNOWLEDGE.CHUNKING') }}:</p>
                      <p class="content-text">{{ data.chunkSize }} chunks</p>
                      <p class="content-title">{{ $t('KNOWLEDGE.LAST_SYNC_TIME') }}:</p>
                      <p class="content-text">{{ formatTime(data.lastSync) }}</p>
                      <!-- <el-tooltip :content="data.result" placement="top"> -->
                      <el-tag v-if="data.status === 'TODO'" type="warning" effect="light">{{ data.status }}</el-tag>
                      <el-tag v-if="data.status === 'RUNNING'" effect="dark">{{ data.status }}</el-tag>
                      <el-tag v-if="data.status === 'FINISHED'" type="success" effect="dark">{{ data.status }}</el-tag>
                      <el-tag v-if="data.status === 'FAILED'" type="danger" effect="dark">{{ data.status }}</el-tag>
                      <!-- </el-tooltip> -->
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-scrollbar>
            <el-empty v-else description="No Data" />
          </div>
        </el-popover>
        <!-- 占位元素 -->
        <li v-for="item in 10" :key="item" class="knowledge-item"></li>
      </ul>
    </el-scrollbar>
    <add-dialog
      ref="addDialogRef"
      :knowledgeId="knowledgeId"
      @getKnowledgeList="getKnowledgeList"
    />
    <argument-dialog
      ref="argumentDialogRef"
      :knowledgeId="knowledgeId"
    />
  </div>
</template>

<style lang="less" scoped>
.knowledge-view {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100%;
  min-height: 0;
  height: 100%;
  padding: 20px 0;
  background: #fafafa;
  .create-btn {
    padding-left: 48px;
    padding-bottom: 10px;
  }
  .knowledge-list {
    display: flex;
    flex-wrap: wrap;
    padding: 0 38px 24px;
    .knowledge-item {
      flex: 1;
      min-width: 300px;
      margin: 0 10px;
    }
    .knowledge-item.isReal {
      position: relative;
      margin: 10px;
      border: 1px solid #e5e7eb;
      border-radius: 10px;
      background: #ffffff;
      cursor: pointer;
      transition-property: all;
      transition-timing-function: cubic-bezier(.4,0,.2,1);
      transition-duration: .15s;
      .knowledge-item-top {
        display: flex;
        align-items: center;
        margin: 12px 24px 0;
        .knowledge-icon {
          width: 30px;
          height: 30px;
          color: #409eff;
        }
        .knowledge-title {
          flex: 1;
          margin: 0 8px;
          color: #000000;
          line-height: 24px;
          font-size: 18px;
          font-weight: bold;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .knowledge-delete {
          width: 20px;
          height: 20px;
          color: red;
        }
      }
      &:hover {
        box-shadow: 0 2px 4px rgba(0, 0, 0, .1);
      }
      .knowledge-item-content {
        padding: 8px 24px 16px;
        line-height: 20px;
        .content-title {
          font-weight: bold;
          margin-top: 8px;
        }
        .content-text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .knowledge-item-chat {
        display: flex;
        justify-content: center;
        padding-bottom: 12px;
      }
      .knowledge-red-number {
        position: absolute;
        top: 0;
        inset-inline-end: 0;
        transform: translate(50%, -50%);
        transform-origin: 100% 0%;
        z-index: 10;
        min-width: 20px;
        height: 20px;
        color: #ffffff;
        font-size: 12px;
        line-height: 20px;
        white-space: nowrap;
        text-align: center;
        background: #ff4d4f;
        border-radius: 10px;
        box-shadow: 0 0 0 1px #ffffff;
      }
    }
  }
}
.popover-container {
  padding: 8px 16px 0;
  .popover-top-btn {
    display: flex;
  }
  .el-scrollbar {
    margin: 0 -10px;
  }
  .el-row {
    margin: -10px 0 0 !important;
    .el-col {
      padding: 10px;
      .popover-source-item {
        border: 1px solid #e5e7eb;
        border-radius: 10px;
        cursor: pointer;
        .source-item-head {
          display: flex;
          align-items: center;
          height: 56px;
          padding: 0 24px;
          color: #000000;
          font-weight: bold;
          font-size: 16px;
          border-bottom: 1px solid #f0f0f0;
          .source-item-title {
            display: flex;
            align-items: center;
            flex: 1;
            overflow: hidden;
            .title-icon {
              width: 18px;
              height: 18px;
              margin-right: 8px;
              color: #409eff;
            }
            .title-text {
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
          .source-item-btns {
            display: flex;
            align-items: center;
            .btns-icon {
              width: 18px;
              height: 18px;
              margin-left: 8px;
              color: #409eff;
            }
          }
        }
        .source-item-content {
          padding: 16px 24px;
          color: #505050;
          .content-title {
            font-weight: bold;
            margin-top: 8px;
          }
          .content-text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .el-tag {
            margin: 8px 0;
          }
        }
      }
    }
  }
}
</style>

<style lang="less">
.data-alert {
  max-width: 740px;
  .el-message-box__title {
    padding-left: 28px;
  }
  .el-message-box__status {
    top: -28px;
    left: 0;
  }
}
</style>
