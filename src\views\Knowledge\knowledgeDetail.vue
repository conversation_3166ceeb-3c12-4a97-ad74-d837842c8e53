<script setup>
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import * as api from './api'

import { useI18n } from 'vue-i18n'
const { t } = useI18n()

const router = useRouter()
const route = useRoute()

const chunkLoading = ref(false)
const chunkList = ref([])
const getChunkList = async () => {
  chunkLoading.value = true
  try {
    const res = await api.getChunkList(route.query.knId, {
      documentId: route.query.docId
    })
    chunkList.value = res || []
    chunkLoading.value = false
  } catch(e) {
    chunkLoading.value = false
  }
}

getChunkList()
</script>

<template>
  <div class="knowledge-detail" v-loading="chunkLoading">
    <div class="knowledge-detail-nav">
      <div class="back-btn" @click="router.push('/knowledge')">Knowledge</div>
      <div class="separator">/</div>
      <div class="detail-name">{{ route.query.knName }}</div>
    </div>
    <el-scrollbar v-if="chunkList.length">
      <div v-for="item in chunkList" :key="item.id" class="knowledge-detail-item">
        <div class="item-head">
          <Memo class="item-head-icon" />
          <span>{{ item.docName }}</span>
        </div>
        <div class="item-content">
          <p class="item-content-title">{{ $t('KNOWLEDGE.CONTENT') }}:</p>
          <p>{{ item.content }}</p>
          <p class="item-content-title">{{ $t('KNOWLEDGE.METADATA') }}:</p>
          <p>{{ item.metaInfo }}</p>
        </div>
      </div>
    </el-scrollbar>
    <el-empty v-else description="No Data" />
  </div>
</template>

<style lang="less" scoped>
.knowledge-detail {
  display: flex;
  flex-direction: column;
  height: 100%;
  line-height: 1.5;
  .knowledge-detail-nav {
    margin: 24px;
    display: flex;
    align-items: center;
    color: rgba(0, 0, 0, .88);
    .back-btn {
      color: rgba(0, 0, 0, 0.45);
      transition: color 0.2s;
      padding: 0 4px;
      border-radius: 4px;
      cursor: pointer;
      &:hover {
        color: rgba(0, 0, 0, 0.88);
        background-color: rgba(0, 0, 0, 0.06);
      }
    }
    .separator {
      margin: 0 8px;
      color: rgba(0, 0, 0, 0.45);
    }
  }
  .el-scrollbar {
    flex: 1;
  }
  .knowledge-detail-item {
    border-top: 1px solid #f0f0f0;
    border-bottom: 1px solid #f0f0f0;
    color: #000000;
    .item-head {
      display: flex;
      align-items: center;
      height: 56px;
      padding: 0 24px;
      font-size: 16px;
      border-bottom: 1px solid #f0f0f0;
      .item-head-icon {
        width: 18px;
        height: 18px;
        margin-right: 8px;
        color: #409eff;
      }
    }
    .item-content {
      padding: 24px;
      .item-content-title {
        font-weight: bold;
      }
    }
  }
}
</style>
