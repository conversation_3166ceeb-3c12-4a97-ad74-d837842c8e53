<script setup>
import { ref, nextTick, onMounted } from 'vue'
import {useRoute} from 'vue-router'
import * as pbi from 'powerbi-client'
import 'powerbi-report-authoring'
import * as api from './api'

import markdownRender from '@/components/MarkdownRender/markdownRender.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const route = useRoute()

// let props = defineProps({
//   chartData: Object
// })
let chartData = ref({})
let pbiVisualData = ref({})
let changeVisualType = ref('tableEx')
let pbiDomId = ref('')
let reportData = ref({})
let embedConfig = ref({
  id: '',
  accessToken: '',
  type: 'report',
  embedUrl: 'https://app.powerbi.cn/reportEmbed',
  tokenType: pbi.models.TokenType.Aad,
  permissions: pbi.models.Permissions.All,
  pageName: 'pbi-chat',
  settings: {
    navContentPaneEnabled: false, // 底部页面导航窗格
    filterPaneEnabled: false // 筛选器窗格
  }
})

const showThinkContent = ref(true)
const showDivider = ref(false)

onMounted(() => {
  getPBIEmbedData()
})
let showErrorTips = ref(false)

const getPBIEmbedData = async () => {
  let id = route.query?.convId || 'c18894f5-430b-4cc9-8aed-f1c94487e34c'
  let res = await api.getPbiEmbedData(id)
  // console.log(res, '获取的数据')
  chartData.value = res
  initConfig()
  // 模拟流式输出
  streamAiText()
  // await nextTick()
  // createPbiVisual()
}

const initConfig = () => {
  pbiDomId.value = chartData.value.requestId
  pbiVisualData.value = chartData.value.pbiVisualParams
  changeVisualType.value = aiPbiType(chartData.value.userInput)
  embedConfig.value.id = pbiVisualData.value.reportId
  // embedConfig.value.id = '4b3b113e-ff99-4a4c-942c-38ed044dc3e4'
  embedConfig.value.accessToken = pbiVisualData.value.accessToken

  reportData.value = {
    instance: null,
    report: null,
    id: chartData.value.pbiVisualParams.reportId,
    token: chartData.value.pbiVisualParams.accessToken
  }
}
const createPbiVisual = async () => {
  reportData.value.instance = new pbi.service.Service(pbi.factories.hpmFactory, pbi.factories.wpmpFactory, pbi.factories.routerFactory)
  // let pbiDom = document.getElementById('pbiDom')
  let pbiDom = document.getElementById(chartData.value.requestId)
  // console.log(embedConfig, '配置项')
  reportData.value.report = reportData.value.instance.embed(pbiDom, embedConfig.value)

  reportData.value.report.on('loaded', async function() {
    // showThinkContent.value = true
    // console.log('报表加载完成')
    // let visualType = pbiVisualData.value.createParams.visualType
    let visualField = pbiVisualData.value.createParams.visualValue
    let visualFilter = pbiVisualData.value.filterParams
    let pageName = 'chatPbi'
    let authoringPage = await reportData.value.report.addPage(pageName)
    await authoringPage.setActive()
    let visualLayout = {
      x: 0,
      y: 0,
      z: 0,
      width: 1000,
      height: 300,
      displayState: {
        mode: 0
      }
    }
    // let res = await authoringPage.createVisual(visualType, visualLayout)
    let res = await authoringPage.createVisual('tableEx', visualLayout)
    let visual = res.visual
    // console.log(visual, '创建的视觉对象')
    // await Promise.all(visualField.forEach(item =>
    //   visual.addDataField(item.dataRoles, item.dataField)
    // ));
    visualField.forEach(item => {
      visual.addDataField(JSON.parse(JSON.stringify(item.dataRoles)), JSON.parse(JSON.stringify(item.dataField)))
    })
    if (visualFilter.length > 0) {
      let filters = []
      visualFilter.forEach(item => {
        item.filterValue.filterType = transformModelType(item.filterValue.filterType)
        // console.log(JSON.parse(JSON.stringify(item.filterValue.values)), '测')
        // let filter = {
        //   $schema: item.filterValue.schema,
        //   target: {
        //     table: item.filterValue.target.table,
        //     column: item.filterValue.target.column
        //   },
        //   filterType: transformModelType(item.filterValue.filterType),
        //   // filterType: pbi.models.FilterType.Basic
        //   operator: item.filterValue.operator,
        //   values: item.filterValue.values
        // }
        filters.push(JSON.parse(JSON.stringify(item.filterValue)))
      })
      await visual.updateFilters(pbi.models.FiltersOperations.Add, filters)
    }

    // let filter = {
    //   $schema: 'http://powerbi.com/product/schema#basic',
    //   target: {
    //     table: 'leads_final',
    //     column: '客户分级final'
    //   },
    //   filterType: pbi.models.FilterType.BasicFilter,
    //   operator: 'In',
    //   values: ['B2', 'B3']
    // }
    // await visual.updateFilters(pbi.models.FiltersOperations.Add, [filter])
    await visual.changeType(changeVisualType.value)
    let settings = {
      layoutType: pbi.models.LayoutType.Custom,
      customLayout: {
        pageSize: {
          type: pbi.models.PageSizeType.Custom,
          width: 1000,
          height: 300
        },
        displayOption: pbi.models.DisplayOption.FitToWidth
      }
    }
    reportData.value.report.updateSettings(settings)
    // Defining data fields
    // const regionColumn = { measure: 'ASTI.CA', table: '度量值表'};
    // const totalUnitsMeasure = { column: 'Bu_Level2', table: 'Dim_BU'};
    // let roles = await visual.getCapabilities()
    // console.log(roles, 'roles角色')
    // Adding visual data fields
    // await visual.addDataField('Category', totalUnitsMeasure);
    // await visual.addDataField('Y', regionColumn);
    // const dataField1 = await visual.getDataFields('Y');
    // const dataField2 = await visual.getDataFields('Category');
    // console.log("Visual 'Y' fields:\n", dataField1);
    // console.log("Visual 'Category' fields:\n", dataField2);
  })
  reportData.value.report.on("error", function (event) {
    // console.log(event.detail);
    if(Number(event.detail.errorCode) === 403) {
      showErrorTips.value = true
    }
  });
}
const transformModelType = (typeStr) => {
  switch (typeStr) {
    case 'BasicFilter':
      return pbi.models.FilterType.BasicFilter
    case 'AdvancedFilter':
      return pbi.models.FilterType.AdvancedFilter
    case 'TopN':
      return pbi.models.FilterType.TopN
    case 'RelativeDate':
      return pbi.models.FilterType.RelativeDate
    case 'RelativeTime ':
      return pbi.models.FilterType.RelativeTime
    default:
      return pbi.models.FilterType.BasicFilter
  }
}
// 识别用户输入的问题是否包含关键字词
const aiPbiType = (params) => {
  if (params.includes('条形图') || params.includes('条状图')) {
    return 'barChart'
  } else if (params.includes('柱形图') || params.includes('柱图')) {
    return 'columnChart'
  } else if (params.includes('饼图') || params.includes('饼状图')) {
    return 'pieChart'
  } else if (params.includes('卡片图') || params.includes('多行卡')) {
    return 'multiRowCard'
  } else {
    return 'tableEx'
  }
}

const toggleShowThink = () => {
  showThinkContent.value = !showThinkContent.value
}

const realContent = ref('')
function streamAiText() {
  let num = 0
  let wordTimeout = null
  const autoWord = (str) => {
    wordTimeout = setTimeout(() => {
      realContent.value = str.substr(0, num)
      scrollTo()
      num += 5
      if (num < str.length + 5) {
        autoWord(str)
      } else {
        clearTimeout(wordTimeout)
        wordTimeout = null

        showDivider.value = true

        createPbiVisual()

        showThinkContent.value = false

        // window.self.scroll({
        //   top: window.self.document.scrollingElement.scrollHeight,
        //   left: 0,
        //   behavior: 'smooth'
        // })
      }
    }, 100)
  }
  chartData.value.aitext ? autoWord(chartData.value.aitext) : createPbiVisual()
}

function scrollTo() {
  let wrap = document.querySelector('.pbi-render')
  let pbi = document.querySelector('.pbi')
  let wrapHeight = wrap.scrollHeight
  let pbiHeight = pbi.clientHeight
  if(wrapHeight > pbiHeight * 2) {
    window.self.scrollTo({
      left: 0,
      top: wrapHeight - pbiHeight * 2,
      behavior: 'smooth'
    })
  }
}

</script>

<template>
  <div class="pbi-render">
    <div v-if="showErrorTips" class="error-text">会话Token已过期，请重新询问。</div>
    <template v-else>
      <!-- <div class="thinking-content" v-if="chartData.aitext && showThinkContent"> -->
      <div class="thinking-content" v-if="chartData.aitext">
        <p class="think-title">
          <div>
            <i class="iconfont icon-shendusikao"></i>
            <span class="text">{{ $t('COMMON.THINK_PROCESS') }}:</span>
          </div>
          <div class="right-btn" :class="{showThink: showThinkContent}">
            <el-icon @click="toggleShowThink"><ArrowUpBold /></el-icon>
          </div>
        </p>
        <div v-show="showThinkContent">
          <markdown-render :context="realContent" />
          <div class="divider" v-if="showDivider">
            <span>用户输入问题为：“{{chartData.userInput}}”, 生成结果如下</span>
          </div>
        </div>
      </div>
      <div class="pbi" :id="chartData.requestId"></div>
    </template>
  </div>
</template>

<style scoped lang="less">
.thinking-content {
  padding: 10px;
  color: #9195a3;
  border-radius: 6px;
  background: linear-gradient(180deg, rgb(127 161 255 / 6%), rgb(203 228 255 / 5%));
  margin-bottom: 5px;
  border-radius: 6px;
  .think-title {
    display: flex;
    align-items: center;
    color: #0A121F;
    // color: #568cf4;
    font-size: 16px;
    font-weight: bold;
    justify-content: space-between;
    padding-right: 20px;
    .text {
      padding-left: 3px;
    }
    .right-btn {
      cursor: pointer;
      transition: .3s transform ease;
      &.showThink {
        transform: rotate(-180deg);
      }
    }
  }
  .markdown-render {
    font-size: 14px;
  }
  .divider {
    border-top: 1px dashed #000;
    padding: 10px 0;
    font-weight: bold;
  }
}
.pbi-render {
  width: 100%;
  height: 100%;
  .error-text {
    height: 40px;
    line-height: 40px;
    font-size: 12px;
    color: #0A121F;
  }
  .pbi {
    width: 100%;
    height: 450px; // Match or exceed the height you set in the visual layout
  }
}
</style>