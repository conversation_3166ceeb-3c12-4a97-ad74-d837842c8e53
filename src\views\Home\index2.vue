<script setup>

let introduceScenes = [
  {
    title: 'Data Analysis',
    desc: 'IntelliData enables user to query data in business-oriented natural language.',
    icon: 'icon-a-DataAnalysis'
  },
  {
    title: 'Data Visualization',
    desc: 'IntelliData enables user to query data by conversing with the engine in business-oriented natural language.',
    icon: 'icon-a-DataVisualization'
  },
  {
    title: 'Data Insight',
    desc: 'Equipped with various data analytical capabilities, IntelliData  provides deep data insights, facilitating users’ decision-making.',
    icon: 'icon-a-DataInsight'
  },
  {
    title: 'Data Diversity',
    desc: 'IntelliData  provides connections to various data sources  including PowerBI, SAP HANA, Databricks, and LUDP.',
    icon: 'icon-a-DataDiversity'
  },
  {
    title: 'Data Securitys',
    desc: 'IntelliData  allows administrators to define roles and permissions, ensuring users are granted proper roles and data access rights.',
    icon: 'icon-a-DataSecuritys'
  }
]
</script>

<template>
<div class="home">
  <div class="top-wrap">
    <div class="top-logo">
      <img class="logo-img" src="./logo-text.png" alt="">
    </div>
    <div class="bottom-func">
      <div class="func-block first-block">
        <img src="./img-chat.png" alt="">
        <p class="func-text">Agent Chat</p>
      </div>
      <div class="func-block second-block">
        <img src="./img-data.png" alt="">
        <p class="func-text">Chat Data</p>
      </div>
      <div class="func-block thired-block">
        <img src="./img-knowledge.png" alt="">
        <p class="func-text">Chat Knowledge</p>
      </div>
      <div class="func-block four-block">
        <img src="./img-setting.png" alt="">
        <p class="func-text">Settings</p>
      </div>
    </div>
  </div>
  <div class="bottom-wrap">
    <div class="introduce-card" v-for="item in introduceScenes">
      <div class="top-icon">
        <i class="iconfont" :class="item.icon"></i>
      </div>
      <div class="middle-text">
        <span>{{item.title}}</span>
      </div>
      <div class="bottom-desc">
        <span>{{item.desc}}</span>
      </div>
    </div>
  </div>
</div>
</template>

<style scoped lang="less">
.home {
  position: relative;
  height: calc(100vh - 50px);
  overflow: auto;
  max-width: 1146px;
  margin: 0 auto;
  .top-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    //justify-content: center;
    padding-top: 66px;
    max-width: 1146px;
    min-height: 636px;
    background-image: url(/src/assets/bg.png);
    background-size: 100%;
    background-repeat: no-repeat;
    .top-logo {
      //margin-bottom: 55px;
      margin-bottom: 50px;
    }
    .logo-img {
      height: 82px;
    }
    .bottom-func {
      display: flex;
      .func-block {
        position: relative;
        //display: inline-block;
        //display: flex;
        justify-content: center;
        width: 192px;
        img {
          width: 100%;
        }
        .func-text {
          position: absolute;
          bottom: 10px;
          font-weight: bold;
          font-size: 16px;
          color: #2D3441;
          width: 100%;
          text-align: center;
        }
      }
      .first-block {
        margin-right: 28px;
      }
      .second-block {
        margin-right: 45px;
        top: 67px;
      }
      .thired-block {
        margin-right: 18px;
        top: 90px;
        .func-text {
          bottom: 30px;
        }
      }
      .four-block {
        .func-text {
          bottom: 20px;
        }
      }
    }
  }
  .bottom-wrap {
    margin-top: -160px;
    display: flex;
    .introduce-card {
      width: 218px;
      height: 265px;
      margin-right: 16px;
      padding: 30px 16px;
      background: rgba(255,255,255,0.6);
      border-radius: 6px;
      text-align: center;
      &:last-child {
        margin-right: 0;
      }
    }
    .top-icon, .middle-text, .bottom-desc {
      display: flex;
      justify-content: center;
    }
    .top-icon {
      align-items: center;
      width: 48px;
      height: 48px;
      margin: 0 auto 25px;
      .iconfont {
        font-size: 42px;
        color: #409eff;
      }
    }
    .middle-text {
      margin-bottom: 22px;
      font-size: 14px;
      font-weight: bold;
      color: #2D3441;
    }
    .bottom-desc {
      font-size: 12px;
      color: #5B6579;
      line-height: 14px;
    }
  }
}
</style>
