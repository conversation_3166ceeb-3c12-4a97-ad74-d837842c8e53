<script setup>
import { useI18n } from 'vue-i18n'
import { ref, computed } from 'vue'
import { ElLoading, ElMessage } from 'element-plus'
import * as api from './api'

const { locale, t } = useI18n()

// 声明props
const props = defineProps({
  apiData: Object
})

// 接收传递的方法
const emit = defineEmits(['getDbList'])

// 是否显示对话框
const dialogVisible = ref(false)
// 插件类型
const apiTypes = ['function call', 'after api', 'none']
// 插件列表
const pluginList = ref([])
// 获取插件列表
const getPluginList = async () => {
  const loading = ElLoading.service({ target: '.el-dialog' })
  try {
    const res = await api.getPluginList({ isPublish: '1' })
    pluginList.value = res?.list || []
    loading.close()
  } catch (e) {
    loading.close()
  }
}

// api表单数据
const apiObj = {
  connectId: null,
  apiProperty: 2,
  apiIdList: []
}
const apiForm = ref(JSON.parse(JSON.stringify(apiObj)))
const apiRules = computed(() => {
  return {
    apiProperty: [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }],
    apiIdList: [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }]
  }
})
// 填充数据
const resetData = (isOpen) => {
  if (isOpen) {
    apiForm.value = props.apiData
    getPluginList()
  } else {
    apiForm.value = JSON.parse(JSON.stringify(apiObj))
  }
}
// 插件类型变更
const apiPropertyChange = (val) => {
  if (val === 2) {
    apiForm.value.apiIdList = []
  }
}
// 绑定API
const saveApi = async () => {
  const loading = ElLoading.service({ target: '.el-dialog' })
  try {
    await api.saveApi(apiForm.value)
    loading.close()
    dialogVisible.value = false
    ElMessage.success(`${t('DATA_SOURCE.OPERATION')} ${t('COMMON.SUCCESS')}`)
    emit('getDbList')
  } catch (e) {
    loading.close()
  }
}
// 数据源表单实例
const apiFormRef = ref()
// 提交表单
const submitForm = () => {
  apiFormRef.value.validate((valid) => {
    if (valid) {
      saveApi()
    }
  })
}

// 暴露属性和方法
defineExpose({ dialogVisible })
</script>

<template>
  <div class="api-dialog">
    <el-dialog
      class="setting-page-dialog-wrap"
      v-model="dialogVisible"
      :title="$t('DATA_SOURCE.BIND_API')"
      width="500px"
      :close-on-click-modal="false"
      destroy-on-close
      @open="resetData(true)"
      @closed="resetData(false)"
    >
      <el-form
        ref="apiFormRef"
        :model="apiForm"
        :rules="apiRules"
        :label-width="locale === 'en_US' ? '110px' : '90px'"
      >
        <el-form-item
          :label="$t('PLUGIN.PLUGIN_TYPE')"
          prop="apiProperty"
        >
          <el-select
            v-model="apiForm.apiProperty"
            :placeholder="$t('COMMON.PLS_SELECT')"
            @change="apiPropertyChange"
          >
            <el-option
              v-for="(item, index) in apiTypes"
              :key="index"
              :label="item"
              :value="index"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="apiForm.apiProperty !== 2"
          :label="$t('PLUGIN.PLUGIN_NAME')"
          prop="apiIdList"
        >
          <el-select
            v-model="apiForm.apiIdList"
            :placeholder="$t('COMMON.PLS_SELECT')"
            multiple
            collapse-tags
            collapse-tags-tooltip
            filterable
            :reserve-keyword="false"
            clearable
          >
            <el-option
              v-for="item in pluginList"
              :key="item.id"
              :label="item.apiName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">{{ $t('COMMON.CANCEL') }}</el-button>
        <el-button
          type="primary"
          @click="submitForm()"
        >{{ $t('COMMON.SUBMIT') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.api-dialog {
  :deep(.el-dialog__body) {
    padding: 18px 20px 0;
    .el-select {
      width: 100%;
    }
  }
}
</style>
