<script setup>
import { useI18n } from 'vue-i18n'
import { ref, computed, watch } from 'vue'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import { encrypt, decrypt } from '@/utils/encrypt'
import { loginRedirect } from '@/utils/util'
import { getUsername } from '@/utils/auth'
import * as api from './api'
import { useRoute } from 'vue-router'

const { locale, t } = useI18n()

const route = useRoute()

// 声明props
const props = defineProps({
  dbTypeList: Array,
  userList: Array,
  dbData: Object,
  dialogType: String,
  // iframeType: Array
})

let currentRowTableNameList = []
// const dbTypeList1 = computed(() => {
//   return window.top === window.self ? props.dbTypeList : props.dbTypeList.filter(item => props.iframeType.includes(item))
// })

const dialogType = computed(() => {
  return props.dialogType
})

// 没有 Project 或 Schema 权限时 禁用下一步按钮
const disabledButton = ref(false)

// 不包含数据库字段的数据源类型
const dbNameBlackList = ['hana', 'pbi']
// 不包含Schema字段的数据源类型
const schemaBlackList = ['mysql', 'doris', 'pbi', 'ludp']
// 不包含账号字段的数据源类型
const dbUserBlackList = ['synapse', 'ludp', 'databricks', 'pbi']
// 不包含密码字段的数据源类型
const dbPwdBlackList = ['synapse', 'ludp', 'databricks', 'pbi']

// 接收传递的方法
const emit = defineEmits(['getDbList'])

// 是否显示对话框
const dialogVisible = ref(false)
// 对话框标题
const dialogTitle = computed(() => {
  if( dialogType.value === 'copy' ) {
    return t('DATA_SOURCE.COPY_DATA_SOURCE')
  } else if(dialogType.value === 'edit') {
    return t('DATA_SOURCE.EDIT_DATA_SOURCE')
  }else {
    return t('DATA_SOURCE.CREATE_DATA_SOURCE')
  }
})
// 已进行的步骤
const stepActive = ref(0)

// 数据源表单数据
const dbObj = {
  dbType: '',
  dbBusiName: '',
  // dbEnv: 'prd',
  dbHost: '',
  dbPort: null,
  warehouse: '',
  dbRole: '',
  dbName: '',
  schemaName: '',
  dbUser: '',
  dbPwd: '',
  pbiRegion: 'CHINA',
  // reportId: '',
  reportId: {
    id: '',
    name: '',
    datasetId: ''
  },
  accessToken: '',
  metadataOwner: [],
  dbHttpPath: '',
  tableNameList: [],
  projectObj: {},
  ludpSchemaName: ''
}
const dbForm = ref(JSON.parse(JSON.stringify(dbObj)))
const dbRules = computed(() => {
  return {
    dbType: [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }],
    dbBusiName: [{ required: true, message: t('COMMON.PLS_INPUT'), trigger: 'blur' }],
    // dbEnv: [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }],
    dbHost: [{ required: true, message: t('COMMON.PLS_INPUT'), trigger: 'blur' }],
    dbPort: [
      { required: true, message: t('COMMON.PLS_INPUT'), trigger: 'blur' },
      { type: 'number', message: t('COMMON.MUST_BE_NUM') }
    ],
    dbRole: [{ required: true, message: t('COMMON.PLS_INPUT'), trigger: 'blur' }],
    warehouse: [{ required: true, message: t('COMMON.PLS_INPUT'), trigger: 'blur' }],
    dbName: [{ required: true, message: t('COMMON.PLS_INPUT'), trigger: 'blur' }],
    schemaName: [{ required: true, message: t('COMMON.PLS_INPUT'), trigger: 'blur' }],
    dbUser: [{ required: true, message: t('COMMON.PLS_INPUT'), trigger: 'blur' }],
    pbiRegion: [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }],
    reportId: [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change',
      validator: (rule, value, callback) => {if (!value || !value.id) {callback(new Error(t('COMMON.PLS_SELECT')))} else {callback()}}
    }],
    metadataOwner: [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }],
    dbHttpPath: [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }],
    tableNameList: [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }],
    projectObj: [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }],
    ludpSchemaName: [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }]
  }
})
// 填充数据
const resetData = async(isOpen) => {
  tableList.value = []
  stepActive.value = 0
  if (isOpen) {
    currentRowTableNameList = {
      id: props.dbData.reportId,
      name: props.dbData.reportName,
      datasetId: props.dbData.datasetId
    }
    dbForm.value = {
      dbType: props.dbData.dbType || '',
      dbBusiName: props.dbData.dbBusiName || '',
      // dbEnv: props.dbData.dbEnv || 'prd',
      dbHost: props.dbData.dbHost || '',
      dbPort: Number(props.dbData.dbPort) || null,
      dbRole: props.dbData.dbRole || '',
      warehouse: props.dbData.warehouse || '',
      dbName: props.dbData.dbName || '',
      schemaName: props.dbData.schemaName || '',
      // projectObj: {
      //   dataAccount: props.dbData.ludpDataAccount || '',
      //   name: props.dbData.ludpProjectName || ''
      // },
      ludpSchemaName: props.dbData.schemaName || '',
      dbUser: props.dbData.dbUser || '',
      dbPwd: props.dbData.dbPwd ? decrypt(props.dbData.dbPwd) : '',
      pbiRegion: props.dbData.pbiRegion || 'CHINA',
      // reportId: props.dbData.reportId || '',
      reportId: {
        id: props.dbData.reportId,
        name: props.dbData.reportName,
        datasetId: props.dbData.datasetId
      } || '',
      accessToken: props.dbData.accessToken || '',
      metadataOwner: props.dbData.metadataOwner ? props.dbData.metadataOwner.split(',') : [],
      dbHttpPath: props.dbData.dbHttpPath || '',
      tableNameList: props.dbData.tableNameList || []
    }
    if (dbForm.value.dbType === 'pbi') {
      // 根据个人token获取pbi报表名称Options getReportNameList ？
      // getReportOptions(dbForm.value.pbiRegion, props.dbData.groupId)
      await getReportNameList(dbForm.value.pbiRegion)

      const findValue = reportOptions.value.find(item => item.id === props.dbData.reportId)
      if(!findValue) {
        // 添加当前数据的 report 信息
        reportOptions.value = [{
          id: props.dbData.reportId,
          name: props.dbData.reportName,
          datasetId: props.dbData.datasetId
        }, ...reportOptions.value]
      }
      
    }
    if (dbForm.value.dbType === 'ludp') {
      getProjectList()
      dbForm.value.projectObj = {
        dataAccount: props.dbData.ludpDataAccount || '',
        name: props.dbData.ludpProjectName || ''
      }
      getSchemaList()
      // dbForm.value.schemaName = props.dbData.schemaName || ''
    }
  } else {
    dbForm.value = JSON.parse(JSON.stringify(dbObj))
    // 恢复 禁用下一步状态
    disabledButton.value = false
  }
}

// ludp-project列表
const projectList = ref([])
// 获取ludp-project列表
const getProjectList = async () => {
  let params = {
    dbType: 'ludp',
    // dbEnv: dbForm.value.dbEnv
  }
  try {
    const res = await api.getProjectList(params)
    projectList.value = res || []
  } catch (e) {
    if(e.data.status === 300) {
      disabledButton.value = true
    }
    console.log(e, 'getProjectList error')
    loginRedirect(e.data.msg)
  }
}
const schemaList = ref([])
// projcet改变联动schemaList
const getSchemaList = async () => {
  let params = {
    dbType: 'ludp',
    // dbEnv: dbForm.value.dbEnv,
    ludpProjectName: dbForm.value.projectObj.name,
    ludpDataAccount: dbForm.value.projectObj.dataAccount
  }
  try {
    const res = await api.getSchemaList(params)
    schemaList.value = res || []
  } catch (error) {
    if(error.data.status === 300) {
      disabledButton.value = true
    }
    console.log('getSchemaList error', error)
  }
}

const changeSchemaName = () => {
  dbForm.value.tableNameList = []
}
const changeProject = () => {
  dbForm.value.ludpSchemaName = ''
  schemaList.value = []
  getSchemaList()
  // dbFormRef.value.resetFields('project')
}
const changeEnv = () => {
  dbForm.value.projectObj = {}
  dbForm.value.ludpSchemaName = ''
  schemaList.value = []
  getProjectList()
}

// 选择数据类型为pbi时获取pbi工作区Options；选择数据类型为ludp时获取Project Options
const changeDbType = (val) => {
  if (val === 'databricks') {
    dbForm.value.dbHost = dbForm.value.dbHost || 'adb-****************.2.databricks.azure.cn'
    dbForm.value.dbPort = dbForm.value.dbPort || 443
    dbForm.value.dbHttpPath = dbForm.value.dbHttpPath || 'sql/1.0/warehouses/ed66faaeb20f7542'
  } else if (val === 'pbi') {
    getReportNameList()
    dbForm.value.reportId = {
      id: '',
      name: '',
      datasetId: ''
    }
    // dbForm.value.reportId = ''
    // reportOptions.value = res || []
  } else if (val === 'ludp') {
    getProjectList()
  }else if(val === 'snowflake') {
    dbForm.value.dbPort = dbForm.value.dbPort || 443
  }
}
// 选择pbi环境时获取pbi工作区Options
const changePbiRegion = (val) => {
  // dbForm.value.reportId = ''
  dbForm.value.reportId = {
    id: '',
    name: '',
    datasetId: ''
  }
  reportOptions.value = []
}

// pbi报表名称Options
const reportOptions = ref([])
const getReportOptions = async (env, workspaceId) => {
  const loading = ElLoading.service({ target: '.el-dialog' })
  try {
    const res = await api.getReportOptions(env, workspaceId)
    reportOptions.value = res || []
    loading.close()
  } catch (e) {
    loading.close()
  }
}

// 获取ciop/ops对应环境域名 进行azure token获取
const getCiopDomain = (url) => {
  if(url && (url.includes("tst") || url.includes('localhost'))) {
    return {
      ciop: "https://ciop-tst.lenovo.com",
      ops: "https://opsagent-tst.lenovo.com"
    }
  }else if(url && url.includes("uat")) {
    return {
      ciop: "https://ciop-uat.lenovo.com",
      ops: "https://opsagent-uat.lenovo.com"
    }
  }else {
    return {
      ciop: "https://ciop.lenovo.com",
      ops: "https://opsagent.lenovo.com"
    }
  }
}
const {ciop, ops} = getCiopDomain(window.self.location.origin)

const getReportNameList = async (env) => {
  const loading = ElLoading.service({ target: '.el-dialog' })
  try {
    const res = await api.getReportNameList(dbForm.value.pbiRegion)
    if(res.status === 40003) {
      const username = route.query?.itcode || getUsername()
      const loginUrl = `${ciop}/api/powerbi/aad/redirect?itcode=${username}&redirectUrl=${ops}/toTokenTip`
      // if(confirm('未检测到有效Azure Token,是否确认登陆获取?')) {
      //   const loginUrl = `${window.self.location.origin}/api/aad/redirect?itcode=${getUsername()}&type=pbi&redirectUrl=${window.self.location.origin}/loggedTips`
      //   // window.top.location.href = loginUrl
      //   window.open(loginUrl, '_blank')
      // }else {
      //   loading.close()
      // }
      ElMessageBox.alert(`未检测到有效Azure Token,<a href="${loginUrl}" target="_blank" style="color: blue;text-decoration:underline;cursor: pointer;">去登陆</a>【完成后请重新操作!】`, '登陆Azure提示', {
        dangerouslyUseHTMLString: true,
        showConfirmButton: false
      })
      loading.close()
    }else {
      reportOptions.value = res || []
      loading.close()
    }
  } catch (e) {
    loading.close()
  }
}

// 保存数据源
const saveDb = async () => {
  const loading = ElLoading.service({ target: '.el-dialog' })
  const { dbType, dbBusiName, metadataOwner } = dbForm.value
  let params = {
    dbType,
    dbBusiName,
    metadataOwner: metadataOwner.join(',')
  }
  try {
    if (dbType === 'pbi') {
      const { pbiRegion, reportId, accessToken, tableNameList } = dbForm.value
      params = {
        ...params,
        pbiRegion,
        reportId: reportId.id,
        datasetId: reportOptions.value.find(x => x.id === reportId.id).datasetId,
        reportName: reportOptions.value.find(x => x.id === reportId.id).name,
        // accessToken,
        tableNameList
      }
    } else {
      const { dbHost, dbPort, warehouse, dbRole, dbName, schemaName, dbUser, dbPwd, dbHttpPath, tableNameList, projectObj, ludpSchemaName } = dbForm.value
      params = {
        ...params,
        // dbEnv: dbType === 'ludp' ? dbEnv : undefined,
        dbHost,
        dbPort,
        warehouse: dbType === 'snowflake' ? warehouse : undefined,
        dbRole: dbType === 'snowflake' ? dbRole : undefined,
        dbName: !dbNameBlackList.includes(dbType) ? dbName : undefined,
        schemaName: !schemaBlackList.includes(dbType) ? schemaName : ludpSchemaName,
        dbUser: !dbUserBlackList.includes(dbType) ? dbUser : undefined,
        dbPwd: !dbPwdBlackList.includes(dbType) ? (dbPwd ? encrypt(dbPwd) : '') : undefined,
        dbHttpPath: dbType === 'databricks' ? dbHttpPath : undefined,
        tableNameList,
        ludpDataAccount: projectObj?.dataAccount,
        ludpProjectName: projectObj?.name
      }
    }
    if (props.dbData.id) {
      params.id = props.dbData.id
      const res = await api.editDb(params)
      loginRedirect(res)
    } else {
      const res = await api.addDb(params)
      loginRedirect(res)
    }
    loading.close()
    dialogVisible.value = false
    emit('getDbList')
  } catch (e) {
    console.log(e, 'error');
    loading.close()
  }
}
// 数据源中的表
const tableList = ref([])
// 获取数据源中的表
const getTableList = async (tableName) => {
  if (tableName === '') {
    return false
  }
  const { dbType, dbHost, dbPort, warehouse, dbRole,  dbName, schemaName, dbUser, dbPwd, dbHttpPath, projectObj, ludpSchemaName } = dbForm.value
  const res = await api.getTableList({
    dbType,
    // dbEnv: dbType === 'ludp' ? dbEnv : undefined,
    dbHost,
    dbPort,
    warehouse: dbType === 'snowflake' ? warehouse : undefined,
    dbRole: dbType === 'snowflake' ? dbRole : undefined,
    ludpDataAccount: dbType === 'ludp' ? projectObj?.dataAccount : undefined,
    ludpProjectName: dbType === 'ludp' ? projectObj?.name : undefined,
    dbName: !dbNameBlackList.includes(dbType) ? dbName : undefined,
    // schemaName: !schemaBlackList.includes(dbType) ? schemaName : undefined,
    schemaName: !schemaBlackList.includes(dbType) ? schemaName : ludpSchemaName,
    dbUser: !dbUserBlackList.includes(dbType) ? dbUser : undefined,
    dbPwd: !dbPwdBlackList.includes(dbType) ? (dbPwd ? encrypt(dbPwd) : '') : undefined,
    dbHttpPath: dbType === 'databricks' ? dbHttpPath : undefined,
    tableName
  })
  if (res) {
    if (Array.isArray(res)) {
      if (res.length) {
        tableList.value = res
      } else if (tableName) {
        tableList.value = []
      } else {
        ElMessage.error(t('DATA_SOURCE.NO_TABLES'))
        throw new Error()
      }
    } else {
      loginRedirect(res)
      throw new Error()
    }
  } else if (tableName) {
    tableList.value = []
  } else {
    ElMessage.error(t('DATA_SOURCE.NO_TABLES'))
    throw new Error()
  }
}

// 远程获取搜索tableList
const remoteGetTableList = (query) => {
  if(dbForm.value.dbType !== 'pbi') {
    getTableList(query)
  }
}

// 获取pbi table list
const getPbiTableList = async() => {
  const res = await api.getPbiTableList({
    pbiRegion: dbForm.value.pbiRegion || 'CHINA',
    datasetId: dbForm.value.reportId.datasetId
  })
  // console.log('pbiList', res)
  if (res) {
    if (Array.isArray(res)) {
      if (res.length) {
        const filterPbiTableData = res.map(item => item.tableName)
        // console.log(filterPbiTableData)
        tableList.value = filterPbiTableData
      } else if (tableName) {
        tableList.value = []
      } else {
        ElMessage.error(t('DATA_SOURCE.NO_TABLES'))
        throw new Error()
      }
    } else {
      // 重定向azure
      // loginRedirect(res)
      // throw new Error()
    }
  } else if (tableName) {
    tableList.value = []
  } else {
    ElMessage.error(t('DATA_SOURCE.NO_TABLES'))
    throw new Error()
  }
}

// 数据源表单实例
const dbFormRef = ref()
// 数据源下一步
const nextStep = () => {
  dbFormRef.value.validate(async (valid) => {
    if (valid) {
      const loading = ElLoading.service({ target: '.el-dialog' })
      try {
        if(dbForm.value.dbType === 'pbi') {
          await getPbiTableList()
          if(currentRowTableNameList.id !== dbForm.value.reportId.id) {
            dbForm.value.tableNameList = []
          }
        }else {
          await getTableList()
        }
        stepActive.value++
        loading.close()
      } catch (e) {
        console.log(e)
        loading.close()
      }
    }
  })
}
// 数据源上一步
const prevStep = () => {
  tableList.value = []
  stepActive.value--
}
// 提交表单
const submitForm = () => {
  dbFormRef.value.validate((valid) => {
    if (valid) {
      saveDb()
    }
  })
}

// 暴露属性和方法
defineExpose({ dialogVisible })
</script>

<template>
  <div class="database-dialog">
    <el-dialog
      class="setting-page-dialog-wrap"
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
      @open="resetData(true)"
      @closed="resetData(false)"
    >
      <el-form
        ref="dbFormRef"
        :model="dbForm"
        :rules="dbRules"
        :label-width="locale === 'en_US' ? '150px' : '130px'"
      >
        <div v-if="stepActive === 0">
          <el-form-item
            prop="dbType"
            :label="$t('DATA_SOURCE.DATA_SOURCE_TYPE')"
          >
            <el-select
              v-model="dbForm.dbType"
              :placeholder="$t('COMMON.PLS_SELECT')"
              :disabled="!!props.dbData.id || dialogType === 'copy'"
              @change="changeDbType"
            >
              <el-option
                v-for="item in dbTypeList"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            prop="dbBusiName"
            :label="$t('DATA_SOURCE.DATA_SOURCE_NAME')"
          >
            <el-input
              maxlength="30"
              v-model="dbForm.dbBusiName"
              :placeholder="$t('COMMON.PLS_INPUT')"
              :disabled="!!props.dbData.id"
            />
          </el-form-item>
<!--          <el-form-item-->
<!--            v-if="dbForm.dbType === 'ludp'"-->
<!--            prop="dbEnv"-->
<!--            :label="$t('DATA_SOURCE.ENVIRONMENT')"-->
<!--          >-->
<!--            <el-radio-group v-model="dbForm.dbEnv" @change="changeEnv">-->
<!--              <el-radio label="prd">{{ $t('DATA_SOURCE.PROD') }}</el-radio>-->
<!--              <el-radio label="tst">{{ $t('DATA_SOURCE.TEST') }}</el-radio>-->
<!--            </el-radio-group>-->
<!--          </el-form-item>-->
          <template v-if="dbForm.dbType !== 'ludp'">
            <el-form-item
              v-if="dbForm.dbType && dbForm.dbType !== 'pbi'"
              prop="dbHost"
              :label="$t('DATA_SOURCE.HOST')"
            >
              <el-input v-model="dbForm.dbHost" :disabled="dialogType === 'copy'" :placeholder="$t('COMMON.PLS_INPUT')" />
            </el-form-item>
            <el-form-item
              v-if="dbForm.dbType && dbForm.dbType !== 'pbi'"
              prop="dbPort"
              :label="$t('DATA_SOURCE.PORT')"
            >
              <el-input v-model.number="dbForm.dbPort" :disabled="dialogType === 'copy'" :placeholder="$t('COMMON.PLS_INPUT')" />
            </el-form-item>
            <!-- 添加wareHouse -->
            <el-form-item
              v-if="dbForm.dbType && dbForm.dbType === 'snowflake'"
              prop="warehouse"
              label="Warehouse"
            >
              <el-input v-model="dbForm.warehouse" :placeholder="$t('COMMON.PLS_INPUT')" />
            </el-form-item>
            <el-form-item
              v-if="dbForm.dbType && !dbNameBlackList.includes(dbForm.dbType)"
              prop="dbName"
              :label="$t('DATA_SOURCE.DATABASE')"
            >
              <el-input v-model="dbForm.dbName" :disabled="dialogType === 'copy'" :placeholder="$t('COMMON.PLS_INPUT')" />
            </el-form-item>
            <el-form-item
              v-if="dbForm.dbType && !schemaBlackList.includes(dbForm.dbType)"
              prop="schemaName"
              label="Schema"
            >
              <el-input v-model="dbForm.schemaName" :disabled="dialogType === 'copy'" :placeholder="$t('COMMON.PLS_INPUT')" />
            </el-form-item>
          </template>
          <template v-else>
            <el-form-item
              prop="projectObj"
              label="Project"
            >
              <el-select
                v-model="dbForm.projectObj"
                :placeholder="$t('COMMON.PLS_SELECT')"
                value-key="dataAccount"
                :disabled="dialogType === 'copy'"
                @change="changeProject"
              >
                <el-option
                  v-for="item in projectList"
                  :key="item.dataAccount"
                  :label="item.name"
                  :value="item"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="dbForm.dbType"
              prop="ludpSchemaName"
              label="Schema"
            >
              <el-select
                @change="changeSchemaName"
                v-model="dbForm.ludpSchemaName"
                :disabled="JSON.stringify(dbForm.projectObj) === '{}' || !JSON.stringify(dbForm.projectObj) || dialogType === 'copy'"
                :placeholder="$t('COMMON.PLS_SELECT')"
              >
                <el-option
                  v-for="item in schemaList"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </template>
          <el-form-item
            v-if="dbForm.dbType && !dbUserBlackList.includes(dbForm.dbType)"
            prop="dbUser"
            :label="$t('DATA_SOURCE.USERNAME')"
          >
            <el-input v-model="dbForm.dbUser" :disabled="dialogType === 'copy'" :placeholder="$t('COMMON.PLS_INPUT')" />
          </el-form-item>
          <el-form-item
            v-if="dbForm.dbType && !dbPwdBlackList.includes(dbForm.dbType)"
            prop="dbPwd"
            :label="$t('DATA_SOURCE.PASSWORD')"
          >
            <el-input v-model="dbForm.dbPwd" type="password" :disabled="dialogType === 'copy'" :placeholder="$t('COMMON.PLS_INPUT')" />
          </el-form-item>
          <!-- Role -->
          <el-form-item
            v-if="dbForm.dbType && dbForm.dbType === 'snowflake'"
            prop="dbRole"
            :label="$t('DATA_SOURCE.ROLE')"
          >
            <el-input v-model="dbForm.dbRole" :placeholder="$t('COMMON.PLS_INPUT')" />
          </el-form-item>
          <el-form-item
            v-if="dbForm.dbType && dbForm.dbType === 'databricks'"
            prop="dbHttpPath"
            label="httpPath"
          >
            <el-input v-model="dbForm.dbHttpPath" :disabled="dialogType === 'copy'" :placeholder="$t('COMMON.PLS_INPUT')" />
          </el-form-item>

          <el-form-item
            v-if="dbForm.dbType === 'pbi'"
            prop="pbiRegion"
            :label="$t('DATA_SOURCE.ENVIRONMENT')"
          >
            <el-radio-group v-model="dbForm.pbiRegion" :disabled="dialogType === 'copy'" @change="changePbiRegion">
              <el-radio label="CHINA">{{ $t('DATA_SOURCE.VNET') }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            v-if="dbForm.dbType === 'pbi'"
            prop="reportId"
            :label="$t('DATA_SOURCE.REPORT_NAME')"
          >
            <el-select
              v-model="dbForm.reportId"
              value-key="id"
              :placeholder="$t('COMMON.PLS_SELECT')"
              filterable
              :disabled="dialogType === 'copy'"
            >
              <el-option
                v-for="item in reportOptions"
                :key="item.id"
                :label="item.name"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            prop="metadataOwner"
            :label="$t('DATA_SOURCE.METADATA_OWNER')"
          >
            <el-select
              v-model="dbForm.metadataOwner"
              :placeholder="$t('COMMON.PLS_SELECT')"
              multiple
              collapse-tags
              collapse-tags-tooltip
              filterable
              :disabled="dialogType === 'copy'"
              :reserve-keyword="false"
            >
              <el-option
                v-for="item in props.userList"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </div>
        <div v-if="stepActive === 1">
          <el-form-item
            prop="tableNameList"
            :label="$t('DATA_SOURCE.TABLE_NAME')"
          >
            <el-select
              v-model="dbForm.tableNameList"
              :placeholder="$t('COMMON.PLS_SELECT')"
              multiple
              collapse-tags
              collapse-tags-tooltip
              filterable
              :reserve-keyword="false"
              remote
              remote-show-suffix
              :remote-method="remoteGetTableList"
            >
              <el-option
                v-for="item in tableList"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <el-button
          v-if="stepActive === 0 && dialogType !== 'copy'"
          type="primary"
          :disabled="disabledButton"
          @click="nextStep()"
        >{{ $t('COMMON.NEXT') }}</el-button>
        <el-button
          v-if="stepActive === 1 && dialogType !== 'copy'"
          @click="prevStep()"
        >{{ $t('COMMON.BACK') }}</el-button>
        <el-button
          v-if="
            stepActive === 1 ||
            dialogType === 'copy'
          "
          type="primary"
          @click="submitForm()"
        >{{ $t('COMMON.SUBMIT') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.database-dialog {
  :deep(.el-dialog__body) {
    padding: 18px 20px 0;
    .el-select {
      width: 100%;
    }
  }
}
</style>
