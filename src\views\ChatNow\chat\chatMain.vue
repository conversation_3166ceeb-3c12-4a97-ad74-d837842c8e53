<script setup>
import { CopyDocument, MoreFilled } from '@element-plus/icons-vue'
import { ref, nextTick, watch, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useI18n } from 'vue-i18n'
import { copyToClipboard, loginRedirect } from '@/utils/util'
import { getToken } from '@/utils/auth'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import pbiRender from './pbiRender.vue'
import markdownRender from '@/components/MarkdownRender/markdownRender.vue'
import { ElMessage } from 'element-plus'
import * as api from './api'

const route = useRoute()
const router = useRouter()
const user = useUserStore()
const { t } = useI18n()

// 新会话id值
const newID = ref('')

// 当前选择的模型
const modelName = ref('')
// 模型列表
const modelList = ref([])
modelName.value = route.query.model || user.model[0] || ''
modelList.value = user.model

// 当前选择的知识库/数据源
const selectParam = ref('')
// 知识库/数据源列表
const selectList = ref([])
// 获取应用列表
const getAppList = async () => {
  const res = await api.getAppList()
  selectList.value = res || []
  if (route.query.select && selectList.value.includes(route.query.select)) {
    selectParam.value = route.query.select
  } else {
    selectParam.value = selectList.value[0] || ''
  }
}

// 滚动栏实例
const scrollbarRef = ref()
// 消息列表实例
const messageListRef = ref()
// 消息列表滚动至指定位置(不传值则滚动至底部)
const scrollTo = async (num) => {
  // 需要通过nextTick等待DOM更新完成
  await nextTick()
  if (messageListRef.value) {
    // 需要等待echarts全部加载完毕
    setTimeout(() => {
      const max = messageListRef.value.clientHeight
      scrollbarRef.value.setScrollTop(isNaN(num) ? max : max - num)
    })
  }
}
// 滚动事件
const handleScroll = (obj) => {
  if (obj.scrollTop === 0 && (pageNum * pageSize < pageTotal)) {
    pageNum++
    getMessageList()
  }
}

// 历史记录分页相关
let pageNum = 1
let pageSize = 6
let pageTotal = 0
const viewLoading = ref(false)
const chatLoading = ref(false)
// 消息列表
const messageList = ref([])
// 获取消息列表
const getMessageList = async () => {
  if (!route.query.id) {
    return
  }
  viewLoading.value = pageNum === 1
  chatLoading.value = pageNum !== 1
  const oldH = messageListRef.value && messageListRef.value.clientHeight
  try {
    const res = await api.getMessageList({
      convUid: route.query.id,
      pageNum,
      pageSize
    })
    viewLoading.value = false
    chatLoading.value = false
    if (res?.list?.length) {
      const list = res.list.filter(item => {
        if (item.type === 'human' || item.type === 'view') {
          // 判断返回的消息是否为JSON字符串，是则以data形式赋值，否则不做处理
          try {
            const data = JSON.parse(item.messageDetail)
            if (!Array.isArray(data.data)) {
              data.data = []
            }
            if (data.message) {
              item.messageDetail = data.message
            }
            item.chartData = data
            return true
          } catch (e) {
            return true
          }
        } else {
          return false
        }
      })
      // selectPrompt.value = list[list.length - 1]?.promptId // 历史最后一次prompt记录赋值给当前
      selectSystemPrompt.value = list[list.length - 1]?.sysPromptId
      selectAppPrompt.value = list[list.length - 1]?.appPromptId
      selectSystemPromptNew.value = list[list.length - 1]?.sysPromptId // 历史最后一次prompt记录赋值给当前
      selectAppPromptNew.value = list[list.length - 1]?.appPromptId // 历史最后一次prompt记录赋值给当前
      pageTotal = res.total || 0
      messageList.value = [
        ...list,
        ...messageList.value
      ]
      if (pageNum === 1) {
        scrollTo()
      } else {
        scrollTo(oldH)
      }
    }
  } catch (e) {
    viewLoading.value = false
    chatLoading.value = false
  }
}

const showWarning = ref(true)
const sendLoading = ref(false)
// 输入框文本
const userInput = ref('')
// 获取AI回复
const getAIResponse = (isNew) => {
  sendLoading.value = true
  const userInputVal = userInput.value
  userInput.value = ''
  const roundIndex = messageList.value.length ? messageList.value[messageList.value.length - 1].roundIndex + 1 : 1
  messageList.value.push({
    messageDetail: userInputVal,
    modelName: modelName.value,
    roundIndex,
    type: 'human'
  }, {
    messageDetail: '',
    modelName: modelName.value,
    roundIndex,
    type: 'view'
  })
  scrollTo()

  const params = {
    chatMode: route.query.scene === 'chat_mct_demo' ? 'chat_with_data' : route.query.scene,
    // convUid: route.query.id,
    convUid: newID.value ? newID.value : route.query.id,
    modelName: modelName.value,
    selectParam: selectParam.value,
    userInput: userInputVal,
    isAddHistory: 0, // 8.29 yueqi：暂时关闭多轮会话
    sysPromptId: selectSystemPromptNew.value,
    appPromptId: selectAppPromptNew.value
  }
  fetchChat(params, isNew)
}
// 聊天流式请求
const fetchChat = (params, isNew) => {
  const ctrl = new AbortController()
  try {
    fetchEventSource(
      `${import.meta.env.VITE_APP_BASE_API_URL}/api/v1/chat/completionsV1`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: getToken()
        },
        body: JSON.stringify(params),
        signal: ctrl.signal,
        openWhenHidden: true,
        onmessage(event) {
          // 判断返回的消息是否为JSON字符串，是则以data形式赋值，否则以knowledge形式赋值
          try {
            const data = JSON.parse(event.data)
            // 开发环境下打印日志方便查看问题
            if (import.meta.env.VITE_APP_MODE !== 'PROD') {
              console.log(data, '处理后的数据')
            }
            if (!('isEnd' in data) && !Array.isArray(data.data)) {
              data.data = []
            }
            if (data.message) {
              messageList.value[messageList.value.length - 1].messageDetail = data.message
            }
            // messageList.value[messageList.value.length - 1].messageDetail = data.aitext
            if(!('isEnd' in data)) {
              // messageList.value[messageList.value.length - 1].chartData = data
              if (route.path === '/chatPbi') {
                data.changeVisualType = aiPbiType(params.userInput)
                messageList.value[messageList.value.length - 1].chartData = data
              } else {
                messageList.value[messageList.value.length - 1].chartData = data
              }
            }

            loginRedirect(data.message)
          } catch (e) {
            messageList.value[messageList.value.length - 1].messageDetail = event.data
          }
          scrollTo()
        },
        onclose() {
          ctrl.abort()
          if (isNew) {
            user.setChatList(1)
          } else if (route.query.select !== selectParam.value) {
            // 流式接口请求成功的情况下更新用户最新选择的数据源/知识库
            const queryId = route.query.id || getLocalStorageId()
            user.setChatParam(queryId, selectParam.value)
            router.replace({
              path: '/chatNow',
              query: {
                scene: route.query.scene,
                select: selectParam.value,
                id: queryId
              }
            })
          }
          sendLoading.value = false
        },
        onerror(error) {
          messageList.value[messageList.value.length - 1].messageDetail = t('CHAT.CHAT_ERROR')
          sendLoading.value = false
          throw new Error(error)
        }
      }
    )
  } catch (e) {
    ctrl.abort()
    messageList.value[messageList.value.length - 1].messageDetail = t('CHAT.CHAT_ERROR')
    sendLoading.value = false
    throw new Error(e)
  }
}
// 识别用户输入的问题是否包含关键字词
const aiPbiType = (params) => {
  if (params.includes('条形图') || params.includes('条状图')) {
    return 'barChart'
  } else if (params.includes('柱形图') || params.includes('柱图')) {
    return 'columnChart'
  } else if (params.includes('饼图') || params.includes('饼状图')) {
    return 'pieChart'
  } else if (params.includes('卡片图') || params.includes('多行卡')) {
    return 'multiRowCard'
  } else {
    return 'tableEx'
  }
}
// 是否为新的对话Id
let isNewId = false
// 创建新的对话
const getNewChatId = async () => {
  viewLoading.value = true
  try {
    const res = await api.getNewChatId({ chatMode: route.query.scene })
    viewLoading.value = false
    if (res) {
      isNewId = true
      localStorage.setItem('currentId',res)
      // router.replace({
      //   path: '/chatNow',
      //   query: {
      //     scene: route.query.scene,
      //     select: selectParam.value,
      //     id: res
      //   }
      // }).then(() => {
      //   getAIResponse(true)
      // })
      newID.value = res
      getAIResponse(true)
    }
  } catch (e) {
    viewLoading.value = false
  }
}
// 发送信息
const sendMessage = () => {
  if (sendLoading.value === true) {
    return
  }
  if (!userInput.value) {
    ElMessage.warning(t('CHAT.EMPTY_WARNING'))
    return
  }
  if (route.query.id || newID.value) {
    getAIResponse()
  } else {
    getNewChatId()
  }
}
// 输入框回车事件
const handleInputEnter = (e) => {
  if (!e.altKey && !e.ctrlKey && !e.metaKey && !e.shiftKey) {
    e.returnValue = false
    sendMessage()
  }
}

const feedbackLoading = ref(false)
// 评分表单数据
const reviewForm = ref({
  quesType: '',
  score: 0,
  messages: ''
})
// 获取当前评分
const getTheFeedback = async (roundIndex) => {
  feedbackLoading.value = true
  reviewForm.value = {
    quesType: '',
    score: 0,
    messages: ''
  }
  try {
    const res = await api.getTheFeedback({
      convUid: route.query.id || newID.value,
      // convUid: route.query.id,
      roundIndex
    })
    feedbackLoading.value = false
    if (res) {
      const { quesType, score, messages } = res
      reviewForm.value = {
        quesType,
        score,
        messages
      }
    }
  } catch (e) {
    feedbackLoading.value = false
  }
}
// 提交评分
const commitFeedback = async (roundIndex, index) => {
  feedbackLoading.value = true
  try {
    const param = {
      convUid: route.query.id || newID.value,
      roundIndex,
      knowledgeSpace: selectParam.value,
      question: messageList.value[index - 1].messageDetail,
      ...reviewForm.value
    }
    const res = await api.commitFeedback(param)
    feedbackLoading.value = false
    if (res) {
      ElMessage.success(`${t('COMMON.SUBMIT')} ${t('COMMON.SUCCESS')}`)
      messageList.value[index].feedbackVisible = false
    } else {
      ElMessage.warning(`${t('COMMON.SUBMIT')} ${t('COMMON.FAIL')}`)
    }
  } catch (e) {
    feedbackLoading.value = false
  }
}

// 选择的提示语ID
let selectSystemPrompt = ref()
let selectAppPrompt = ref()
let selectSystemPromptNew = ref()
let selectAppPromptNew = ref()
// 提示语列表
const systemPromptList = ref([])
const appPromptList = ref([])

const systemPromptListNew = ref([])
const appPromptListNew = ref([])
// 提示语选中的Tab
const promptTabName = ref('application')
// 获取提示语列表
const getPromptList = async () => {
  const res = await api.getPromptList({
    chatScene: route.query.scene,
    promptType: promptTabName.value // application: 应用提示语, system: 系统提示语【只有itadmin可见】
  })
  if (promptTabName.value === 'application') {
    appPromptList.value = res || []
  } else {
    systemPromptList.value = res || []
  }
}
const getAllPromptList = async () => {
  let applicationRes = []
  let systemRes = []
  const res = await api.getPromptList({
    chatScene: route.query.scene,
    promptType: '' // application: 应用提示语, system: 系统提示语【只有itadmin可见】
  })
  res.forEach(element => {
    if(element.promptType === "application") {
      applicationRes.push(element)
    }
    if(element.promptType === "system") {
      systemRes.push(element)
    }
  })
  appPromptListNew.value = applicationRes
  systemPromptListNew.value = systemRes
}

watch(
  () => route.query.id,
  () => {
    showWarning.value = true
    if (isNewId) {
      isNewId = false
    } else {
      if (route.query.scene === 'chat_with_scene') {
        getAppList()
      }
      pageNum = 1
      pageTotal = 0
      messageList.value = []
      getMessageList()
    }
    getAllPromptList()
  },
  { immediate: true }
)

const getLocalStorageId = () => localStorage.getItem('currentId')

const dialogTitle = computed(() => {
  const title = t('CHAT.DIALOG_WINDOW')
  return route.query.scene === 'chat_with_scene' ? `${title} [${route.query.select}]` : title
})
</script>

<template>
  <div class="chat-title">
    <p>{{ dialogTitle }}</p>
  </div>
  <div class="chat-wrap">
    <div class="chat-view" v-loading="viewLoading">
      <div class="chat-view-layout">
        <el-scrollbar ref="scrollbarRef" class="chat-content" @scroll="handleScroll">
          <div v-if="chatLoading" class="chat-content-loading" v-loading="chatLoading" />
          <ul v-if="messageList.length" ref="messageListRef" class="chat-content-list">
            <li v-for="(item, index) in messageList" :key="`${item.roundIndex}-${item.type}`"
              :class="['chat-content-item', item.type]">
              <div style="display: flex;position:relative;">
                <i v-if="item.type === 'human'" class="iconfont icon-yonghu1 chat-human-icon"></i>
                <el-icon v-if="item.type === 'view' || item.type === 'welcome' || item.type === 'agentWelcome'"
                  class="chat-ai-icon">
                  <img src="./imgs/ai.png" alt="">
                </el-icon>
                <div class="welcome-wrap" style="width: calc(100% - 40px);">
                  <markdown-render v-if="item.type === 'human' || (item.type === 'view' && item.chartData?.message)"
                    :context="item.messageDetail" />
                  <pbi-render v-if="item.chartData && route.path === '/chatPbi'" :chartData="item.chartData" />
                </div>
              </div>
              <div v-if="item.type === 'view'" class="chat-operation">
                <div style="display: flex">
                  <el-tooltip :content="$t('COMMON.COPY')" placement="top">
                    <el-button class="chat-operation-button" size="large" :icon="CopyDocument" text
                      @click="copyToClipboard(item.messageDetail)" />
                  </el-tooltip>
                  <el-popover v-model:visible="item.feedbackVisible" placement="bottom" :width="320" trigger="click"
                    @before-enter="getTheFeedback(item.roundIndex)">
                    <template #reference>
                      <div>
                        <el-tooltip :content="$t('CHAT.RATING')" placement="top">
                          <el-button class="chat-operation-button" size="large" :icon="MoreFilled" text />
                        </el-tooltip>
                      </div>
                    </template>
                    <div class="chat-review" v-loading="feedbackLoading">
                      <el-form :model="reviewForm" label-width="75px" size="large">
                        <el-form-item :label="$t('CHAT.QA_CATEGORY')" prop="quesType">
                          <el-select v-model="reviewForm.quesType" :placeholder="$t('COMMON.PLS_SELECT')"
                            :teleported="false">
                            <el-option v-for="item in user.feedbackSelect" :key="item" :label="item" :value="item" />
                          </el-select>
                        </el-form-item>
                        <el-form-item :label="$t('CHAT.QA_RATING')" prop="score">
                          <el-rate v-model="reviewForm.score" clearable />
                        </el-form-item>
                        <el-form-item prop="messages" label-width="0">
                          <el-input v-model="reviewForm.messages" type="textarea" :rows="3" resize="none"
                            :placeholder="$t('COMMON.PLS_INPUT')" />
                        </el-form-item>
                        <el-button class="chat-review-btn" @click="commitFeedback(item.roundIndex, index)">
                          {{ $t('COMMON.SUBMIT') }}
                        </el-button>
                      </el-form>
                    </div>
                  </el-popover>
                </div>
              </div>
            </li>
          </ul>
          <el-empty v-else :description="$t('CHAT.START_CHAT')" />
        </el-scrollbar>
        <div v-if="user.scenes.includes(route.query.scene)" class="chat-view-input">
          <div class="input-layout">
            <el-input v-model="userInput" type="textarea" :placeholder="$t('CHAT.START_CHAT')"
              :autosize="{ minRows: 2, maxRows: 4 }" resize="none" @keydown.enter="handleInputEnter" />
          </div>
          <el-button class="send-button" size="large" text :loading="sendLoading" @click="sendMessage()">
            <img class="send-img" src="./imgs/send.png" alt="send">
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.chat-wrap {
  display: flex;
  flex: 1;
  height: 0;
  border-radius: 12px;
  background-color: #fff;
}

.chat-view {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 0;
  height: 100%;

  .chat-view-top {
    display: flex;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-bottom: 1px solid #f3f4f6;
  }

  .chat-view-layout {
    display: flex;
    flex-direction: column;
    //flex: 1;
    //height: calc(100vh - 49px);
    height: calc(100vh - 100px);

    .chat-content {
      flex: 1;
      padding: 0 30px;

      .chat-content-loading {
        height: 50px;
      }

      .chat-content-list {
        color: #0f172a;
        font-size: 16px;
        line-height: 28px;
        padding-bottom: 32px;

        .welcome-placeholder {
          height: 24px;
        }

        .chat-content-item {
          padding: 24px 16px;
          border-radius: 12px;
          overflow-wrap: break-word;

          .chat-ai-icon {
            width: 36px;
            height: 36px;
            margin-top: 4px;
            margin-right: 10px;
            background-color: #568CF4;
            border-radius: 4px;

            img {
              transform: scale(0.5);
            }
          }
        }

        .chat-content-item.human {
          :deep(.markdown-render p) {
            float: right;
            border-radius: 12px;
            border-top-right-radius: 0;
            background-color: #DDE8FF;
            padding: 4px 12px;
            margin-left: 45px;
            // text-align: right;
            font-size: 12px;
            color: #0A121F;
          }

          .chat-human-icon {
            position: absolute;
            right: -5px;
            top: 1px;
            width: 36px;
            height: 36px;
            border-radius: 4px;
            background: linear-gradient(129deg, #EBF0FF 0%, #DEECFF 49%, #E7E4FF 100%);
            color: #568CF4;
            font-size: 22px;
            text-align: center;
            line-height: 36px;
          }
        }

        .chat-content-item.view {
          // background: #f1f5f9;
          .chat-knowladge-wrap {
            display: inline-flex;
            margin: 2px 40px 0 0;
            border-radius: 12px;
            border-top-left-radius: 0;
            background-color: #F5F8FF;
            font-size: 12px;
            color: #0A121F;
            .markdown-render {
              padding: 4px 12px;
            }
          }
          .chat-operation {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            border-top: 1px solid #e5e7eb;
            margin-top: 16px;
            padding-top: 16px;

            .chat-operation-button {
              font-size: 16px;
              padding: 12px;
            }
          }
        }

        .chat-content-item.welcome {

          // background: #f1f5f9;
          .welcome-wrap {
            padding: 24px 16px;
            background-color: #F5F8FF;
            border-radius: 12px;
            border-top-left-radius: 0;
          }

          .welcome-advices {
            display: flex;
            flex-wrap: wrap;
            padding-bottom: 8px;

            .el-tag {
              cursor: pointer;
              margin: 12px 12px 0 0;
              font-size: 14px;
            }
          }
        }
      }

      .el-empty {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
      }
    }

    .chat-view-input {
      display: flex;
      align-items: flex-end;
      padding: 24px 48px 40px;
      position: relative;

      .input-layout {
        flex: 1;
        margin: 0 8px;

        .input-warning {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-left: 16px;
          background: #eeeeee;
          border-radius: 4px;

          .input-warning-text {
            padding: 8px 0;
            line-height: 1.5;
          }

          .input-warning-close {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            font-size: 30px;
            font-weight: lighter;
            cursor: pointer;
          }
        }

        :deep(.el-textarea__inner) {
          font-size: 16px;
          // padding: 8px 11px;
          height: auto;
        }

        :deep(.el-textarea__inner::placeholder) {
          font-size: 12px;
          color: #ACB2BB;
          line-height: 24px;
        }
      }

      .send-button {
        position: absolute;
        right: 63px;
        bottom: 40px;
        font-size: 18px;
        padding: 0;

        &:hover {
          background-color: transparent;
        }

        .send-img {
          width: 24px;
          height: 24px;
        }
      }

      &::after {
        content: '';
        width: 100%;
        height: 32px;
        position: absolute;
        top: -32px;
        right: 0;
        background-image: linear-gradient(to top, #fff, transparent);
      }
    }
  }

  // .chat-content-list {
  //   .human-item {
  //     display: flex;
  //     justify-content: flex-end;
  //   }
  // }

  .prompt-button {
    // display: none;
    position: absolute;
    bottom: 128px;
    right: 30px;
    width: 40px;
    height: 40px;

    .el-button {
      font-size: 18px;
      width: 40px;
      height: 40px;
      box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    }
  }
}

// .welcome-wrap {
//   background-color: #F5F8FF;
//   border-radius: 12px;
//   border-top-left-radius: 0;
//   padding: 10px;
// }
.agent-welcome {
  .welcome-text-agent {
    font-size: 12px;
  }

  .advices-title {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    font-weight: bold;

    .btn-refresh {
      color: #3C78EC;
    }
  }

  .agent-tag {
    background-color: #fff;
    border: none;
    color: #4B556A;
    margin-top: 10px;
    margin-right: 10px;
    cursor: pointer;
  }
}


.chat-review {
  .chat-review-btn {
    width: 100%;
    margin-top: 12px;
  }
}

.prompt-inner {
  .prompt-inner-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .prompt-inner-label {
      width: 100px;
      font-weight: bold;
      color: #000000;
    }

    .el-select {
      flex: 1;
    }
  }

  .prompt-inner-content {
    margin: 0 -12px;

    :deep(.el-scrollbar__wrap) {
      max-height: 400px;

      .prompt-inner-list {
        padding: 0 12px;

        .prompt-inner-item {
          padding: 12px;
          line-height: 1.5;
          border-bottom: 1px solid rgba(5, 5, 5, 0.06);
          cursor: pointer;

          &.active {
            background: #e7ecf9;
          }

          &:hover {
            background: #e7ecf9;
          }

          .prompt-inner-item-title {
            color: rgba(0, 0, 0, 0.88);
          }

          .prompt-inner-item-desc {
            color: rgba(0, 0, 0, 0.45);
          }
        }

        .prompt-inner-item:last-child {
          border: none;
        }
      }
    }
  }
}

.chat-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // height: 50px;
  padding: 9px 10px;
  border-bottom: 1px solid #E5E7ED;
  font-size: 14px;
  color: #0A121F;
  letter-spacing: 1px;
  min-height: 50px;

  .title-right {
    display: flex;

    .chat-view-top {
      margin-right: 10px;
      span {
        margin-right: 5px;
        font-size: 12px;
      }
    }

    :deep(.el-input__inner::placeholder) {
      font-size: 12px;
    }
    .prompt-wrap {
      display: flex;
    }
    .prompt-select-wrap {
      margin-right: 10px;
      &:last-child {
        margin-right: 0;
      }
      span {
        margin-right: 5px;
        font-size: 12px;
      }
    }
    .el-select {
      width: 180px;
    }
  }
}

.chat-main {
  flex: 1;
  height: 0;
}

.datasource-select-option {
  margin: 0 7px;

  &.el-select-dropdown__item:hover {
    border-radius: 4px;
    background: #F0F5FF;
  }
}
</style>
