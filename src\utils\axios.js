import axios from 'axios'
import router from '@/router'
import { getToken, removeUserInfo } from './auth'
import { ElMessage } from 'element-plus'

// http响应大状态码提示信息
const DefaultResMsgObj = {
  status40001: '该报表不存在或已失效，请查看或使用其他报表',
  status40002: '针对自助化报表报错时走相同逻辑却要不同处理的特殊处理',
  status400: '入参格式不对',
  status401: '请重新登录',
  status404: '网络请求不存在',
  status502: '服务不可用-网关错误',
  status504: '服务超时',
  statusOther: 'ERROR',
  status30001: '用户名或密码错误',
  status30002: '验证码错误，请重试',
  status30003: '验证码失效，请重试',
  status30004: '当前用户名无权访问，请先获得授权'
}
// 响应错误处理方法
const ErrorHandle = (status, other) => {
  switch (status) {
  // 接口超时，前端控制
  // 自助化报表pbi报错提示专门设置
  case 40001:
    ElMessage.warning(other || DefaultResMsgObj.status40001)
    break
  case 40002:
    break
  case 40004:
    break
  // 入参不对
  case 400:
    ElMessage.warning(other || DefaultResMsgObj.status400)
    break
  // 401: 未登录/token过期-则跳转登录页面。清除token并携带当前页面的路径
  case 401:
    removeUserInfo()
    // 401无权限跳转登录
    if (location.href.indexOf('redirectUrl') < 0) {
      // 如果没跳转过登录，先跳转adfs登录
      const redirectUrl = encodeURIComponent(location.href)
      location.href = `${location.origin}/adfs/login?redirectUrl=${redirectUrl}`
    } else {
      // 如果无法进行adfs登录，则重定向到登录页
      router.replace({
        path: '/login',
        query: {
          redirect: router.currentRoute.fullPath
        }
      })
    }
    break
  // 404: 请求不存在
  case 404:
    ElMessage.error(DefaultResMsgObj.status404)
    break
  // 502: 服务不可用-网关错误
  case 502:
    ElMessage.warning(DefaultResMsgObj.status502)
    break
  // 504: 服务超时
  case 504:
    ElMessage.warning(DefaultResMsgObj.status504)
    break
  // 30001: 用户名秘密错误
  case 30001:
    ElMessage.warning(DefaultResMsgObj.status30001)
    break
  // 30002: 验证码错误
  case 30002:
    ElMessage.warning(DefaultResMsgObj.status30002)
    break
  // 30003: 验证码过期
  case 30003:
    ElMessage.warning(DefaultResMsgObj.status30003)
    break
  // 30004: 无权限
  case 30004:
    ElMessage.warning(DefaultResMsgObj.status30004)
    break
  // 其他错误直接抛出错误信息
  default:
    ElMessage.error(other)
  }
}

const axiosService = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API_URL,
  timeout: 30000,
  withCredentials: false // 跨域请求允许携带cookie
})

// 添加请求拦截器
axiosService.interceptors.request.use((config) => {
  // 在发送请求之前做些什么
  if (getToken()) {
    config.headers['Authorization'] = getToken() // 让每个请求携带自定义token
  }
  return config
}, (error) => {
  // 对请求错误做些什么
  return Promise.reject(error)
})

// 添加响应拦截器
axiosService.interceptors.response.use((response) => {
  // noInlayerData，自定义参数，没有内存data时需要设置成true
  const { noInlayerData } = response.config
  if (noInlayerData) {
    if (response.status === 200 || response.status === 201) {
      return Promise.resolve(response)
    } else {
      ErrorHandle(response.status, response.msg)
      return Promise.reject(response)
    }
  } else {
    const resData = response.data
    // 暂时分200；非200
    if (resData.status === 200 || resData.status === 201) {
      return Promise.resolve(resData.data)
    } else if (resData.status === 30003) {
      ElMessage.error(DefaultResMsgObj.status30003)
      return Promise.resolve('refresh') // 重新获取验证码
    } else if (resData.status === 40002 || resData.status === 40003 || resData.status === 40004) {
      return Promise.resolve(resData)
    } else {
      ErrorHandle(response.data.status, response.data.msg)
      return Promise.reject(response)
    }
  }
}, (error) => {
  // 响应错误-服务器状态码不是200的情况
  console.log(error, '响应错误')
  if (error.response) {
    ErrorHandle(error.response.status, error.response.data.msg)
    return Promise.reject(error)
  } else {
    // 断网
    ElMessage.error(DefaultResMsgObj.statusOther)
    console.log('No network connection')
    return Promise.reject(error)
  }
})

export default axiosService
