<script setup>
import { View, Plus, Minus } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import { ref, computed } from 'vue'
import { ElLoading, ElMessageBox, ElMessage } from 'element-plus'
import * as api from './api'

const { locale, t } = useI18n()

// 声明props
const props = defineProps({
  filterList: Object,
  appData: Object,
  onlyRead: Boolean
})
// agent类型
const agentTypeList = [{
  label: 'Data Scientist',
  value: 'DataScientist'
}, {
  label: 'Summarizer',
  value: 'Summarizer'
}, {
  label: 'Tool Expert',
  value: 'Tool'
}, {
  label: 'Insight',
  value: 'Insight'
}]
// 接收传递的方法
const emit = defineEmits(['getAppList'])

// 数据源列表
const dbList = ref([])
// 知识库列表
const knowledgeList = ref([])
// Data Source类型提示语列表
const promptList = ref([])
// 模型数据列表
const modelList = ref([])
// 插件列表数据
const pluginList = ref([])
// 用户列表
const userList = ref([])
// 获取数据源列表
const getDbList = async () => {
  const res = await api.getDbList({})
  dbList.value = res || []
}
// 获取知识库列表
const getKnowledgeList = async () => {
  const res = await api.getKnowledgeList({})
  knowledgeList.value = res || []
}
// 获取Data Source类型提示语列表
const getPromptList = async () => {
  const res = await api.getPromptList({
    // chatScene: 'chat_with_data',
    // chatScene: 'chat_with_knowledge',
    promptType: 'application'
  })
  promptList.value = res || []
}
// 获取用户列表
const getUserList = async () => {
  const res = await api.getUserList()
  userList.value = res || []
}
// 获取模型列表
const getModelList = async () => {
  const res = await api.getModelList()
  modelList.value = res || []
}
// 获取插件列表
const getPluginList = async () => {
  const res = await api.getPluginList()
  pluginList.value = res || []
}

// 是否显示对话框
const dialogVisible = ref(false)
// 选择应用类型对话框
const dialogApplicationType = ref(false)
// 应用类型 1 原生 2 多个
const activeType = ref('')
// 对话框标题
const dialogTitle = computed(() => {
  return props.onlyRead ? t('APP.VIEW_APP') : props.appData.id ? t('APP.EDIT_APP') : t('APP.CREATE_APP')
})

// 应用表单数据
const appObj = {
  id: undefined,
  sceneName: '',
  sceneType: '',
  appType: '',
  model: '',
  agentSettingList: [{
    agentType: '',
    promptData: {},
    DataResource: '',
    promptKnow: {},
    knowledgeResource: '',
    promptInsight: {},
    insightResource: '',
    plugin: []
  }],
  // bu: '',
  // bg: '',
  // kpiCategory: '',
  // kpi: [],
  connectId: null,
  promptId: null,
  insightPrompt: {},
  insightKnowledge: '',
  insightModel: '',
  comment: '',
  recommendQuestionList: [''],
  sceneOwner: []
}
const appForm = ref(JSON.parse(JSON.stringify(appObj)))
const appRules = computed(() => {
  const res = {
    sceneName: [{ required: true, message: t('COMMON.PLS_INPUT'), trigger: 'blur' }],
    sceneType: [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }]
  }
  if(activeType.value === 1) {
    res.model = [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }]
    res.promptId = [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }]
    res.connectId = [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }]
  }
  if(activeType.value === 2) {
    res.model = [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }]
    res.agentSettingList = {
      DataResource: [{required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change'}],
      plugin: [{required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change'}]
    }
  }
  return res
})
// 智能体类型下拉数据过滤-已选禁用
const agentTypeFilterList = computed(() => {
  const res = JSON.parse(JSON.stringify(agentTypeList))
  const seled = appForm.value.agentSettingList.map(element => element.agentType)
  seled.forEach(element => {
    res.forEach(type => {
      if(type.value === element) {
        type.disabled = true
      }
    })
  })
  return res
})
// 处理后端返回多智能体配置信息为前端使用格式
const formatMutiCfg = (data) => {
  const res = []
  data.details.forEach(item => {
    if(item.agentName === "DataScientist") {
      res.push({
        agentType: item.agentName,
        promptData: {
          id: item.promptId,
          promptName: item.promptName
        },
        promptKnow: {},
        DataResource: item.resources.length ? item.resources[0].value : '',
        knowledgeResource: '',
        plugin: [],
        promptInsight: {},
        insightResource: '',
      })
    }
    if(item.agentName === "Summarizer") {
      res.push({
        agentType: item.agentName,
        promptData: {},
        promptKnow: {
          id: item.promptId,
          promptName: item.promptName
        },
        DataResource: '',
        knowledgeResource: item.resources.length ? item.resources[0].value : '',
        plugin: [],
        promptInsight: {},
        insightResource: '',
      })
    }
    if(item.agentName === "Tool") {
      res.push({
        agentType: item.agentName,
        promptData: {},
        promptKnow: {},
        DataResource: '',
        knowledgeResource: '',
        plugin: item.resources.map(res => res.value),
        promptInsight: {},
        insightResource: '',
      })
    }
    if(item.agentName === "Insight") {
      res.push({
        agentType: item.agentName,
        promptData: {},
        promptKnow: {},
        promptInsight: {
          id: item.promptId,
          promptName: item.promptName
        },
        DataResource: '',
        knowledgeResource: '',
        insightResource: item.resources.length ? item.resources[0].value : '',
        plugin: []
      })
    }
  })
  return res
}
// 填充数据
const resetData = async (isOpen) => {
  if (isOpen) {
    let defaultMultiCfg = []
    if(props.appData.appCode) {
      // 获取智能体配置信息
      const res = await api.getMutiAppConfig({
        appCode: props.appData.appCode
      })
      if(res !== null) {
        if(props.appData.appType === 'mutli-agent') {
          defaultMultiCfg = formatMutiCfg(res)
        }
        if(props.appData.appType === 'native') {
          props.appData.insightPrompt = {
            id: res.details[0].promptId,
            promptName: res.details[0].promptName
          }
          props.appData.insightKnowledge = res.details[0].resources[0].value
          props.appData.insightModel = res.details[0].model || ''
        }
      }else {
        console.log('关联appcode为null')
      }
    }else {
      console.log('该应用无appCode')
    }
    appForm.value = {
      id: props.appData.id || undefined,
      sceneName: props.appData.sceneName || '',
      sceneType: props.appData.sceneType || '',
      model: props.appData.model || '',
      appType: activeType.value === 2 ? 'mutli-agent' : 'native',
      agentSettingList: defaultMultiCfg.length ? defaultMultiCfg : [{
        agentType: '',
        promptData: {},
        DataResource: '',
        promptKnow: {},
        knowledgeResource: '',
        plugin: [],
        promptInsight: {},
        insightResource: ''
      }],
      // bu: props.appData.bu || '',
      // bg: props.appData.bg || '',
      // kpiCategory: props.appData.kpiCategory || '',
      // kpi: props.appData.kpi || [],
      connectId: props.appData.connectId ? {id: props.appData.connectId, dbBusiName: props.appData.connectName} : null,
      promptId: props.appData.promptId ? {id: props.appData.promptId, promptName: props.appData.promptName} : null,
      // connectId: props.appData.connectId,
      // promptId: props.appData.promptId || '',
      insightPrompt: props.appData.insightPrompt || {},
      insightKnowledge: props.appData.insightKnowledge || '',
      insightModel: props.appData.insightModel || '',
      comment: props.appData.comment || '',
      recommendQuestionList: props.appData.recommendQuestionList || [''],
      sceneOwner: props.appData.sceneOwner ? props.appData.sceneOwner.split(',') : [],
    }
  } else {
    appForm.value = JSON.parse(JSON.stringify(appObj))
  }
}
// 查看所选提示语内容
const showPrompt = (obj) => {
  const promptId = appForm.value.promptId?.id || obj.id
  if (promptId) {
    const content = promptList.value.find(x => x.id === promptId).content
    ElMessageBox.alert(content)
  }
}
// 添加推荐问题
const addRecQuestion = () => {
  appForm.value.recommendQuestionList.push('')
}
// 删除推荐问题
const delRecQuestion = (index) => {
  appForm.value.recommendQuestionList.splice(index, 1)
}
// 处理多智能体配置项参数为后端接收格式
const formatMutiParam = () => {
  return appForm.value.agentSettingList.map(item => {
    if(item.agentType === "DataScientist") {
      return {
        agentType: "DataScientist",
        resourceList: [{
          name: item.DataResource,
          type: "database",
          value: item.DataResource
        }],
        promptId: item.promptData?.id,
        promptName: item.promptData?.promptName
      }
    }
    if(item.agentType === "Summarizer") {
      return {
        agentType: "Summarizer",
        resourceList: [{
          name: item.knowledgeResource,
          type: "knowledge",
          value: item.knowledgeResource
        }],
        promptId: item.promptKnow?.id,
        promptName: item.promptKnow?.promptName
      }
    }
    if(item.agentType === "Insight") {
      return {
        agentType: "Insight",
        resourceList: [{
          name: item.insightResource,
          type: "insight",
          value: item.insightResource
        }],
        promptId: item.promptInsight?.id,
        promptName: item.promptInsight?.promptName
      }
    }
    if(item.agentType === "Tool") {
      return {
        agentType: "Tool",
        resourceList: item.plugin.map(name => ({
          name: name,
          type: "tool",
          value: name
        }))
      }
    }
  })
}
// 保存应用
const saveApp = async () => {
  const params = {
    ...appForm.value,
    sceneOwner: appForm.value.sceneOwner.join(',')
  }
  if(activeType.value === 1) {
    // delete params.agentSettingList
    // 处理数据源参数
    const connectObj = params.connectId
    if(connectObj) {
      params.connectId = connectObj.id
      params.connectName = connectObj.dbBusiName
    }
    // 处理提示词参数
    const promptObj = params.promptId
    if(promptObj) {
      params.promptId = promptObj.id
      params.promptName = promptObj.promptName
    }
    // insight 相关参数格式化 params.agentSettingList
    params.agentSettingList = [
      {
        "resourceList": [
          {
            "name": params.insightKnowledge,
            "type": "knowledge",
            "value": params.insightKnowledge
          }
        ],
        "promptId": params.insightPrompt?.id,
        "promptName": params.insightPrompt?.promptName,
        "model": params.insightModel
      }
    ]
  }
  if(activeType.value === 2) {
    // 去除不必要参数 带上会报错
    delete params.connectId
    delete params.promptId
    const settings = formatMutiParam()
    // 过滤空配置项
    const agentSettings = settings.filter(item => item)
    if(agentSettings.length === 0) {
      ElMessage({
        type: 'warning',
        message: t('APP.AGENT_CONFIG')
      })
      return
    }
    params.agentSettingList = agentSettings
    params.appType = 'mutli-agent'
  }

  const loading = ElLoading.service({ target: '.el-dialog' })
  try {
    if (appForm.value.id) {
      await api.updateApp(params)
    } else {
      await api.addApp(params)
    }
    loading.close()
    dialogVisible.value = false
    emit('getAppList')
  } catch (e) {
    loading.close()
  }
}
// 应用表单实例
const appFormRef = ref()
// 提交表单
const submitForm = () => {
  appFormRef.value.validate((valid) => {
    if (valid) {
      saveApp()
    }
  })
}

const handleAgentTypeSelect = type => {
  if(type === 1) {
    activeType.value = 1
  }
  if(type === 2) {
    activeType.value = 2
  }
}
const agentTypeNext = () => {
  if(activeType.value === '') {
    ElMessage({
      message: t('APP.SELECT_APP_TYPE'),
      type: 'warning',
      plain: true
    })
    return false
  }
  dialogVisible.value = true
  dialogApplicationType.value = false
}

const deleteAgentItem = index => {
  appForm.value.agentSettingList.splice(index, 1)
}
const addAgentItem = () => {
  appForm.value.agentSettingList.push({
    agentType: '',
    promptData: {},
    DataResource: '',
    promptKnow: {},
    knowledgeResource: '',
    plugin: [],
    promptInsight: {},
    insightResource: ''
  })
}

getDbList()
getKnowledgeList()
getPromptList()
getUserList()
getModelList()
getPluginList()
// 暴露属性和方法
defineExpose({ dialogVisible, activeType, dialogApplicationType })
</script>

<template>
  <div class="app-dialog">
    <el-dialog
      class="setting-page-dialog-wrap"
      v-model="dialogVisible"
      :title="dialogTitle"
      width="700px"
      :close-on-click-modal="false"
      destroy-on-close
      @open="resetData(true)"
      @closed="resetData(false)"
    >
      <el-form
        ref="appFormRef"
        :model="appForm"
        :rules="appRules"
        label-width="137px"
      >
        <el-form-item prop="sceneName" :label="$t('APP.INPUT_APPNAME')">
          <el-input
            v-model="appForm.sceneName"
            maxlength="30"
            :placeholder="$t('COMMON.PLS_INPUT')"
            :disabled="props.onlyRead || !!props.appData.id"
          />
        </el-form-item>
        <el-form-item prop="sceneType" :label="$t('APP.SELECT_SCENE')">
          <el-select
            v-model="appForm.sceneType"
            :placeholder="$t('COMMON.PLS_SELECT')"
            :disabled="props.onlyRead"
          >
            <el-option
              v-for="item in props.filterList.applyScene"
              :key="item.value"
              :label="item[locale === 'en_US' ? 'nameEn' : 'nameCn']"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="model" :label="$t('APP.MODEL_NAME')">
          <el-select
            v-model="appForm.model"
            :placeholder="$t('COMMON.PLS_SELECT')"
            :disabled="props.onlyRead"
          >
            <el-option
              v-for="item in modelList"
              :key="item.modelName"
              :label="item.modelName"
              :value="item.modelName"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="is-required" v-if="activeType === 2" :label="$t('APP.AGENT_SETTING')">
          <div class="agent-item" v-for="(item, index) in appForm.agentSettingList" :key="index">
            <div class="top-agent-type">
              <el-form-item
                :prop="`agentSettingList.${index}.agentType`"
                :label="$t('APP.AGENT_TYPE')"
                label-width="90"
              >
                <el-select
                  size="small"
                  v-model="appForm.agentSettingList[index].agentType"
                  popper-class="agent-type-dropdown"
                  :disabled="props.onlyRead"
                >
                  <el-option
                    v-for="type in agentTypeFilterList"
                    :key="type.value"
                    :label="type.label"
                    :value="type.value"
                    :disabled="type.disabled"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <div class="right-button" v-if="!props.onlyRead" @click="deleteAgentItem(index)">
                <i class="iconfont icon-circleminus"></i>
              </div>
            </div>
            <!-- appForm.agentSettingList[index] -->
            <div class="agent-config">
              <el-form-item
                :prop="`agentSettingList.${index}.promptData`"
                :label="$t('APP.PROMPT')"
                :label-width="locale === 'en_US' ? 90 : 70"
                v-if="item.agentType === 'DataScientist'"
              >
                <el-select v-model="item.promptData" value-key="id" :disabled="props.onlyRead" filterable clearable>
                  <el-option
                    v-for="prompt in promptList"
                    :key="prompt.id"
                    :label="prompt.promptName"
                    :value="prompt"
                  >
                  </el-option>
                </el-select>
                <el-button :icon="View" @click="showPrompt(appForm.agentSettingList[index].promptData)" />
              </el-form-item>
              <el-form-item
                :prop="`agentSettingList.${index}.promptKnow`"
                :label="$t('APP.PROMPT')"
                :label-width="locale === 'en_US' ? 90 : 70"
                v-if="item.agentType === 'Summarizer'"
              >
                <el-select v-model="item.promptKnow" value-key="id" :disabled="props.onlyRead" filterable clearable>
                  <el-option
                    v-for="prompt in promptList"
                    :key="prompt.id"
                    :label="prompt.promptName"
                    :value="prompt"
                  >
                  </el-option>
                </el-select>
                <el-button :icon="View" @click="showPrompt(appForm.agentSettingList[index].promptKnow)" />
              </el-form-item>
              <el-form-item
                :prop="`agentSettingList.${index}.promptInsight`"
                :label="$t('APP.PROMPT')"
                :label-width="locale === 'en_US' ? 90 : 70"
                v-if="item.agentType === 'Insight'"
              >
                <el-select v-model="item.promptInsight" value-key="id" :disabled="props.onlyRead" filterable clearable>
                  <el-option
                    v-for="prompt in promptList"
                    :key="prompt.id"
                    :label="prompt.promptName"
                    :value="prompt"
                  >
                  </el-option>
                </el-select>
                <el-button :icon="View" @click="showPrompt(appForm.agentSettingList[index].promptInsight)" />
              </el-form-item>
              <el-form-item
                :prop="`agentSettingList.${index}.DataResource`"
                :label="$t('APP.RESOURCE')"
                :rules="appRules.agentSettingList.DataResource"
                :label-width="locale === 'en_US' ? 90 : 70"
                v-if="item.agentType === 'DataScientist'"
              >
                <el-select v-model="item.DataResource" :disabled="props.onlyRead" filterable clearable>
                  <el-option
                    v-for="db in dbList"
                    :key="db.id"
                    :label="db.dbBusiName"
                    :value="db.dbBusiName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                :prop="`agentSettingList.${index}.knowledgeResource`"
                :label="$t('APP.RESOURCE')"
                :label-width="locale === 'en_US' ? 90 : 70"
                v-if="item.agentType === 'Summarizer'"
              >
                <el-select v-model="item.knowledgeResource" :disabled="props.onlyRead" filterable clearable>
                  <el-option
                    v-for="knowledge in knowledgeList"
                    :key="knowledge"
                    :label="knowledge"
                    :value="knowledge"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                :prop="`agentSettingList.${index}.insightResource`"
                :label="$t('APP.RESOURCE')"
                :label-width="locale === 'en_US' ? 90 : 70"
                v-if="item.agentType === 'Insight'"
              >
                <el-select v-model="item.insightResource" :disabled="props.onlyRead" filterable clearable>
                  <el-option
                    v-for="knowledge in knowledgeList"
                    :key="knowledge"
                    :label="knowledge"
                    :value="knowledge"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                :prop="`agentSettingList.${index}.plugin`"
                :label="$t('APP.PLUGIN')"
                :rules="appRules.agentSettingList.plugin"
                :label-width="locale === 'en_US' ? 90 : 70"
                v-if="item.agentType === 'Tool'"
              >
                <el-select v-model="item.plugin" :disabled="props.onlyRead" filterable clearable multiple collapse-tags style="width: 100%;">
                  <el-option
                    v-for="plugin in pluginList"
                    :key="plugin"
                    :label="plugin"
                    :value="plugin"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="add-agent" v-if="!props.onlyRead && appForm.agentSettingList.length < 4">
            <div class="add-btn" @click="addAgentItem">
              <el-icon><Plus /></el-icon>
              {{ $t('APP.ADD_AGENT') }}
            </div>
          </div>
        </el-form-item>
        <!-- <el-row>
          <el-col :span="12">
            <el-form-item prop="bu" label="BU">
              <el-select
                v-model="appForm.bu"
                clearable
                :placeholder="$t('COMMON.PLS_SELECT')"
                :disabled="props.onlyRead"
              >
                <el-option
                  v-for="item in props.filterList.bu"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="bg" label="BG">
              <el-select
                v-model="appForm.bg"
                clearable
                :placeholder="$t('COMMON.PLS_SELECT')"
                :disabled="props.onlyRead"
              >
                <el-option
                  v-for="item in props.filterList.bg"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="kpiCategory" :label="$t('APP.SELECT_KPITYPE')">
              <el-select
                v-model="appForm.kpiCategory"
                clearable
                :placeholder="$t('COMMON.PLS_SELECT')"
                :disabled="props.onlyRead"
                @change="appForm.kpi = []"
              >
                <el-option
                  v-for="(val, key) in props.filterList.kpi"
                  :key="key"
                  :label="key"
                  :value="key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="kpi" label="KPI">
              <el-select
                v-model="appForm.kpi"
                multiple
                collapse-tags
                collapse-tags-tooltip
                clearable
                :placeholder="$t('COMMON.PLS_SELECT')"
                :disabled="props.onlyRead"
              >
                <el-option
                  v-for="item in props.filterList.kpi[appForm.kpiCategory]"
                  :key="item.value"
                  :label="item[locale === 'en_US' ? 'nameEn' : 'nameCn']"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-form-item v-if="activeType === 1" prop="connectId" :label="$t('APP.DATA_SOURCE')">
          <el-select
            v-model="appForm.connectId"
            filterable
            :placeholder="$t('COMMON.PLS_SELECT')"
            value-key="id"
            :disabled="props.onlyRead"
          >
            <el-option
              v-for="item in dbList"
              :key="item.id"
              :label="item.dbBusiName"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="activeType === 1" prop="promptId" :label="$t('APP.PROMPT')">
          <div class="form-item-layout">
            <el-select
              v-model="appForm.promptId"
              filterable
              :placeholder="$t('COMMON.PLS_SELECT')"
              value-key="id"
              :disabled="props.onlyRead"
            >
              <el-option
                v-for="item in promptList"
                :key="item.id"
                :label="item.promptName"
                :value="item"
              />
            </el-select>
            <el-button :icon="View" @click="showPrompt()" />
          </div>
        </el-form-item>
        <!-- 原生应用insight相关配置 -->
        <el-form-item v-if="activeType === 1" prop="insightPrompt" :label="$t('APP.INSIGHT_PROMPT')">
          <el-select
            v-model="appForm.insightPrompt"
            :placeholder="$t('COMMON.PLS_SELECT')"
            value-key="id"
            :disabled="props.onlyRead"
            filterable
            clearable
          >
            <el-option
              v-for="item in promptList"
              :key="item.id"
              :label="item.promptName"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="activeType === 1"
          prop="insightKnowledge"
          :label="$t('APP.INSIGHT_KNOWLEDGE')"
        >
          <el-select
            v-model="appForm.insightKnowledge"
            :placeholder="$t('COMMON.PLS_SELECT')"
            :disabled="props.onlyRead"
            filterable
            clearable
          >
            <el-option
              v-for="knowledge in knowledgeList"
              :key="knowledge"
              :label="knowledge"
              :value="knowledge"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="activeType === 1"
          prop="insightModel"
          :label="$t('APP.INSIGHT_MODEL')"
        >
          <el-select
            v-model="appForm.insightModel"
            :placeholder="$t('COMMON.PLS_SELECT')"
            :disabled="props.onlyRead"
            clearable
          >
            <el-option
              v-for="item in modelList"
              :key="item.modelName"
              :label="item.modelName"
              :value="item.modelName"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="comment" :label="$t('APP.DESC')">
          <el-input
            v-model="appForm.comment"
            type="textarea"
            maxlength="1000"
            :rows="3"
            resize="none"
            :placeholder="$t('COMMON.PLS_INPUT')"
            :disabled="props.onlyRead"
          />
        </el-form-item>
        <el-form-item prop="recommendQuestionList" :label="$t('APP.DEMO_QUESTION')">
          <div
            v-for="(item, index) in appForm.recommendQuestionList"
            :key="index"
            class="form-item-layout"
          >
            <el-input
              v-model="appForm.recommendQuestionList[index]"
              maxlength="100"
              :placeholder="$t('COMMON.PLS_INPUT')"
              :disabled="props.onlyRead"
            />
            <template v-if="!props.onlyRead">
              <el-button
                v-if="appForm.recommendQuestionList.length === index + 1"
                :icon="Plus"
                @click="addRecQuestion()"
              />
              <el-button
                v-else
                :icon="Minus"
                @click="delRecQuestion(index)"
              />
            </template>
          </div>
        </el-form-item>
        <el-form-item prop="sceneOwner" :label="$t('APP.OWNER')">
          <el-select
            v-model="appForm.sceneOwner"
            :placeholder="$t('COMMON.PLS_SELECT')"
            multiple
            collapse-tags
            collapse-tags-tooltip
            filterable
            :reserve-keyword="false"
            :disabled="props.onlyRead"
          >
            <el-option
              v-for="item in userList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">{{ $t('COMMON.CANCEL') }}</el-button>
        <el-button v-if="!props.onlyRead" type="primary" @click="submitForm()">
          {{ $t('COMMON.SUBMIT') }}
        </el-button>
      </template>
    </el-dialog>

    <el-dialog
      class="setting-page-dialog-wrap"
      v-model="dialogApplicationType"
      :title="dialogTitle"
      width="700px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <p>{{ $t('APP.APPLICATION_TYPE') }}</p>
      <div class="agent-type-wrap">
        <div class="card-item native" :class="{active: activeType === 1}" @click="handleAgentTypeSelect(1)">
          <div class="image">
            <i class="iconfont icon-huihuaguanli"></i>
          </div>
          <div class="content">
            <p class="title">{{ $t('APP.NATIVE_APPLICATION') }}</p>
            <p class="desc">
              {{ $t('APP.NATIVR_DESC') }}
            </p>
          </div>
        </div>
        <div class="card-item multi" :class="{active: activeType === 2}" @click="handleAgentTypeSelect(2)">
          <div class="image">
            <i class="iconfont icon-NetworkAlternative"></i>
          </div>
          <div class="content">
            <p class="title">{{ $t('APP.MULTI_AGENT_APPLICATION') }}</p>
            <p class="desc">
              {{ $t('APP.MULTI_DESC') }}
            </p>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="dialogApplicationType=false">{{ $t('COMMON.CANCEL') }}</el-button>
        <el-button type="primary" @click="agentTypeNext">
          {{ $t('COMMON.NEXT') }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
@import url('//at.alicdn.com/t/c/font_4728306_8ii6k6nby9i.css');
.app-dialog {
  :deep(.el-dialog__body) {
    padding: 18px 20px 0;
    .el-select {
      width: 100%;
    }
    .form-item-layout {
      width: 100%;
      display: flex;
      align-items: center;
      .el-button {
        margin-left: 10px;
        padding: 8px 10px;
      }
    }
    .form-item-layout+.form-item-layout {
      margin-top: 18px;
    }
    .agent-config {
      .el-form-item:first-child {
        .el-select {
          width: calc(100% - 50px);
        }
        .el-button {
          margin-left: 10px;
          padding: 8px 10px;
        }
      }
      .el-form-item {
        padding: 15px 10px 0 0;
        &:last-child {
          padding-bottom: 15px;
        }
      }
    }
    .add-agent {
      width: 100%;
      height: 25px;
      margin-top: 5px;
      line-height: 25px;
      border: 1px dashed #bcbcbc;
      border-radius: 5px;
      text-align: center;
      font-size: 12px;
      .add-btn {
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        height: 25px;
        color: #717175;
        .el-icon {
          margin-right: 5px;
        }
      }
      
    }
  }
  .agent-type-wrap {
    display: flex;
    justify-content: space-between;
    margin: 10px 0 10px;
    .card-item {
      display: flex;
      align-items: center;
      width: 340px;
      height: 100px;
      border: 1px solid #eee;
      border-radius: 5px;
      cursor: pointer;
      &.active {
        border-color: #6496F5;
        background-color: #F7F9FE;
      }
      .image {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 60px;
        height: 60px;
        margin-left: 10px;
        .iconfont {
          font-size: 50px;
          color: #5391F5;
        }
      }
      .content {
        height: 100%;
        flex: 1;
        padding: 10px;
        .title {
          margin-top: 15px;
          font-weight: bold;
        }
        .desc {
          font-size: 12px;
          padding: 5px 0;
        }
      }
    }
    .multi {
      margin-left: 20px;
    }
  }
  .agent-item, .top-agent-type {
    width: 100%;
  }
  .agent-item {
    border: 1px solid #eee;
    margin: 5px 0;
  }
  .top-agent-type {
    padding: 0 10px;
    background-color: #F5F5F5;
    display: flex;
    align-items: center;
    position: relative;
    .label {
      width: 80px;
    }
    .el-select {
      width: 200px;
      :deep(.el-input--small) {
        .el-input__inner {
          line-height: 24px;
        }
      }
    }
    .right-button {
      position: absolute;
      right: 15px;
      cursor: pointer;
    }
  }
}
</style>
<style lang="less">
.el-message-box__message {
  p {
    white-space: pre-wrap;
  }
}
.agent-type-dropdown .el-select-dropdown__item.is-disabled {
  color: #c0c4cc;
}
.agent-type-dropdown .el-select-dropdown__item.selected {
  font-weight: 400;
}
</style>
