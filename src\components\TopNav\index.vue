<script setup>
import { ref, watch } from 'vue'
import {useRouter} from 'vue-router'
import {useUserStore} from "@/stores/user";
import {getUsername} from "@/utils/auth"
import {useI18n} from "vue-i18n"
const { locale } = useI18n()

let user = useUserStore()
let router = useRouter()
const goHome = () => {
  router.push('/')
}
const jumpLink = () => {
  window.open('https://myhub.lenovo.com/sites/compliance/corporate_policies/Pages/policy27')
}
const setLocale = () => {
  const lang = localStorage.getItem('lang') === 'en_US' ? 'zh_CN' : 'en_US'
  localStorage.setItem('lang', lang)
  locale.value = lang
}

let headerAvatarImg = ref('')
let letter = getUsername().slice(0, 1).toUpperCase()

watch(() => user.thumbnailPhoto, (newVal) => {
  if (newVal) {
    headerAvatarImg.value = `data:image/png;base64,${newVal}`
  }
})

</script>

<template>
  <div class="top-nav">
    <div class="left-logo">
      <img class="lenovo-logo" src="../../assets/lenovo.png" alt="lenovo">
      <img class="logo-img" src="./logo-img.png" alt="logo" @click="goHome"/>
<!--      <div class="logo-title">-->
<!--        <p>INTELLIDATA</p>-->
<!--        <p>数智分析引擎</p>-->
<!--      </div>-->
    </div>
    <div class="right-func">
      <i class="iconfont icon-zhongyingqiehuan1" @click="setLocale()"></i>
      <i class="iconfont icon-xiaoxi" style="cursor: not-allowed;"></i>
      <i class="iconfont icon-yiwen" @click="jumpLink"></i>
      <span class="user-info">
        <span class="user-img">
          <img :src="headerAvatarImg" v-if="headerAvatarImg" />
          <span class="letter" v-else>{{letter}}</span>
        </span>
      </span>
    </div>
  </div>
</template>

<style scoped lang="less">
.top-nav {
  display: flex;
  flex: 1;
  justify-content: space-between;
  align-items: center;
  height: 50px;
  background: linear-gradient( 120deg, #F0F4F9 0%, #E7EDFD 100%);
  .left-logo {
    display: flex;
    align-items: center;
    .logo-img {
      height: 50px;
      cursor: pointer;
      // width: 85%;
    }
    .lenovo-logo {
      width: 90px;
      height: 30px;
      margin-left: 16px;
    }
  }
  .right-func {
    margin-right: 14px;
    .iconfont {
      font-size: 16px;
      margin-right: 16px;
      cursor: pointer;
    }
    .user-info {
      user-select: none;
      .user-img {
        display: inline-block;
        width: 34px;
        height: 34px;
        border-radius: 6px;
        background-color: #fff;
        margin-right: 10px;
        vertical-align: middle;
        overflow: hidden;
        img {
          width: 100%;
          //transform: scale(1.2);
        }
        .letter {
          font-family: Montserrat;
          font-weight: bold;
          font-size: 14px;
          color: #323F50;
          margin-top: 8px;
          margin-left: 9px;
          width: 15px;
          height: 18px;
          display: inline-block;
          text-align: center;
        }
      }
      span {
        font-family: Montserrat;
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 18px;
      }
    }
  }
}
</style>
