import request from '@/utils/axios'

const url = '/'

function getFilterList(params) {
  return request({
    url: url + 'scene/filterList',
    method: 'get',
    params
  })
}

function getDbList(data) {
  return request({
    url: url + 'chatdata/dataSource/simpleList',
    method: 'post',
    data
  })
}

function getKnowledgeList(params) {
  return request({
    url: url + 'knowledge/space/nameList',
    method: 'post',
    params
  })
}

function getPromptList(data) {
  return request({
    url: url + 'prompt/simpleList',
    method: 'post',
    data
  })
}

function getUserList(params) {
  return request({
    url: url + 'user/itcode/search',
    method: 'get',
    params
  })
}

function getAppList(data) {
  return request({
    url: url + 'scene/list',
    method: 'post',
    data
  })
}

function addApp(data) {
  return request({
    url: url + 'scene/add',
    method: 'post',
    data
  })
}

function updateApp(data) {
  return request({
    url: url + 'scene/update',
    method: 'post',
    data
  })
}

function publishApp(data) {
  return request({
    url: url + 'scene/publish',
    method: 'post',
    data
  })
}

function deleteApp(data) {
  return request({
    url: url + 'scene/delete',
    method: 'post',
    data
  })
}

// 获取智能体配置信息 原生和多智能体
function getMutiAppConfig(data) {
  return request({
    url: url + 'scene/getGptsApp/'+data.appCode,
    method: 'get'
  })
}

function getModelList() {
  return request({
    url: url + 'model/list',
    method: 'get'
  })
}

function getPluginList() {
  return request({
    url: url + 'apiConfig/apiNameList',
    method: 'get'
  })
}

export {
  getFilterList,
  getDbList,
  getPromptList,
  getUserList,
  getAppList,
  addApp,
  updateApp,
  publishApp,
  deleteApp,
  getKnowledgeList,
  getMutiAppConfig,
  getModelList,
  getPluginList
}
