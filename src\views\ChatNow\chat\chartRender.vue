<script setup>
import * as echarts from 'echarts'
import { ref, watch, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { getToken } from '@/utils/auth'
import { fetchEventSource } from "@microsoft/fetch-event-source"
import {ElMessage} from 'element-plus'
// import getOption, { chartType } from './chartOptions'
// import { Advisor } from '@antv/ava'
import { format } from 'sql-formatter'
import markdownRender from '@/components/MarkdownRender/markdownRender.vue'
import createChart from '@/components/CreateChart/v2/createChart.vue'
import { useI18n } from 'vue-i18n'
import * as api from './api/index.js'
import { isAllNotNull } from '@/utils/util'

const { t, locale  } = useI18n()

const route = useRoute()

// 声明props
const props = defineProps({
  chartData: Object,
  isRCA: Boolean,
  sessionid: String,
  requestId: String,
  userQuestionQtQ: [String, Boolean],
  userQuestionPCON: [String, Boolean]
})

// 图表类型列表
const chartTypeList = [{
  label: '柱状图',
  value: 'COLUMN_CHART'
}, {
  label: '折线图',
  value: 'LINE_CHART'
}, {
  label: '条形图',
  value: 'BAR_CHART'
}, {
  label: '面积图',
  value: 'AREA_CHART'
}, {
  label: '饼图',
  value: 'PIE_CHART'
}]
// sql弹窗
const sqlVisible = ref(false)

const charType = ref('COLUMN_CHART')

// 选中的Tab
const activeTab = ref('')
// 是否显示SQL标签
const showSQL = ref(true)

// 是否渲染图表组件
const renderChartContent = ref(false)
if (props.chartData.semanticMap && props.chartData.semanticMap.other && props.chartData.semanticMap.other.length === 0 && props.chartData.semanticMap.measure.length > 0 && props.chartData.data.length > 0) {
  renderChartContent.value = true
  activeTab.value = 'Chart'
} else {
  activeTab.value = 'Data'
}
// 是否是卡片图
const isCard = ref(false)
if (props.chartData.semanticMap && props.chartData.semanticMap.other?.length === 0 && props.chartData.semanticMap.dimension?.length === 0 && props.chartData.semanticMap.time?.length === 0 && props.chartData.semanticMap.measure?.length !== 0 && props.chartData.data?.length > 0){
  isCard.value = true
}
// SQL标签关闭
const onTabRemove = (name) => {
  if (name === 'SQL') {
    showSQL.value = false
    if (activeTab.value === 'SQL') {
      // activeTab.value = advices.value.length ? 'Chart' : 'Data'
      activeTab.value = renderChartContent.value ? 'Chart' : 'Data'
    }
  }
}
// SQL代码展示
const sqlContext = ref('')
try {
  sqlContext.value = '```sql\n' + format(props.chartData.sql, { language: 'trino' }) + '\n```'
} catch (e) {
  sqlContext.value = '```sql\n' + props.chartData.sql + '\n```'
}

// DAX代码展示
const daxContext = ref('')
try {
  daxContext.value = '```sql\n' + format(props.chartData.pbiDax, { language: 'trino' }) + '\n```'
} catch (e) {
  daxContext.value = '```sql\n' + props.chartData.pbiDax + '\n```'
}

// 表格表头
const tableHeader = ref([])
if (props.chartData.data.length) {
  tableHeader.value = Object.keys(props.chartData.data[props.chartData.data.length === 1 ? 0 : 1])
}
// 表格当前页数据
const tableData = ref([])
// 分页相关
const pageNum = ref(1)
const pageSize = ref(10)
const pageTotal = ref(0)
pageTotal.value = props.chartData.data.length
// 获取表格当前页数据
const setTableData = () => {
  const startIndex = (pageNum.value - 1) * pageSize.value
  const endIndex = pageNum.value * pageSize.value
  tableData.value = props.chartData.data.slice(startIndex, endIndex)
}
// 切换pageSize
const handleSizeChange = (val) => {
  pageNum.value = 1
  pageSize.value = val
  setTableData()
}
// 切换pageNum
const handleCurrentChange = (val) => {
  pageNum.value = val
  setTableData()
}
setTableData()

// 推荐图表类型列表
const advices = ref([])
// 选择的图表类型
const selectedType = ref('')
// 获取推荐图表类型列表
const getAdvices = () => {
  // AI返回的图表类型，如果是支持的图表类型，放在类型列表中的第一个
  const resType = props.chartData.type.replace('response_', '')
  advices.value = chartType[resType] ? [{ type: resType }] : []
  // 获取AVA分析推荐图表类型与图表的轴
  const myAdvisor = new Advisor()
  const result = myAdvisor.advise({ data: props.chartData.data })
  // 对AVA分析的结果做处理
  result.forEach((item) => {
    if (chartType[item.type]) {
      if (advices.value[0] && item.type === resType) {
        advices.value[0].encode = item.spec?.encode
      } else {
        advices.value.push({
          type: item.type,
          encode: item.spec?.encode
        })
      }
    }
  })
  // 补全默认图表的encode
  const regularTypes = ['line_chart', 'step_line_chart', 'area_chart', 'column_chart', 'bar_chart', 'scatter_plot', 'bubble_chart']
  const pieTypes = ['pie_chart', 'donut_chart']
  if (advices.value[0] && !advices.value[0].encode) {
    if (regularTypes.find(x => x === resType)) {
      if (route.query.scene === 'chat_mct_demo') {
        const keys = JSON.parse(JSON.stringify(tableHeader.value))
        const filterFields = ['kpi_name', 'bu', 'pro_line', 'pro_site']
        const qualifiedFields = keys.filter(x => filterFields.includes(x))
        if (keys.includes('kpi_date') && qualifiedFields.length === 1) {
          advices.value[0].encode = {
            x: 'kpi_date',
            y: keys.filter(x => x !== 'kpi_date' && x !== qualifiedFields[0]),
            color: qualifiedFields[0]
          }
        }
      } else {
        advices.value[0].encode = result.find(item => regularTypes.find(type => type === item.type))?.spec?.encode
      }
    } else if (pieTypes.find(x => x === resType)) {
      advices.value[0].encode = result.find(item => pieTypes.find(type => type === item.type))?.spec?.encode
    }
  }
  // 只要有饼状图就一定有漏斗图
  const pieItem = advices.value.find(x => x.type === 'pie_chart')
  if (pieItem) {
    const funnelItem = advices.value.find(x => x.type === 'funnel_chart')
    if (funnelItem) {
      funnelItem.encode = pieItem.encode
    } else {
      advices.value.push({
        type: 'funnel_chart',
        encode: pieItem.encode
      })
    }
  }
  // 只要有折线图就一定会有柱状图条形图，用于demo展示
  const lineItem = advices.value.find(x => x.type === 'line_chart')
  if (lineItem) {
    if (!advices.value.find(x => x.type === 'column_chart')) {
      advices.value.push({
        type: 'column_chart',
        encode: lineItem.encode
      })
    }
    if (!advices.value.find(x => x.type === 'bar_chart')) {
      advices.value.push({
        type: 'bar_chart',
        encode: lineItem.encode
      })
    }
  }
  selectedType.value = advices.value.length ? advices.value[0].type : ''
}
// 字段/维度数量在2-5时可获取图表推荐
// if (
//   props.chartData.type &&
//   props.chartData.type !== 'response_table' &&
//   tableHeader.value.length > 1 &&
//   tableHeader.value.length < 6
// ) {
//   getAdvices()
// }
// 卡片图逻辑
// if (tableHeader.value.length === 1 && props.chartData.data.length === 1) {
//   const val = props.chartData.data[0][tableHeader.value[0]]
//   // 判断是否为数值或数值字符串
//   if (!isNaN(parseFloat(val)) && isFinite(val)) {
//     advices.value.unshift({ type: 'card_chart' })
//     selectedType.value = 'card_chart'
//   }
// }
// activeTab.value = advices.value.length ? 'Chart' : 'Data'

// echarts渲染实例
// const chartRef = ref()
// let myChart = null
// 获取图表option并渲染
// const setChartOption = () => {
//   const selectedAdvice = advices.value.find(item => item.type === selectedType.value)
//   const option = getOption(props.chartData.data, selectedAdvice)
//   myChart.setOption(option, true)
// }

// onMounted(() => {
//   nextTick(() => {
//     // 字段/维度数量在2-5时可渲染图表
//     if (advices.value.length && tableHeader.value.length > 1 && tableHeader.value.length < 6) {
//       myChart = echarts.init(chartRef.value)
//       setChartOption()
//       window.addEventListener('resize', () => {
//         myChart.resize()
//       })
//     }
//   })
// })

// 判断字符串是否为URL
const isURL = (str) => {
  try {
    new URL(str)
    return true
  } catch (e) {
    return false
  }
}

const handleChartCommand = (command) => {
  charType.value = command
}

const openSqlDialog = () => {
  sqlVisible.value = true
}
// const insightData = ref('')
const insightData = props.chartData.insight ? ref(props.chartData.insight) : ref('')
const handleInsightData = async() => {
  let ctrl = new AbortController() // 标记是否结束
  try {
    await fetchEventSource(`${import.meta.env.VITE_APP_BASE_API_URL}/scene/api/summary?sessionId=${props.sessionid}&requestId=${props.requestId}&stream=true`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Authorization: getToken() 这里不需要token，是个开放的api对外
      },
      signal: ctrl.signal,
      openWhenHidden: true,
      onmessage(event) {
        let data = JSON.parse(event.data)
        if (data.isEnd && data.data === null) {
          return
        } else {
          insightData.value += data.data
        }
      },
      onclose() {
        ElMessage.success(t('CHAT.INSIGHT_SUCCESS'))
        ctrl.abort()
      },
      onerror(error) {
        ElMessage.error(t('CHAT.INSIGHT_FAIL'))
        throw error
      }
    })
  } catch (error) {
    console.log(error)
  }
}

onMounted(() => {
  if(props.chartData.isFetchEventSource && isAllNotNull(props.chartData.data)) {
    handleInsightData()
  }
})
</script>

<template>
  <div class="chart-render">
    <!-- 洞察内容 -->
    <div class="insight-result">
      <markdown-render :context="insightData" />
    </div>
    <div class="chart-result">
      <div class="right-button">
        <div class="dropdown-list">
          <el-dropdown placement="bottom" :teleported="false" :disabled="isCard || !renderChartContent" trigger="click" @command="handleChartCommand">
            <i class="icon-1 iconfont icon-tubiaoleixing" :class="{no: isCard || !renderChartContent}"></i>
            <template #dropdown>
              <el-dropdown-menu class="chart-type-dropdown">
                <el-dropdown-item v-for="item in chartTypeList" :key="item.value" :command="item.value">{{ $t(`CHART.${item["value"]}`)
                  }}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <!-- <el-dropdown placement="bottom" :teleported="false" trigger="click">
            <i class="iconfont icon-SQL"></i>
            <template #dropdown></template>
          </el-dropdown> -->
          <i class="icon-1 iconfont icon-SQL" @click="openSqlDialog"></i>

          <!-- <el-dropdown placement="bottom" :teleported="false" trigger="click">
            <i class="icon-1 iconfont icon-gengduo"></i>
            <template #dropdown>
              <el-dropdown-menu class="analysis-dropdown">
                <el-dropdown-item :disabled="props.chartData.data?.length === 0" @click="handleInsightData">
                  <i class="iconfont icon-shujudongcha"></i>
                  {{ $t('CHAT.DATA_INSIGHT') }}
                </el-dropdown-item>
                <el-dropdown-item disabled>
                  <i class="iconfont icon-zhinengyuce"></i>
                  {{ $t('CHAT.INTELLIGENT_PREDICTION') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown> -->
        </div>
      </div>
      <el-tabs v-model="activeTab" @tab-remove="onTabRemove">
        <!-- <el-tab-pane v-if="advices.length" label="Chart" name="Chart">
          <div class="chart-advice">
            <div>{{ $t('CHAT.ADVICES') }}</div>
            <el-select v-model="selectedType" @change="setChartOption()">
              <el-option v-for="item in advices" :key="item.type" :label="$t(`CHART.${chartType[item.type]}`)"
                :value="item.type" />
            </el-select>
          </div>
          <el-card v-if="selectedType === 'card_chart'">
            <el-statistic :title="tableHeader[0]" :value="tableData[0][tableHeader[0]]"
              :formatter="(val) => val.toLocaleString()" />
          </el-card>
          <div v-else ref="chartRef" style="height: 500px" />
        </el-tab-pane> -->

        <el-tab-pane v-if="renderChartContent" label="Chart" name="Chart">
          <create-chart :renderData="props.chartData" :chartType="charType" />
        </el-tab-pane>
        <el-tab-pane label="Data" name="Data">
          <el-table :data="tableData" header-cell-class-name="table-box-header">
            <el-table-column v-for="item in tableHeader" :key="item" :prop="item" :label="item"
              :min-width="isURL(tableData[0][item]) ? '330px' : '130px'">
              <template v-if="item === 'suggest_actions'" #default="scope">
                <div class="suggest-layout">
                  <el-tooltip :content="scope.row[item].split('\n').map(x => `<li>${x}</li>`).join('')"
                    :raw-content="true" placement="top" effect="suggest-tip">
                    <Warning class="suggest-icon" />
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination v-model:current-page="pageNum" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
            :total="pageTotal" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </el-tab-pane>
        <!-- <el-tab-pane v-if="showSQL && !props.chartData.pbiDax && !props.isRCA" label="SQL" name="SQL" closable>
          <markdown-render :context="sqlContext" />
        </el-tab-pane>
        <el-tab-pane v-if="props.chartData.pbiDax" label="DAX" name="DAX">
          <markdown-render :context="daxContext" />
        </el-tab-pane> -->
      </el-tabs>
    </div>
    <div class="analysis-result">
      <div class="result1" v-if="userQuestionQtQ === true">
        <p class="analysis-label">Based on the provided data, here are some financial analysis results:</p>
        <ul>
          <li>
            <span class="li-dian"></span>
            From
            FY21/22Q2
            to
            FY22/23Q1
            , the company's quarterly expenses show a significant increase, especially in
            FY22/23Q1
            , with a quarter-on-quarter (QoQ) increase of
            6.5
            million dolloars. This could be due to company expansion, marketing efforts, or other significant
            expenditures.
            However, from
            FY22/23Q1
            to
            FY22/23Q2
            , there is a substantial decrease in expenses, with a decrease of
            1.8
            million dollars, which may indicate cost control measures by the company or a reduction in revenue.
          </li>
          <li>
            <span class="li-dian"></span>
            From
            FY22/23Q2
            to
            FY23/24Q4
            , despite fluctuations, expenses generally trend upwards, which may indicate the company is scaling up its
            business or increasing investments.
          </li>
          <li>
            <span class="li-dian"></span>
            The exceptionally low expenses in
            FY24/25Q2
            , with a decrease of
            13.4
            million dollars, require special attention. This could be due to the current quarterly data collection is in
            progress or
            changes in accounting policies
          </li>
        </ul>
      </div>
      <div class="result2" v-if="userQuestionPCON === true">
        <p class="analysis-label">Based on the provided data, here are some analysis results:</p>
        <ul>
          <li>
            <span class="li-dian"></span>
            Highest positive PCON is in the Consumer sector of PRC, at
            253
            million dollars.
          </li>
          <li>
            <span class="li-dian"></span>
            The lowest negative is in HQ's SMB sector,
            -509
            million dollars.
          </li>
          <li>
            <span class="li-dian"></span>
            The REL Segment's high PCON, particularly in EMEA, signals substantial profitability or business volume in
            related transactions.
          </li>
          <li>
            <span class="li-dian"></span>
            High SMB PCON in EMEA and PRC indicates the significant revenue contribution from small and medium-sized
            businesses in these regions.
          </li>
        </ul>
      </div>
    </div>
    <!-- <div class="insight-result">
      <markdown-render :context="insightData" />
    </div> -->
  </div>


  <el-dialog v-model="sqlVisible" :show-close="true" width="800" class="sql-dialog setting-page-dialog-wrap">
    <template #header>
      <div class="my-header">
        <h4>{{ $t('CHAT.VIEW_SQL') }}</h4>
        <!-- <el-button type="danger" @click="close">
          <el-icon><CloseBold /></el-icon>
        </el-button> -->
      </div>
    </template>
    <div v-if="showSQL && !props.chartData.pbiDax && !props.isRCA">
      <markdown-render :context="sqlContext" />
    </div>
    <div v-if="props.chartData.pbiDax">
      <markdown-render :context="daxContext" />
    </div>
  </el-dialog>

</template>

<style lang="less" scoped>
.chart-type-dropdown,
.analysis-dropdown {
  padding: 7px;
  z-index: 2;
  :deep(li) {
    font-size: 12px;
    // color: #0A121F;
  }
  :deep(li:hover) {
    border-radius: 4px;
    // background: #F0F5FF;
    // color: #0A121F;
  }
  .img-icon {
    width: 16px;
    height: 16px;
    margin-right:3px;
  }
}
.chart-render {
  background-color: #F5F8FF;
  padding: 0 10px;
  border-radius: 12px;
  border-top-left-radius: 0;
  position: relative;
  .chart-result {
    position: relative;
  }
  .right-button {
    position: absolute;
    top: 4px;
    right: 10px;
    z-index: 2;

    .dropdown-list {
      position: relative;

      i.icon-1 {
        width: 16px;
        height: 16px;
        line-height: 35px;
        margin-right: 20px;
        cursor: pointer;
        &:hover {
          color: #568CF4;
        }
        &.no {
          cursor: not-allowed;
        }
        &.no:hover {
          color: #a8abb2;
        }
      }
    }
  }

  .el-tabs :deep('.el-tabs__nav-wrap::after') {
    height: 1px;
    background-color: #E5E7ED;
  }
  .chart-advice {
    display: flex;
    align-items: center;
    font-size: 14px;

    .el-select {
      margin-left: 10px;
    }
  }

  :deep(.el-card) {
    width: 240px;
    margin: 30px;
    text-align: center;

    .el-statistic__head {
      text-transform: uppercase;
      font-size: 14px;
      line-height: 1;
      margin-bottom: 12px;
    }

    .el-statistic__content {
      font-size: 28px;
    }
  }

  :deep(.table-box-header) {
    font-size: 16px;
  }

  .el-pagination {
    margin: 15px 0;
    justify-content: flex-end;
  }

  .suggest-layout {
    display: flex;
    align-items: center;
    justify-content: center;

    .suggest-icon {
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
  }

  .analysis-result {
    display: none;
    .analysis-label {
      margin-bottom: 6px;
    }

    .li-dian {
      display: inline-block;
      width: 8px;
      height: 8px;
      background-color: #333;
      border-radius: 50%;
      margin-right: 6px;
      margin-top: -2px;
      position: relative;
      top: -2px;
    }
  }
}
.insight-result {
  // padding: 10px 0;
  padding-top: 5px;
  :deep(.el-loading-mask) {
    background-color: transparent;
  }
  :deep(.el-loading-spinner) {
    margin-top: -15px;
    text-align: left;
    padding-left: 20px;
    .circular {
      width: 20px;
      height: 20px;
    }
  }
}
</style>
<style lang="less">
.is-suggest-tip {
  background: #e7ecf9;
  color: #333433;
  border: 1px solid #e5e4e4;

  .el-popper__arrow::before {
    background: #e7ecf9;
    border: 1px solid #e5e4e4;
  }

  li {
    list-style: disc;
    font-size: 14px;
  }
}
.sql-dialog {
  .el-dialog__header {
    border-bottom: 1px solid #D5DFEB;
    margin-right: 0;
  }
  }
</style>
