import request from '@/utils/axios'

const url = '/'

function getAppList(params) {
  return request({
    url: url + 'scene/publishingNameList',
    method: 'get',
    params
  })
}

function getChatList(data) {
  return request({
    url: url + 'api/v1/chat/dialogue/list',
    method: 'post',
    data
  })
}

function toTop(data) {
  return request({
    url: url + 'api/v1/chat/toTop',
    method: 'post',
    data
  })
}

function deleteChat(data) {
  return request({
    url: url + 'api/v1/chat/dialogue/delete',
    method: 'post',
    data
  })
}
function renameChat(data) {
  return request({
    url: url + 'api/v1/chat/dialogue/history/updateConvName',
    method: 'post',
    data
  })
}
function getPromptList(data) {
  return request({
    url: url + 'prompt/list',
    method: 'post',
    data
  })
}
function getNewChatId(data) {
  return request({
    url: url + 'api/v1/chat/dialogue/new',
    method: 'post',
    data
  })
}

export {
  getChatList,
  toTop,
  deleteChat,
  getAppList,
  getPromptList,
  getNewChatId,
  renameChat
}
