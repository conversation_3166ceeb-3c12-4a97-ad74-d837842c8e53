import EncryptLong from 'encryptlong'

// 公钥
const publicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCVZ6rPo7HVODLYl42roGe96wjd2dKJc13XzbZEbiLsPMnIurI9mSULfzvNe6KUIGkJjb0RSNU/UHCJ8k9O5PShQDeO52kl5LVFFTJXDk95XMtIao+MiLiNJ3n455J5M7VogcEZdwyhTpaHY6pUoZlps3iV7o87JyCgIvhR2cCq/QIDAQAB'
// 私钥
const privateKey = 'MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJVnqs+jsdU4MtiXjaugZ73rCN3Z0olzXdfNtkRuIuw8yci6sj2ZJQt/O817opQgaQmNvRFI1T9QcInyT07k9KFAN47naSXktUUVMlcOT3lcy0hqj4yIuI0nefjnknkztWiBwRl3DKFOlodjqlShmWmzeJXujzsnIKAi+FHZwKr9AgMBAAECgYAOt7+j5MRH9NlNbOCP36VYmCavdB3BXJQKBkHGS/M0wmg+VeXpG4UCtO33OA6z+N1gC45sfXhEXHA5pp0ZcISIlo/xEwKbuGXROeQHMGxIPABMfHw7ynaU6ShkI72YGzIV+m1MJgXRjU+ESVm18vBm3AgIhJTBYdFB4bqHLi9vIQJBAPMonL04eaHQUGniz6ELAhSSqaj6jd/8i84e2IoCueuQ91lbOl7a+Rbyg0KeAuoWjBkjIXp/vO6GvlMo9cb5gh8CQQCdS4zaa7oZmB+Bi8JBCR5ytUUb9l8loG58CldmDs01VtWJ+R65lUhKobtzSeN0pGuLJqR9DQk40ExH3PbZu4djAkAj/zEk+gVXpSUiZ8dhCeYm8YdYUq9KJ3Ly8rIPzFOFwhI8EjtT64rQkNpJtGauDOR0nDTKk7p+fUgTu46XBDz1AkBH4qIxpC/9yG8FpliVnlRC+iLmX5cmPAaiO5ursbII4XFfx/FJwTaznOxtCOShhw77HiLoX4/KXi+uXAaYhUKfAkEArvI53aCJYPunv9rC8nQBcNweu9/JxV00CfjlGOeYwqE5PmeW/ADWrnt+1ywfnhwMN4bEbc/wswVIM8aIt7ZUFQ=='

// 加密
export const encrypt = (txt) => {
  const encryptor = new EncryptLong()
  encryptor.setPublicKey(publicKey)
  return encryptor.encryptLong(encodeURIComponent(txt))
}

// 解密
export const decrypt = (txt) => {
  const encryptor = new EncryptLong()
  encryptor.setPrivateKey(privateKey)
  return decodeURIComponent(encryptor.decryptLong(txt))
}
