<script setup>
import leftBar from '../../components/LeftBar/leftBar.vue'
import { CopyDocument, MoreFilled, Position, Document } from '@element-plus/icons-vue'
import { ref, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useI18n } from 'vue-i18n'
import { copyToClipboard, loginRedirect } from '@/utils/util'
import { getToken } from '@/utils/auth'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import chartRender from './chartRender.vue'
import markdownRender from '@/components/MarkdownRender/markdownRender.vue'
import { ElMessage } from 'element-plus'
import * as api from './api'

const route = useRoute()
const router = useRouter()
const user = useUserStore()
const { t } = useI18n()

// 当前选择的模型
const modelName = ref('')
// 模型列表
const modelList = ref([])
modelName.value = user.model[0] || ''
modelList.value = user.model

// 当前选择的知识库/数据源
const selectParam = ref('')
// 知识库/数据源列表
const selectList = ref([])
// 获取知识库列表
const getKnowledgeList = async () => {
  const res = await api.getKnowledgeList()
  selectList.value = res || []
  if (route.query.select && selectList.value.includes(route.query.select)) {
    selectParam.value = route.query.select
  } else {
    selectParam.value = selectList.value[0] || ''
  }
}
// 获取数据源列表
const getDbList = async () => {
  const res = await api.getDbList()
  selectList.value = res || []
  if (route.query.select && selectList.value.includes(route.query.select)) {
    selectParam.value = route.query.select
  } else {
    selectParam.value = selectList.value[0] || ''
  }
}
// 获取应用列表
const getAppList = async () => {
  const res = await api.getAppList()
  selectList.value = res || []
  if (route.query.select && selectList.value.includes(route.query.select)) {
    selectParam.value = route.query.select
  } else {
    selectParam.value = selectList.value[0] || ''
  }
}

// 滚动栏实例
const scrollbarRef = ref()
// 消息列表实例
const messageListRef = ref()
// 消息列表滚动至指定位置(不传值则滚动至底部)
const scrollTo = async (num) => {
  // 需要通过nextTick等待DOM更新完成
  await nextTick()
  if (messageListRef.value) {
    // 需要等待echarts全部加载完毕
    setTimeout(() => {
      const max = messageListRef.value.clientHeight
      scrollbarRef.value.setScrollTop(isNaN(num) ? max : max - num)
    })
  }
}
// 滚动事件
const handleScroll = (obj) => {
  if (obj.scrollTop === 0 && (pageNum * pageSize < pageTotal)) {
    pageNum++
    getMessageList()
  }
}

// 历史记录分页相关
let pageNum = 1
let pageSize = 6
let pageTotal = 0
const viewLoading = ref(false)
const chatLoading = ref(false)
// 消息列表
const messageList = ref([])
// 获取消息列表
const getMessageList = async () => {
  if (!route.query.id) {
    if (route.query.scene === 'chat_mct_demo') {
      messageList.value = [{ type: 'welcome', roundIndex: -1 }]
    }
    return
  }
  viewLoading.value = pageNum === 1
  chatLoading.value = pageNum !== 1
  const oldH = messageListRef.value && messageListRef.value.clientHeight
  try {
    const res = await api.getMessageList({
      convUid: route.query.id,
      pageNum,
      pageSize
    })
    viewLoading.value = false
    chatLoading.value = false
    if (res?.list?.length) {
      const list = res.list.filter(item => {
        if (item.type === 'human' || item.type === 'view') {
          // 判断返回的消息是否为JSON字符串，是则以data形式赋值，否则不做处理
          try {
            const data = JSON.parse(item.messageDetail)
            if (!Array.isArray(data.data)) {
              data.data = []
            }
            // 返回的message是否为空来做报错消息提示展示
            // if (!data.message) {
            //   item.messageDetail = data.aitext
            // } else {
            //   item.messageDetail = data.message
            // }
            // Jira MTYLH-2891 去掉aitext的显示
            if (data.message) {
              item.messageDetail = data.message
            }
            // item.messageDetail = data.aitext
            item.chartData = data
            return true
          } catch (e) {
            return true
          }
        } else {
          return false
        }
      })
      // selectPrompt.value = list[list.length - 1]?.promptId // 历史最后一次prompt记录赋值给当前
      selectSystemPrompt.value = list[list.length - 1]?.sysPromptId // 历史最后一次prompt记录赋值给当前
      selectAppPrompt.value = list[list.length - 1]?.appPromptId // 历史最后一次prompt记录赋值给当前
      pageTotal = res.total || 0
      messageList.value = [
        ...route.query.scene === 'chat_mct_demo' && (pageNum * pageSize >= pageTotal) ? [{ type: 'welcome', roundIndex: -1 }] : [],
        ...list,
        ...messageList.value
      ]
      if (pageNum === 1) {
        scrollTo()
      } else {
        scrollTo(oldH)
      }
    }
  } catch (e) {
    viewLoading.value = false
    chatLoading.value = false
  }
}

const showWarning = ref(true)
const sendLoading = ref(false)
// 输入框文本
const userInput = ref('')
// 检测是否出现RCA相关关键字
const isRCA = (str) => {
  const regex = new RegExp('rca|suggestion|action', 'i')
  return regex.test(str)
}

// 检测是否出现QtQ相关关键字
const isQtQ = (str) => {
  const regex = new RegExp('Show QtQ difference of sum expense by fiscal quarter in ISG, LA', 'i')
  return regex.test(str)
}

// 检测是否出现PCON相关关键字
const isPCON = (str) => {
  const regex = new RegExp('Show Total PCON by Geo and Segment in 2023 Q3', 'i')
  return regex.test(str)
}
// 检测是否出现UPPH相关关键字
const isUPPH = (str) => {
  const regex = new RegExp('upph', 'i')
  return regex.test(str)
}
// 获取AI回复
const getAIResponse = (isNew) => {
  sendLoading.value = true
  const userInputVal = userInput.value
  userInput.value = ''
  const roundIndex = messageList.value.length ? messageList.value[messageList.value.length - 1].roundIndex + 1 : 1
  messageList.value.push({
    messageDetail: userInputVal,
    modelName: modelName.value,
    roundIndex,
    type: 'human'
  }, {
    messageDetail: '',
    modelName: modelName.value,
    roundIndex,
    type: 'view'
  })
  scrollTo()

  // Chat MCT Demo 特殊逻辑，检测到指定关键字使用不同的数据源
  const _RCA = isRCA(userInputVal)
  const params = {
    chatMode: route.query.scene === 'chat_mct_demo' ? 'chat_with_data' : route.query.scene,
    convUid: route.query.id,
    modelName: modelName.value,
    selectParam: route.query.scene === 'chat_mct_demo' ? (_RCA ? 'processed_rca_data' : 'MCT_KPI') : selectParam.value,
    userInput: userInputVal,
    // isAddHistory: route.query.scene === 'chat_with_data' ? 1 : 0,
    isAddHistory: 0, // 8.29 yueqi：暂时关闭多轮会话
    // promptId: route.query.scene === 'chat_mct_demo' ? (_RCA ? 119 : 111) : selectPrompt.value
    sysPromptId: route.query.scene === 'chat_mct_demo' ? (_RCA ? 119 : 111) : selectSystemPrompt.value,
    appPromptId: route.query.scene === 'chat_mct_demo' ? (_RCA ? 119 : 111) : selectAppPrompt.value
  }
  fetchChat(params, isNew)
}
// 聊天流式请求
const fetchChat = (params, isNew) => {
  const ctrl = new AbortController()
  try {
    fetchEventSource(
      `${import.meta.env.VITE_APP_BASE_API_URL}/api/v1/chat/completionsV1`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: getToken()
        },
        body: JSON.stringify(params),
        signal: ctrl.signal,
        openWhenHidden: true,
        onmessage(event) {
          // 判断返回的消息是否为JSON字符串，是则以data形式赋值，否则以knowledge形式赋值
          try {
            const data = JSON.parse(event.data)
            // 开发环境下打印日志方便查看问题
            if (import.meta.env.VITE_APP_MODE !== 'PROD') {
              console.log(data, '处理后的数据')
            }
            if (!Array.isArray(data.data)) {
              data.data = []
            }
            // 返回的message是否为空来做报错消息提示展示
            // if (!data.message) {
            //   messageList.value[messageList.value.length - 1].messageDetail = data.aitext
            // } else {
            //   messageList.value[messageList.value.length - 1].messageDetail = data.message
            // }
            // Jira MTYLH-2891 去掉aitext的显示
            if (data.message) {
              messageList.value[messageList.value.length - 1].messageDetail = data.message
            }
            // messageList.value[messageList.value.length - 1].messageDetail = data.aitext
            messageList.value[messageList.value.length - 1].chartData = data
            loginRedirect(data.message)
          } catch (e) {
            messageList.value[messageList.value.length - 1].messageDetail = event.data
          }
          scrollTo()
        },
        onclose() {
          ctrl.abort()
          if (isNew) {
            user.setChatList(1)
          } else if (route.query.select !== selectParam.value) {
            // 流式接口请求成功的情况下更新用户最新选择的数据源/知识库
            user.setChatParam(route.query.id, selectParam.value)
            router.replace({
              path: '/chat',
              query: {
                scene: route.query.scene,
                select: selectParam.value,
                id: route.query.id
              }
            })
          }
          sendLoading.value = false
        },
        onerror(error) {
          messageList.value[messageList.value.length - 1].messageDetail = t('CHAT.CHAT_ERROR')
          sendLoading.value = false
          throw new Error(error)
        }
      }
    )
  } catch (e) {
    ctrl.abort()
    messageList.value[messageList.value.length - 1].messageDetail = t('CHAT.CHAT_ERROR')
    sendLoading.value = false
    throw new Error(e)
  }
}
// 是否为新的对话Id
let isNewId = false
// 创建新的对话
const getNewChatId = async () => {
  viewLoading.value = true
  try {
    const res = await api.getNewChatId({ chatMode: route.query.scene })
    viewLoading.value = false
    if (res) {
      isNewId = true
      router.replace({
        path: '/chat',
        query: {
          scene: route.query.scene,
          select: selectParam.value,
          id: res
        }
      }).then(() => {
        getAIResponse(true)
      })
    }
  } catch (e) {
    viewLoading.value = false
  }
}
// 打字机效果
let num = 0
let wordTimeout = null
const autoWord = (str) => {
  wordTimeout = setTimeout(() => {
    messageList.value[messageList.value.length - 1].messageDetail = str.substr(0, num)
    scrollTo()
    num += 10
    if (num < str.length + 10) {
      autoWord(str)
    } else {
      clearTimeout(wordTimeout)
      wordTimeout = null
    }
  }, 100)
}
// Chat PRC Demo 假数据
const getPRCDemoData = () => {
  sendLoading.value = true
  const userInputVal = userInput.value
  userInput.value = ''
  const roundIndex = messageList.value.length ? messageList.value[messageList.value.length - 1].roundIndex + 1 : 1
  messageList.value.push({
    messageDetail: userInputVal,
    modelName: modelName.value,
    roundIndex,
    type: 'human'
  }, {
    messageDetail: '',
    modelName: modelName.value,
    roundIndex,
    type: 'view'
  })
  scrollTo()

  setTimeout(() => {
    if (messageList.value.length === 2) {
      const str = '报表名称：[大区排名](https://ciop.lenovo.com/#/MTY5MDk0NzkwOTU0MOWkp+WMuuaOkuWQjQ==), 报表摘要：所有大区PRC Total，REL，SMB，CON的REV和CA完成率和排名\n\n报表名称：[大区销售进展日报](https://ciop.lenovo.com/#/MTY5MDk0ODAyMTg3NeWkp+WMuumUgOWUrui/m+WxlQ==), 报表摘要：所有大区REV和CA Outlook完成率，Order QTD, Order QTD%, Order YoY，SI Booking QTD, SI Booking QTD%， SI Booking QTD YoY\n\n报表名称：[REL战区排名](https://ciop.lenovo.com/#/MTY5MDk0NTkzMTE4NuaImOWMuumUgOWUrui/m+WxlQ==)，报表摘要：REL所有大区，战区，Segment (IDG，ISG，SSG)的REV和CA目标完成率及排名\n\n报表名称：[REL战区销售进展](https://ciop.lenovo.com/#/MTY5MDk0NTkzMTE4NuaImOWMuumUgOWUrui/m+WxlQ==), 报表摘要：所有大区及战区指标，包含REV和CA Outlook完成率，Order QTD, Order QTD%, Order YoY，SI Booking QTD, SI Booking QTD%， SI Booking QTD YoY，总商机，剩余任务，剩余商机，剩余商机V值'
      autoWord(str)
    } else {
      messageList.value[messageList.value.length - 1].messageDetail = '用户想要查看所有大区REL完成率排名情况。'
      messageList.value[messageList.value.length - 1].chartData = {
        type: 'response_pie_chart',
        // aitext: '用户想要查看所有大区REL完成率排名情况。',
        aitext: '',
        sql: 'SELECT 大区, REL完成率 FROM 大区排名 ORDER BY REL完成率 DESC',
        data: [
          { 大区: '华北大区', REL完成率: 0.57 },
          { 大区: '华东大区', REL完成率: 0.47 },
          { 大区: '西南大区', REL完成率: 0.46 },
          { 大区: '东南大区', REL完成率: 0.46 },
          { 大区: '西北大区', REL完成率: 0.39 },
          { 大区: '中东大区', REL完成率: 0.38 },
          { 大区: '华南大区', REL完成率: 0.34 },
          { 大区: '东北大区', REL完成率: 0.31 }
        ]
      }
      scrollTo()
    }
    sendLoading.value = false
  }, 2000)
}
// Chat MCT Demo 特殊逻辑，检测到指定关键字展示特殊按钮
const showRCABtn = (index) => {
  if (route.query.scene === 'chat_mct_demo') {
    const str = messageList.value[index - 1]?.messageDetail
    const _RCA = isRCA(str)
    const _UPPH = isUPPH(str)
    return !_RCA && _UPPH ? (index === messageList.value.length - 1 ? sendLoading.value === false : true) : false
  } else {
    return false
  }
}
const autoSendMessage = (str) => {
  userInput.value = str
  sendMessage()
}
const inputAdvices = [
  'shows MTD of MCR in May,2024',
  'shows MTD of MCR by BU',
  'shows daily trend of ATS of Server in June,2024',
  'RCA on Online UPPH',
  'RCA on Working Hours'
]
// 发送信息
const sendMessage = () => {
  if (sendLoading.value === true) {
    return
  }
  if (!userInput.value) {
    ElMessage.warning(t('CHAT.EMPTY_WARNING'))
    return
  }
  if (route.query.scene === 'chat_prc_demo') {
    getPRCDemoData()
    return
  }
  if (route.query.id) {
    getAIResponse()
  } else {
    getNewChatId()
  }
}
// 输入框回车事件
const handleInputEnter = (e) => {
  if (!e.altKey && !e.ctrlKey && !e.metaKey && !e.shiftKey) {
    e.returnValue = false
    sendMessage()
  }
}

const feedbackLoading = ref(false)
// 评分表单数据
const reviewForm = ref({
  quesType: '',
  score: 0,
  messages: ''
})
// 获取当前评分
const getTheFeedback = async (roundIndex) => {
  feedbackLoading.value = true
  reviewForm.value = {
    quesType: '',
    score: 0,
    messages: ''
  }
  try {
    const res = await api.getTheFeedback({
      convUid: route.query.id,
      roundIndex
    })
    feedbackLoading.value = false
    if (res) {
      const { quesType, score, messages } = res
      reviewForm.value = {
        quesType,
        score,
        messages
      }
    }
  } catch(e) {
    feedbackLoading.value = false
  }
}
// 提交评分
const commitFeedback = async (roundIndex, index) => {
  feedbackLoading.value = true
  try {
    const param = {
      convUid: route.query.id,
      roundIndex,
      knowledgeSpace: selectParam.value,
      question: messageList.value[index - 1].messageDetail,
      ...reviewForm.value
    }
    const res = await api.commitFeedback(param)
    feedbackLoading.value = false
    if (res) {
      ElMessage.success(`${t('COMMON.SUBMIT')} ${t('COMMON.SUCCESS')}`)
      messageList.value[index].feedbackVisible = false
    } else {
      ElMessage.warning(`${t('COMMON.SUBMIT')} ${t('COMMON.FAIL')}`)
    }
  } catch(e) {
    feedbackLoading.value = false
  }
}

// 选择的提示语ID
// let selectPrompt = ref()
let selectSystemPrompt = ref()
let selectAppPrompt = ref()
// 提示语列表
// const promptList = ref([])
const systemPromptList = ref([])
const appPromptList = ref([])
// 是否为itAdmin。是则显示系统提示语和应用提示语；不是则只显示应用提示语
const isAdmin = ref(user.promptTab)
// 提示语选中的Tab
const promptTabName = ref('application')
const changePromptTab = () => {
  getPromptList()
}
// 获取提示语列表
const getPromptList = async () => {
  const res = await api.getPromptList({
    chatScene: route.query.scene,
    promptType: promptTabName.value // application: 应用提示语, system: 系统提示语【只有itadmin可见】
  })
  if (promptTabName.value === 'application') {
    appPromptList.value = res || []
  } else {
    systemPromptList.value = res || []
  }
}
// 提示语气泡实例
const promptPopoverRef = ref()
// 填充提示语
const setPrompt = (id, type) => {
  // selectPrompt.value = id
  if (type === 'system') {
    selectSystemPrompt.value = id
  } else {
    selectAppPrompt.value = id
  }
  promptPopoverRef.value.hide()
}

watch(
  () => route.query.id,
  () => {
    showWarning.value = true
    if (isNewId) {
      isNewId = false
    } else {
      if (route.query.scene === 'chat_with_data') {
        getDbList()
      } else if (route.query.scene === 'chat_knowledge') {
        getKnowledgeList()
      } else if (route.query.scene === 'chat_with_scene') {
        getAppList()
      }
      pageNum = 1
      pageTotal = 0
      messageList.value = []
      getMessageList()
    }
    getPromptList()
  },
  { immediate: true }
)
</script>

<template>
  <div class="chat-wrap">
    <left-bar class="left-bar" v-if="user.routes" />
    <div class="chat-view" v-loading="viewLoading">
      <div
        v-if="route.query.scene === 'chat_with_data' || route.query.scene === 'chat_knowledge' || route.query.scene === 'chat_with_scene'"
        class="chat-view-top"
      >
        <!-- <el-select v-model="modelName" style="margin-right: 16px">
          <el-option
            v-for="item in modelList"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select> -->
        <el-select v-model="selectParam" filterable>
          <el-option
            v-for="item in selectList"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </div>
      <div class="chat-view-layout">
        <el-scrollbar ref="scrollbarRef" class="chat-content" @scroll="handleScroll">
          <div v-if="chatLoading" class="chat-content-loading" v-loading="chatLoading" />
          <ul v-if="messageList && messageList.length" ref="messageListRef" class="chat-content-list">
            <div v-if="messageList[0].type === 'welcome'" class="welcome-placeholder" />
            <li
              v-for="(item, index) in messageList"
              :key="`${item.roundIndex}-${item.type}`"
              :class="['chat-content-item', item.type]"
            >
              <div style="display: flex">
                <User v-if="item.type === 'human'" class="chat-human-icon" />
                <img
                  v-if="item.type === 'view' || item.type === 'welcome'"
                  src="@/assets/chatgpt.png"
                  class="chat-ai-icon"
                />
                <div style="width: calc(100% - 40px)">
                  <template v-if="item.type === 'welcome'">
                    <div class="welcome-text">Hi, Welcome to IntelliData.</div>
                    <div class="welcome-text">You can chat data with below questions:</div>
                    <div class="welcome-advices">
                      <el-tag
                        v-for="item in inputAdvices"
                        :key="item"
                        size="large"
                        @click="autoSendMessage(item)"
                      >{{ item }}</el-tag>
                    </div>
                  </template>
                  <!--                <markdown-render-->
                  <!--                  v-if="item.messageDetail"-->
                  <!--                  :context="item.messageDetail"-->
                  <!--                />-->
                  <!--                Jira MTYLH-2891 去掉aitext的显示-->
                  <markdown-render
                    v-if="item.type === 'human' || (item.type === 'view' && item.chartData?.message)"
                    :context="item.messageDetail"
                  />
                  <chart-render
                    v-if="item.chartData"
                    :chartData="item.chartData"
                    :userQuestionQtQ="isQtQ(messageList[index - 1]?.messageDetail)"
                    :userQuestionPCON="isPCON(messageList[index - 1]?.messageDetail)"
                    :isRCA="route.query.scene === 'chat_mct_demo' ? isRCA(messageList[index - 1]?.messageDetail) : false"
                  />
                </div>
              </div>
              <div v-if="item.type === 'view'" class="chat-operation">
                <div style="padding-left: 40px">
                  <el-button
                    v-if="showRCABtn(index)"
                    type="primary"
                    @click="autoSendMessage('RCA on Online UPPH with Top 10 factors')"
                  >Check RCA</el-button>
                </div>
                <div style="display: flex">
                  <el-tooltip :content="$t('COMMON.COPY')" placement="top">
                    <el-button
                      class="chat-operation-button"
                      size="large"
                      :icon="CopyDocument"
                      text
                      @click="copyToClipboard(item.messageDetail)"
                    />
                  </el-tooltip>
                  <el-popover
                    v-model:visible="item.feedbackVisible"
                    placement="bottom"
                    :width="320"
                    trigger="click"
                    @before-enter="getTheFeedback(item.roundIndex)"
                  >
                    <template #reference>
                      <div>
                        <el-tooltip :content="$t('CHAT.RATING')" placement="top">
                          <el-button
                            class="chat-operation-button"
                            size="large"
                            :icon="MoreFilled"
                            text
                          />
                        </el-tooltip>
                      </div>
                    </template>
                    <div class="chat-review" v-loading="feedbackLoading">
                      <el-form
                        :model="reviewForm"
                        label-width="75px"
                        size="large"
                      >
                        <el-form-item :label="$t('CHAT.QA_CATEGORY')" prop="quesType">
                          <el-select
                            v-model="reviewForm.quesType"
                            :placeholder="$t('COMMON.PLS_SELECT')"
                            :teleported="false"
                          >
                            <el-option
                              v-for="item in user.feedbackSelect"
                              :key="item"
                              :label="item"
                              :value="item"
                            />
                          </el-select>
                        </el-form-item>
                        <el-form-item :label="$t('CHAT.QA_RATING')" prop="score">
                          <el-rate v-model="reviewForm.score" clearable />
                        </el-form-item>
                        <el-form-item prop="messages" label-width="0">
                          <el-input
                            v-model="reviewForm.messages"
                            type="textarea"
                            :rows="3"
                            resize="none"
                            :placeholder="$t('COMMON.PLS_INPUT')"
                          />
                        </el-form-item>
                        <el-button class="chat-review-btn" @click="commitFeedback(item.roundIndex, index)">
                          {{ $t('COMMON.SUBMIT') }}
                        </el-button>
                      </el-form>
                    </div>
                  </el-popover>
                </div>
              </div>
            </li>
          </ul>
          <el-empty v-else :description="$t('CHAT.START_CHAT')" />
        </el-scrollbar>
        <div v-if="user.scenes.includes(route.query.scene)" class="chat-view-input">
          <img src="@/assets/chatgpt.png" width="24" height="24" style="margin-bottom: 8px" />
          <div class="input-layout">
            <div v-if="showWarning" class="input-warning">
              <div class="input-warning-text">{{ $t('CHAT.INPUT_WARNING') }}</div>
              <div class="input-warning-close" @click="showWarning = false">×</div>
            </div>
            <el-input
              v-model="userInput"
              type="textarea"
              :autosize="{ minRows: 1, maxRows: 4 }"
              resize="none"
              @keydown.enter="handleInputEnter"
            />
          </div>
          <el-button
            class="send-button"
            size="large"
            :icon="Position"
            text
            :loading="sendLoading"
            @click="sendMessage()"
          />
        </div>
      </div>
      <el-popover
        v-if="route.query.scene === 'chat_with_data' || route.query.scene === 'chat_knowledge'"
        ref="promptPopoverRef"
        placement="top"
        :width="280"
        trigger="click"
      >
        <template #reference>
          <div class="prompt-button">
            <el-tooltip :content="$t('CHAT.CLICK_PROMPT')" placement="top">
              <el-button
                size="large"
                :icon="Document"
                text
                round
              />
            </el-tooltip>
          </div>
        </template>
        <div class="prompt-inner">
          <el-tabs v-model="promptTabName" @tab-change="changePromptTab" v-if="isAdmin">
            <el-tab-pane :label="$t('PROMPT.SYSTEM')" name="system">
              <el-scrollbar class="prompt-inner-content">
                <ul v-if="systemPromptList.length" class="prompt-inner-list">
                  <li
                    v-for="item in systemPromptList"
                    :key="item.id"
                    class="prompt-inner-item"
                    :class="{ active: selectSystemPrompt === item.id }"
                    @click="setPrompt(item.id, 'system')"
                  >
                    <div>
                      <div class="prompt-inner-item-title">{{ item.promptName }}</div>
                      <!-- <div class="prompt-inner-item-desc">
                        场景：{{ item.chatScene }}，次级场景：{{ item.subChatScene }}
                      </div> -->
                    </div>
                  </li>
                </ul>
                <el-empty v-else />
              </el-scrollbar>
            </el-tab-pane>
            <el-tab-pane :label="$t('PROMPT.APPLICATION')" name="application">
              <el-scrollbar class="prompt-inner-content">
                <ul v-if="appPromptList.length" class="prompt-inner-list">
                  <li
                    v-for="item in appPromptList"
                    :key="item.id"
                    class="prompt-inner-item"
                    :class="{ active: selectAppPrompt === item.id }"
                    @click="setPrompt(item.id, 'app')"
                  >
                    <div>
                      <div class="prompt-inner-item-title">{{ item.promptName }}</div>
                    </div>
                  </li>
                </ul>
                <el-empty v-else />
              </el-scrollbar>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-popover>
    </div>
  </div>
</template>

<style lang="less" scoped>
.chat-wrap {
  display: flex;
  flex: 1;
  background-color: #fff;
  .left-bar {
    width: 266px;
  }
}
.chat-view {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  .chat-view-top {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-bottom: 1px solid #f3f4f6;
  }
  .chat-view-layout {
    display: flex;
    flex-direction: column;
    //flex: 1;
    //height: calc(100vh - 49px);
    height: calc(100vh - 100px);
    .chat-content {
      flex: 1;
      padding: 0 48px;
      .chat-content-loading {
        height: 50px;
      }
      .chat-content-list {
        color: #0f172a;
        font-size: 16px;
        line-height: 28px;
        padding-bottom: 32px;
        .welcome-placeholder {
          height: 24px;
        }
        .chat-content-item {
          padding: 24px 16px;
          border-radius: 12px;
          overflow-wrap: break-word;
          .chat-ai-icon {
            width: 24px;
            height: 24px;
            margin-top: 4px;
            margin-right: 16px;
          }
        }
        .chat-content-item.human {
          .chat-human-icon {
            width: 18px;
            height: 18px;
            margin-top: 7px;
            margin-right: 16px;
          }
        }
        .chat-content-item.view {
          background: #f1f5f9;
          .chat-operation {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            border-top: 1px solid #e5e7eb;
            margin-top: 16px;
            padding-top: 16px;
            .chat-operation-button {
              font-size: 16px;
              padding: 12px;
            }
          }
        }
        .chat-content-item.welcome {
          background: #f1f5f9;
          .welcome-advices {
            display: flex;
            flex-wrap: wrap;
            padding-bottom: 8px;
            .el-tag {
              cursor: pointer;
              margin: 12px 12px 0 0;
              font-size: 14px;
            }
          }
        }
      }
      .el-empty {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
      }
    }
    .chat-view-input {
      display: flex;
      align-items: flex-end;
      padding: 24px 48px 40px;
      position: relative;
      .input-layout {
        flex: 1;
        margin: 0 8px;
        .input-warning {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-left: 16px;
          background: #eeeeee;
          border-radius: 4px;
          .input-warning-text {
            padding: 8px 0;
            line-height: 1.5;
          }
          .input-warning-close {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            font-size: 30px;
            font-weight: lighter;
            cursor: pointer;
          }
        }
        :deep(.el-textarea__inner) {
          font-size: 16px;
          padding: 8px 11px;
        }
      }
      .send-button {
        font-size: 18px;
        padding: 12px;
      }
      &::after {
        content: '';
        width: 100%;
        height: 32px;
        position: absolute;
        top: -32px;
        background-image: linear-gradient(to top, #fff, transparent);
      }
    }
  }
  .prompt-button {
    position: absolute;
    bottom: 128px;
    right: 25px;
    width: 40px;
    height: 40px;
    .el-button {
      font-size: 18px;
      width: 40px;
      height: 40px;
      box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    }
  }
}
.chat-review {
  .chat-review-btn {
    width: 100%;
    margin-top: 12px;
  }
}
.prompt-inner {
  .prompt-inner-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    .prompt-inner-label {
      width: 100px;
      font-weight: bold;
      color: #000000;
    }
    .el-select {
      flex: 1;
    }
  }
  .prompt-inner-content {
    margin: 0 -12px;
    :deep(.el-scrollbar__wrap) {
      max-height: 400px;
      .prompt-inner-list {
        padding: 0 12px;
        .prompt-inner-item {
          padding: 12px;
          line-height: 1.5;
          border-bottom: 1px solid rgba(5, 5, 5, 0.06);
          cursor: pointer;
          &.active {
            background: #e7ecf9;
          }
          &:hover {
            background: #e7ecf9;
          }
          .prompt-inner-item-title {
            color: rgba(0, 0, 0, 0.88);
          }
          .prompt-inner-item-desc {
            color: rgba(0, 0, 0, 0.45);
          }
        }
        .prompt-inner-item:last-child {
          border: none;
        }
      }
    }
  }
}
</style>
