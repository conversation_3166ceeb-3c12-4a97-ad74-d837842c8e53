<script setup>
import { useI18n } from 'vue-i18n'
import { Plus } from '@element-plus/icons-vue'
import { ref } from 'vue'
import databaseDialog from './databaseDialog.vue'
import metadataDialog from './metadataDialog.vue'
import apiDialog from './apiDialog.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import * as api from './api'

import { useRoute } from 'vue-router'

const { t } = useI18n()

const route = useRoute()

// 数据源类型列表
const dbTypeList = ref([])
// 获取数据源类型列表
const getDbTypeList = async () => {
  const res = await api.getDbTypeList()
  // window.top === window.self ? props.dbTypeList : props.dbTypeList.filter(item => props.iframeType.includes(item))
  // if(window.top === window.self) {
  //   dbTypeList.value = res || []
  // }else {
  //   dbTypeList.value = ["pbi"]
  // }
  if(route.query?.type) {
    dbTypeList.value = ["pbi"]
  }else {
    dbTypeList.value = res || []
  }
}
// 用户列表
const userList = ref([])
// 获取用户列表
const getUserList = async () => {
  const res = await api.getUserList()
  userList.value = res || []
}

// 搜索条件
const searchObj = {
  dbBusiName: '',
  dbType: route.query?.type || '',
  desc: '',
  createdBy: ''
}
const searchForm = ref(JSON.parse(JSON.stringify(searchObj)))
// 表格Loading
const tableLoading = ref(false)
// 表格数据
const tableData = ref([])
// 分页相关
const pageNum = ref(1)
const pageSize = ref(10)
const pageTotal = ref(0)
//在运营智能体里显示的类型
// const iframeType = ['pbi']
// 获取数据源列表
const getDbList = async (page) => {
  tableLoading.value = true
  if (page) {
    pageNum.value = page
  }
  try {
    const res = await api.getDbList({
      ...searchForm.value,
      pageNum: pageNum.value,
      pageSize: pageSize.value
    })
    // tableData.value = window.top === window.self ? (res?.list || []) : (res?.data.filter(item => iframeType.includes(item)) || [])
    tableData.value = res?.list || []
    pageTotal.value = res?.total || 0
    tableLoading.value = false
  } catch (e) {
    tableLoading.value = false
  }
}
// 重置搜索条件
const resetSearch = () => {
  searchForm.value = JSON.parse(JSON.stringify(searchObj))
  getDbList(1)
}

// 数据源对话框实例
const dbDialogRef = ref()
const dbData = ref({})
const dialogType = ref('')
// 打开数据源对话框
const openDbDialog = (type, data = {}) => {
  const _data = JSON.parse(JSON.stringify(data))
  if (type === 'copy') {
    delete _data.id
  }
  dialogType.value = type
  dbData.value = _data
  dbDialogRef.value.dialogVisible = true
}

// 删除数据源
const deleteDb = (id) => {
  ElMessageBox.confirm(
    t('COMMON.DELETE_CONFIRM'),
    t('COMMON.DELETE'),
    { type: 'warning', confirmButtonText: '确定', cancelButtonText: '取消', closeOnClickModal: false }
  ).then(async () => {
    await api.deleteDb({ id })
    ElMessage.success(t('COMMON.DELETE_SUCCESS'))
    if (pageNum.value !== 1 && tableData.value.length === 1) {
      pageNum.value--
    }
    getDbList()
  })
}

// metadata对话框实例
const metaDialogRef = ref()
const dbId = ref(null)
// 打开metadata对话框
const openMetaDialog = (row) => {
  dbId.value = row.id
  dbData.value = JSON.parse(JSON.stringify(row))
  metaDialogRef.value.dialogVisible = true
}

// 绑定api对话框实例
const apiDialogRef = ref()
const apiData = ref({})
// 打开绑定api对话框
const openApiDialog = (id, data) => {
  apiData.value = {
    connectId: id,
    apiProperty: data ? data.apiProperty : 2,
    apiIdList: data?.apiIdList || []
  }
  apiDialogRef.value.dialogVisible = true
}

getDbTypeList()
getUserList()
getDbList()
</script>

<template>
  <div class="database-view">
    <div class="search-box">
      <el-input
        maxlength="30"
        v-model="searchForm.dbBusiName"
        :placeholder="$t('DATA_SOURCE.INPUT_NAME')"
        clearable
      />
      <el-select
        v-if="!route.query?.type"
        v-model="searchForm.dbType"
        :placeholder="$t('DATA_SOURCE.SELECT_TYPE')"
        filterable
        clearable
      >
        <el-option
          v-for="item in dbTypeList"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
      <!-- <el-input
        maxlength="30"
        v-model="searchForm.desc"
        :placeholder="$t('DATA_SOURCE.INPUT_DESC')"
        clearable
      /> -->
      <el-select
        v-model="searchForm.createdBy"
        :placeholder="$t('DATA_SOURCE.SELECT_CREATOR')"
        filterable
        clearable
      >
        <el-option
          v-for="item in userList"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
      <div class="search-btn-left">
        <el-button type="primary" @click="getDbList(1)">{{ $t('COMMON.SEARCH') }}</el-button>
        <el-button @click="resetSearch()">{{ $t('COMMON.RESET') }}</el-button>
      </div>
      <div class="search-btn-right">
        <el-button type="primary" @click="openDbDialog('create')">
          <i class="iconfont icon-tianjia1"></i>
          <span>{{ $t('COMMON.CREATE') }}</span>
        </el-button>
      </div>
    </div>
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      class="table-box"
      header-cell-class-name="table-box-header"
      height="100%"
    >
      <el-table-column prop="dbBusiName" :label="$t('DATA_SOURCE.DATA_SOURCE_NAME')" min-width="175" />
      <el-table-column prop="dbType" :label="$t('DATA_SOURCE.DATA_SOURCE_TYPE')" width="165" />
      <el-table-column prop="comment" :label="$t('DATA_SOURCE.DATA_SOURCE_DESC')" min-width="200" />
      <el-table-column prop="createdBy" :label="$t('COMMON.CREATOR')" width="100" />
      <el-table-column prop="metadataOwner" :label="$t('DATA_SOURCE.METADATA_OWNER')" min-width="100" />
      <el-table-column prop="updatedTime" :label="$t('COMMON.LAST_UPDATE')" width="165" />
      <el-table-column :label="$t('COMMON.OPERATIONS')" width="160">
        <template #default="scope">
          <div class="table-box-btn">
            <el-button v-if="scope.row.editAuth" link @click="openDbDialog('edit', scope.row)">
              <el-tooltip :content="$t('COMMON.EDIT')" placement="top">
                <i class="iconfont icon-edit"></i>
              </el-tooltip>
            </el-button>
            <el-button link @click="openDbDialog('copy', scope.row)">
              <el-tooltip :content="$t('COMMON.COPY')" placement="top">
                <i class="iconfont icon-copy"></i>
              </el-tooltip>
            </el-button>
            <el-button v-if="scope.row.delAuth" link @click="deleteDb(scope.row.id)">
              <el-tooltip :content="$t('COMMON.DELETE')" placement="top">
                <i class="iconfont icon-delete"></i>
              </el-tooltip>
            </el-button>
            <el-button v-if="scope.row.metadataAuth" link @click="openMetaDialog(scope.row)">
              <el-tooltip :content="$t('COMMON.SETTING')" placement="top">
                <i class="iconfont icon-shezhi"></i>
              </el-tooltip>
            </el-button>
          </div>
<!--          <div class="table-box-btn">-->
<!--            <el-button v-if="scope.row.editAuth" link @click="openDbDialog('edit', scope.row)">-->
<!--              {{ $t('COMMON.EDIT') }}-->
<!--            </el-button>-->
<!--            <el-button link @click="openDbDialog('copy', scope.row)">-->
<!--              {{ $t('COMMON.COPY') }}-->
<!--            </el-button>-->
<!--            <el-button v-if="scope.row.delAuth" link @click="deleteDb(scope.row.id)">-->
<!--              {{ $t('COMMON.DELETE') }}-->
<!--            </el-button>-->
<!--            <el-button v-if="scope.row.apiAuth" link @click="openApiDialog(scope.row.id, scope.row.dataApiMapping)">-->
<!--              {{ $t('DATA_SOURCE.BIND_API') }}-->
<!--            </el-button>-->
<!--            <el-button v-if="scope.row.metadataAuth" link @click="openMetaDialog(scope.row.id)">-->
<!--              {{ $t('DATA_SOURCE.METADATA_MGT') }}-->
<!--            </el-button>-->
<!--          </div>-->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="pagination-box"
      v-model:current-page="pageNum"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="pageTotal"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="getDbList(1)"
      @current-change="getDbList()"
    />
    <database-dialog
      ref="dbDialogRef"
      :dbTypeList="dbTypeList"
      :userList="userList"
      :dbData="dbData"
      :dialogType="dialogType"
      @getDbList="getDbList"
    />
    <metadata-dialog
      ref="metaDialogRef"
      :dbId="dbId"
      :dbData="dbData"
      @getDbList="getDbList"
    />
    <api-dialog
      ref="apiDialogRef"
      :apiData="apiData"
      @getDbList="getDbList"
    />
  </div>
</template>

<style lang="less" scoped>
.database-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 24px;
  background: #fff;
  flex: 1;
  min-height: 0;
  .table-box {
    .table-box-btn {
      i {
        color: #568CF4;
      }
    }
  }
  .search-box {
    .search-btn-right {
      i {
        margin-right: 4px;
        padding-top: 2px;
        font-size: 14px;
      }
    }
  }
}
</style>
