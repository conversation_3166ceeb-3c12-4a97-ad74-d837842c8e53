
import request from '@/utils/axios'

const url = '/'

function getFilterList(params) {
  return request({
    url: url + 'scene/filterList',
    method: 'get',
    params
  })
}

function getAppList(data) {
  return request({
    url: url + 'scene/list',
    method: 'post',
    data
  })
}

function toFavorite(data) {
  return request({
    url: url + 'scene/saveFavorite',
    method: 'post',
    data,
  })
}

export {
  getAppList,
  getFilterList,
  toFavorite
}