<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
let router = useRouter()
let route = useRoute()

let menuList = ref([
  { id: 1, icon: 'icon-a-AgentChat1', text: 'AGENT_MARKET', path: 'chat_with_scene' },
  { id: 2, icon: 'icon-a-ChatData', text: 'INTELLIGENT_ANALYSIS', path: 'chat_with_data' },
  { id: 3, icon: 'icon-a-ChatKnowledge1', text: 'KNOWLEDGE_SERVICE', path: 'chat_knowledge' }
  ])

let unfoldMenu = ref(false) // 展开菜单

const toggleMenu = () => {
  unfoldMenu.value = !unfoldMenu.value
}
const createChat = (path) => {
  if(path === 'chat_with_scene') {
    router.push({
      path: '/agentChat'
    })
    return
  }
  router.push({
    path: '/chatNow',
    query: { scene: path }
  })
}
const goPath = (path) => {
  router.push(path)
}

const isActiveMenu = computed(() => {
  return (itemPath) => {
    return route.query.scene === itemPath || (route.path === '/agentChat' && itemPath === 'chat_with_scene')
  }
})

</script>

<template>
<div class="left-nav" :class="{'fold-nav': !unfoldMenu}">
  <div class="menu-wrap">
    <div class="menu-list" v-for="item in menuList" :key="item.path"  :class="{ 'active-menu-list': isActiveMenu(item.path) }" @click="createChat(item.path)">
      <i class="iconfont" :class="item.icon"></i>
      <span class="menu-text">{{ $t(`COMMON.${item.text}`) }}</span>
    </div>
  </div>
  <div class="menu-bottom-wrap">
    <div class="menu-list" :class="{ 'active-menu-list': route.path === '/settings' }" @click="goPath('/settings')">
      <i class="iconfont icon-setting"></i>
      <span class="menu-text">{{ $t('COMMON.SETTING') }}</span>
    </div>
    <!-- <div class="menu-list" :class="{ 'active-menu-list': route.path === '/logMonitor' }" @click="goPath('/logMonitor')">
      <i class="iconfont icon-rizhijiankong"></i>
      <span class="menu-text">{{ $t('COMMON.LOG_MONITOR') }}</span>
    </div> -->
  </div>
  <div class="menu-collapse" @click="toggleMenu">
    <el-icon :size="14" :class="{unfold: !unfoldMenu}"><DArrowLeft /></el-icon>
  </div>
</div>
</template>

<style scoped lang="less">
@import url('//at.alicdn.com/t/c/font_4728306_rxazw3hrbqr.css');
.left-nav {
  position: relative;
  //height: calc(100vh - 50px);
  width: 210px;
  padding: 44px 11px 10px 9px;
  background: linear-gradient( 120deg, #F0F4F9 0%, #E7EDFD 100%);
  transition: all .2s ease;
  //background: linear-gradient( 120deg, #F0F4F9 0%, #090 100%);
  .menu-list {
    display: flex;
    width: 190px;
    height: 42px;
    padding: 0 13px;
    line-height: 42px;
    color: #353B50;
    margin-bottom: 14px;
    transition: all .2s ease;
    .menu-text {
      flex: 1;
      overflow: hidden;
      white-space: no-wrap;
    }
    .iconfont {
      font-size: 16px;
      margin-right: 10px;
      color: #8190b0;
    }
    &:hover {
      cursor: pointer;
      background: #568CF4;
      border-radius: 4px 4px 4px 4px;
      color: #fff;
      .iconfont {
        color: #fff;
      }
    }
  }
  .active-menu-list {
    background: #568CF4;
    border-radius: 4px 4px 4px 4px;
    color: #fff;
    .iconfont {
      color: #fff;
    }
  }
  .menu-bottom-wrap {
    position: absolute;
    bottom: 48px;
  }
  .menu-collapse {
    position: absolute;
    right: 0;
    top: 50%;
    width: 16px;
    height: 30px;
    line-height: 35px;
    text-align: center;
    background-color: #568CF4;
    border-radius: 4px 0px 0px 4px;
    color: #fff;
    cursor: pointer;
    .el-icon.unfold {
      transform: rotate(180deg);
    }
  }
}
.fold-nav {
  width: 60px;
  .menu-list {
    width: 42px;
  }
}
</style>
