<script setup>
import { useI18n } from 'vue-i18n'
import { ref, computed } from 'vue'
import { ElLoading } from 'element-plus'
import * as api from './api'

const { locale, t } = useI18n()

// 声明props
const props = defineProps({
  promptType: String,
  promptTypeList: Object,
  applySceneList: Array,
  promptData: Object
})

// 接收传递的方法
const emit = defineEmits(['getPromptList'])

// 是否显示对话框
const dialogVisible = ref(false)
const dialogView = ref(false)
// 对话框标题
const dialogTitle = computed(() => {
  if (dialogView.value) {
    return t('PROMPT.VIEW_PROMPT')
  } else {
    return props.promptData.id ? t('PROMPT.EDIT_PROMPT') : t('PROMPT.CREATE_PROMPT')
  }
})

// 提示语表单数据
const promptObj = {
  id: undefined,
  promptType: '',
  chatScene: '',
  promptName: '',
  applyScene: '',
  subChatScene: '',
  content: ''
}
const promptForm = ref(JSON.parse(JSON.stringify(promptObj)))
const promptRules = computed(() => {
  return {
    chatScene: [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }],
    promptName: [{ required: true, message: t('COMMON.PLS_INPUT'), trigger: 'blur' }],
    applyScene: [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }],
    subChatScene: [{ required: true, message: t('COMMON.PLS_INPUT'), trigger: 'blur' }],
    content: [{ required: true, message: t('COMMON.PLS_INPUT'), trigger: 'blur' }]
  }
})
// 填充数据
const resetData = (isOpen) => {
  if (isOpen) {
    promptForm.value = {
      id: props.promptData.id || undefined,
      promptType: props.promptData.promptType || props.promptType,
      chatScene: props.promptData.chatScene || '',
      promptName: props.promptData.promptName || '',
      applyScene: props.promptData.applyScene || '',
      subChatScene: props.promptData.subChatScene || '',
      content: props.promptData.content || (props.promptType === 'system' ? t('PROMPT.DEFAULT_CONTENT') : '')
    }
  } else {
    promptForm.value = JSON.parse(JSON.stringify(promptObj))
  }
}
// 保存提示语
const savePrompts = async () => {
  const loading = ElLoading.service({ target: '.el-dialog' })
  try {
    if (promptForm.value.id) {
      promptForm.value.id = Number(promptForm.value.id)
      await api.updatePrompt(promptForm.value)
    } else {
      await api.addPrompt(promptForm.value)
    }
    loading.close()
    dialogVisible.value = false
    emit('getPromptList')
  } catch (e) {
    loading.close()
  }
}
// 提示语表单实例
const promptFormRef = ref()
// 提交表单
const submitForm = () => {
  promptFormRef.value.validate((valid) => {
    if (valid) {
      savePrompts()
    }
  })
}

// 暴露属性和方法
defineExpose({ dialogVisible, dialogView })
</script>

<template>
  <div class="prompt-dialog">
    <el-dialog
      class="setting-page-dialog-wrap"
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
      @open="resetData(true)"
      @closed="resetData(false)"
    >
      <el-form
        :disabled="dialogView"
        ref="promptFormRef"
        :model="promptForm"
        :rules="promptRules"
        :label-width="locale === 'en_US' ? '145px' : '100px'"
      >
        <el-form-item prop="chatScene" :label="$t('PROMPT.PROMPT_TYPE')">
          <el-select v-model="promptForm.chatScene">
            <el-option
              v-for="(val, key) in props.promptTypeList"
              :key="key"
              :label="val"
              :value="key"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="promptName" :label="$t('PROMPT.PROMPT_NAME')">
          <el-input
            v-model="promptForm.promptName"
            maxlength="50"
            :placeholder="$t('COMMON.PLS_INPUT')"
            :disabled="!!props.promptData.id"
          />
        </el-form-item>
        <el-form-item v-if="props.promptType === 'application'" prop="applyScene" :label="$t('PROMPT.APP_SCENE')">
          <el-select v-model="promptForm.applyScene" :placeholder="$t('COMMON.PLS_SELECT')">
            <el-option
              v-for="item in props.applySceneList"
              :key="item.value"
              :label="item[locale === 'en_US' ? 'nameEn' : 'nameCn']"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="props.promptType === 'system'" prop="subChatScene" :label="$t('PROMPT.ROLE_DESC')">
          <el-input
            v-model="promptForm.subChatScene"
            type="textarea"
            :rows="6"
            resize="none"
            :placeholder="$t('COMMON.PLS_INPUT')"
          />
        </el-form-item>
        <el-form-item prop="content">
          <template #label>
            <span>{{ $t('PROMPT.CONTENT') }}</span>
            <el-tooltip :content="$t('PROMPT.CONTENT_TIP')" placement="top" popper-class="prompt-tip">
              <QuestionFilled class="question-icon" />
            </el-tooltip>
          </template>
          <el-input
            v-model="promptForm.content"
            type="textarea"
            maxlength="3500"
            :rows="8"
            resize="none"
            :placeholder="$t('COMMON.PLS_INPUT')"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div v-if="!dialogView">
          <el-button @click="dialogVisible = false">{{ $t('COMMON.CANCEL') }}</el-button>
          <el-button type="primary" @click="submitForm()">{{ $t('COMMON.SUBMIT') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.prompt-dialog {
  :deep(.el-dialog__body) {
    padding: 18px 20px 0;
    .el-select {
      width: 100%;
    }
    .question-icon {
      width: 14px;
      height: 14px;
      margin: 9px 0 0 4px;
    }
  }
}
</style>
<style lang="less">
.prompt-tip {
  max-width: 260px;
}
</style>
