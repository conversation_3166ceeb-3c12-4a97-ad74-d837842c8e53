import { ElMessage } from 'element-plus'
import i18n from "@/lang/index.js";
const { t } = i18n.global
const getDataZoom = (data) => {
  let dataZoom = []
  if (data.timeAxis && data.source.length > 60) {
    dataZoom = [{
      type: 'slider',
      start: 70,
      end: 100
    }]
    if (data.charType === 'BAR_CHART') {
      dataZoom = [{
        type: 'slider',
        start: 90,
        end: 100,
        orient: 'vertical'
      }]
    }
  }
  return dataZoom
}
const getAxisData = (data) => {
  let axis = {
    type: 'category'
  }
  if (data.axisData && data.axisData.length > 0) {
    axis.data = data.axisData[0].data.map(item => item.value)
  }
  return axis
}
const getTipTxt = (data) => {
  let tiptxt = '';
  if (data.source.length === 0) tiptxt = 'no data'
  return tiptxt
}
// 类目轴 柱/折图
const createBarAndLineOption = (data) => {
  let seriesList = []
  if (data.source.length > 0) {
    seriesList = data.dimension.slice(1).map(item => {
      if (data.charType == 'COLUMN_CHART') return { type: 'bar', barMaxWidth: 50, barMinWidth: 10, name: item, itemStyle: { borderRadius: [5, 5, 0, 0] } }
      if (data.charType == 'LINE_CHART') return { type: 'line', smooth: true, name: item }
    })
  }
  return {
    title: {
      left: 'center',
      padding: 10,
      text: getTipTxt(data),
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    legend: {
      top: 10
    },
    dataset: {
      dimensions: data.dimension,
      source: data.source
    },
    xAxis: getAxisData(data),
    yAxis: {},
    dataZoom: getDataZoom(data),
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    series: seriesList
  }
};

// 条形图
const createColumnOption = (data) => {
  let seriesList = []
  if (data.source.length > 0) {
    seriesList = data.dimension.slice(1).map(item => {
      return { type: 'bar', name: item,  barMaxWidth: 50, barMinWidth: 10, itemStyle: { borderRadius: [0, 5, 5, 0] } }
    })
  }
  return {
    title: {
      text: getTipTxt(data),
      left: 'center',
      padding: 10,
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    legend: { top: 10 },
    dataset: {
      dimensions: data.dimension,
      source: data.source
    },
    xAxis: {
      type: 'value'
    },
    yAxis: getAxisData(data),
    dataZoom: getDataZoom(data),
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    series: seriesList
  }
};
// 面积图
const createAreaOption = (data) => {
  let seriesList = []
  if (data.source.length > 0) {
    seriesList = data.dimension.slice(1).map(item => {
      return { type: 'line', smooth: true, name: item, areaStyle: {} }
    })
  }
  return {
    title: {
      text: getTipTxt(data),
      left: 'center',
      padding: 10,
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    legend: { top: 10 },
    dataset: {
      dimensions: data.dimension,
      source: data.source
    },
    xAxis: getAxisData(data),
    yAxis: {
      type: 'value'
    },
    dataZoom: getDataZoom(data),
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    series: seriesList
  }
}
// 饼图
const createPieOption = (data) => {
  if (data.timeAxis && data.source.length > 60) {
    ElMessage({
      message: t('CHAT.NO_SUPPORT_PIE'),
      type: 'warning'
    })
    return {}
  }
  let pieName = data.dimension[0]
  const seriesList = []
  if (data.source.length > 0) {
    data.dimension.forEach((item, index) => {
      if (index === 0) return false
      seriesList.push({
        type: 'pie',
        name: item,
        radius: 100,
        center: [`${index * 25}%`, '50%'],
        encode: {
          "itemName": pieName,
          "value": data.dimension[index]
        }
      })
    })
  }
  return {
    title: {
      text: getTipTxt(data),
      left: 'center',
      padding: 10,
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      top: 10
      // orient: 'vertical'
    },
    dataset: {
      dimensions: data.dimension,
      source: data.source
    },
    series: seriesList
    // series: [{
    //   type: 'pie',
    //   name: data.dimension[1],
    //   radius: 100,
    //   center: ['50%', '50%'],
    //   encode: {
    //     "itemName": data.dimension[0],
    //     "value": data.dimension[1]
    //   }
    // }]
  }
};

export default function createChartOption(data) {
  switch (data.charType) {
    case 'COLUMN_CHART': return createBarAndLineOption(data)
    case 'LINE_CHART': return createBarAndLineOption(data)
    case 'BAR_CHART': return createColumnOption(data)
    case 'AREA_CHART': return createAreaOption(data)
    case 'PIE_CHART': return createPieOption(data)
    default: return {}
  }
};
