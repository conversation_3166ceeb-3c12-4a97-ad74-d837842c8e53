<script setup>
import { reactive, ref, watch } from 'vue'
import { Search, WarningFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

import { useRouter } from 'vue-router'
import * as api from './api/index.js'

// import { copyToClipboard } from '../../utils/util.js'

import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()

let router = useRouter()
const props = defineProps({
  currentTab: 'String'
})
let currentTab = 'all'

const formInline = reactive({
  sceneName: '',
  sceneType: ''
})
const total = ref(0)
const pageNo = ref(1)
const pageSize = ref(8)

// 场景
let scenceList = ref([])
// 获取应用查询条件列表
const getFilterList = async () => {
  const res = await api.getFilterList()
  scenceList.value = res.applyScene || []
}
getFilterList()

// 列表数据
const agentDataList = ref([])
const getAgentDataList = async (flag) => {
  if (flag) pageNo.value = 1
  const res = await api.getAppList({
    ...formInline,
    favoriteStatus: currentTab === 'all' ? '' : 1,
    publishStatus: '1',
    pageNum: pageNo.value,
    pageSize: pageSize.value
  })
  total.value = res?.total || 0
  agentDataList.value = [...res?.list] || []
}
getAgentDataList()

const handleCollecBtn = async (data) => {
  let msg = ''
  let status = ''
  if (data.favoriteStatus == '1') {
    msg = t('AGENT.CANCEL_FAVORITE')
    status = '0'
  } else {
    msg = t('AGENT.ADD_FAVORITE')
    status = '1'
  }
  // 收藏类型 1：agent chat 2：chat data
  let params = {
    favoriteId: data.id,
    favoriteType: 1,
    favoriteStatus: status
  }

  await api.toFavorite(params)
  ElMessage({
    dangerouslyUseHTMLString: true,
    message: msg,
    type: 'success',
    customClass: 'agent-message-alert'
  })
  getAgentDataList()
}
const handleShare = (data) => {
  // &id=${item.convUid}
  const url = `${location.origin}/chatNow?scene=chat_with_scene&select=${data.sceneName}`

  const dom = document.createElement('textarea')
  dom.setAttribute('readonly', 'readonly')
  dom.value = url
  document.body.appendChild(dom)
  dom.select()
  const result = document.execCommand('copy')

  let msg = `${t('AGENT.COPY_SUCCESS')}<br/><span class="url">${url}</span>`
  if (!result) msg = t('AGENT.COPY_FAIL')

  ElMessage({
    dangerouslyUseHTMLString: true,
    message: msg,
    type: result ? 'success' : 'error',
    customClass: 'agent-message-alert'
  })

  document.body.removeChild(dom)
}

const onSubmit = () => {
  if (formInline.sceneName === '' && formInline.sceneType === '') return
  getAgentDataList(true)
}
const onReset = () => {
  formInline.sceneName = ''
  formInline.sceneType = ''
  getAgentDataList(true)
}

const handlePageNoChange = (no) => {
  pageNo.value = no
  getAgentDataList()
}

const handleCardClick = (data) => {
  if(data.appType === "mutli-agent") {
    window.alert('多智能体对话功能暂未发布')
    return
  }

  if(data.projectFlag === 'pbi') {
    router.push({
      path: '/chatPbi',
      query: { scene: 'chat_with_scene', select: data.sceneName, model: data.model }
    })
  }else {
    router.push({
      path: '/chatNow',
      query: { scene: 'chat_with_scene', select: data.sceneName, model: data.model }
    })
  }
}

watch(() => props.currentTab,
  (val) => {
    currentTab = val
    formInline.sceneName = ''
    formInline.sceneType = ''
    getAgentDataList(true)
  }
)

</script>

<template>
  <div class="tab-content">
    <el-form :inline="true" :model="formInline" class="form-inline">
      <el-form-item>
        <el-input v-model="formInline.sceneName" :placeholder="$t('AGENT.INPUT_NAME')" :prefix-icon="Search"
          clearable />
      </el-form-item>
      <el-form-item>
        <el-select v-model="formInline.sceneType" :placeholder="$t('AGENT.SELECT_SCENE')" clearable>
          <el-option v-for="item in scenceList" :key="item.value" :label="locale === 'zh_CN' ? item?.nameCn : item?.nameEn" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button class="filter-button filter-query" type="primary" @click="onSubmit">{{ $t('COMMON.SEARCH')
          }}</el-button>
        <el-button class="filter-button" type="default" @click="onReset">{{ $t('COMMON.RESET') }}</el-button>
      </el-form-item>
    </el-form>
    <div class="card-list">
      <el-scrollbar>
        <el-space wrap :size="16" v-if="agentDataList && agentDataList.length">
          <div class="card-item" v-for=" item in agentDataList" :key="item.id" @click="handleCardClick(item)">
            <div class="card-title">
              <div class="corner-label">
                <el-text truncated>
                  {{ scenceList.find(x => x.value === item.sceneType)?.[locale === 'zh_CN' ? 'nameCn' : 'nameEn'] }}
                </el-text>
              </div>
              <i class="iconfont icon-duihua2 title-icon" v-if="item.appType === 'native'"></i>
              <i class="iconfont icon-NetworkAlternative title-icon" v-if="item.appType === 'mutli-agent'"></i>
              <el-text class="title-txt" truncated>{{ item.sceneName }}</el-text>
            </div>
            <div class="card-label">
              <el-tag disable-transitions>
                <el-text truncated>{{ item.createdBy }}</el-text>
              </el-tag>
              <el-tag disable-transitions>{{ item.updatedTime && item.updatedTime.slice(0, -9) }}</el-tag>
            </div>
            <div class="card-content">
              <el-text line-clamp="3">{{ item.comment }}</el-text>
            </div>
            <div class="card-bottom">
              <div>
                <el-tag type="primary" size="small" disable-transitions>{{ item.appType === 'native' ? $t('APP.NATIVE_APP') : $t('APP.MULTI_AGENT_APP') }}</el-tag>
              </div>
              <div>
                <i class="iconfont opt-icon"
                :class="{ 'icon-shoucang': !item.favoriteStatus || item.favoriteStatus == '0', 'icon-xing': item.favoriteStatus == '1' }"
                @click.stop="handleCollecBtn(item)"></i>
                <i class="iconfont icon-fenxiang opt-icon" @click.stop="handleShare(item)"></i>
              </div>
            </div>
          </div>
          <!-- 占位元素 -->
          <div v-for="item in 9" :key="item" class="card-item-empty"></div>
        </el-space>
        <el-empty v-else :image-size="100" />
      </el-scrollbar>
    </div>
    <div class="card-pagi">
      <p class="tips-ai">
        <el-icon>
          <WarningFilled />
        </el-icon>
        {{ $t('CHAT.INPUT_WARNING') }}
      </p>
      <el-pagination layout="prev, pager, next" :total="total" v-model:current-page="pageNo"
        :default-page-size="pageSize" @current-change="handlePageNoChange" />
    </div>
  </div>
</template>

<style lange="less" scoped>
@import url('//at.alicdn.com/t/c/font_4728306_8ii6k6nby9i.css');
.tab-content {
  :deep(.el-input) {
    width: 200px;
  }

  :deep(.el-input__inner::placeholder) {
    font-size: 12px;
  }

  .filter-button {
    width: 64px;

    &.filter-query {
      background-color: #568CF4;
    }
  }
}

.card-list {
  :deep(.el-space .el-space__item) {
    /* flex: 1; */
    width: calc((100% - 64px) / 4);
    min-width: 278px;
  }
}

@media (max-width: 1304px) {
  .card-list {
    :deep(.el-space .el-space__item) {
      /* flex: 1; */
      width: calc((100% - 32px) / 2);
      min-width: 278px;
    }
  }
}
@media (max-width: 716px) {
  .card-list {
    :deep(.el-space .el-space__item) {
      /* flex: 1; */
      width: calc((100% - 16px));
      min-width: 278px;
    }
  }
}

.card-item-empty {
  width: 100%;
  height: 0;
}

.card-item {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 180px;
  padding: 13px 15px 0;
  box-shadow: 0px 2px 8px 0px rgba(184, 198, 227, 0.2);
  border-radius: 8px;
  box-sizing: border-box;
  cursor: pointer;

  .card-title {
    display: flex;
    height: 30px;
    font-size: 14px;
    position: relative;
    padding-right: 60px;

    .corner-label {
      position: absolute;
      right: -15px;
      top: -13px;
      width: 100px;
      height: 40px;
      padding: 0 8px 0 32px;
      text-align: center;
      background: url(./imgs/corner.png) center no-repeat;
      background-size: contain;
      font-size: 13px;
      color: #3C78EC;
      line-height: 32px;
    }

    .title-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      background: linear-gradient(180deg, #558DF4 0%, #46AFFE 100%);
      border-radius: 4px;
      margin-right: 8px;
      font-size: 24px;
      color: #F4F8FF;
    }

    .title-txt {
      flex: 1;
      color: #0A121F;
      height: 18px;
    }
  }
  .card-label {
    margin-top: 8px;
    .el-tag {
      margin-right: 5px;
      background-color: #EFF3FA;
      border-radius: 2px;
      border: none;
      color: #6E7889;
      
      .el-text {
        color: #6E7889;
        font-size: 12px;
        max-width: 60px;
        line-height: 24px;
      }
    }
  }

  .card-content {
    flex: 1;
    height: 0;
    margin: 8px 0;
    font-size: 12px;
    color: #4B556A;

    .el-text {
      --el-text-font-size: 12px;
      letter-spacing: 1px;
    }
  }

  .card-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 32px;
    border-top: 1px solid #EEF0F4;

    .opt-icon {
      margin: 0 5px;
      color: #A5AAB3;
      font-size: 16px;


      &:hover {
        cursor: pointer;
        color: #6193F5;
      }

      &.icon-xing {
        color: #FFAF0F;
      }

      /* 
      &.icon-shoucang:hover {
        color: #6193F5;
      } */
    }
  }
}
.tab-content {
  display: flex;
  flex-direction: column;
  height: 100%;

  .form-inline {
    height: 50px;
  }

  .card-list {
    flex: 1;
    height: 0;
  }

  .card-pagi {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    /* border-top: 1px solid #e4e7ed; */

    .tips-ai {
      display: flex;
      align-items: flex-start;
      font-size: 12px;
      color: #9BA1AB;

      i {
        margin-right: 5px;
      }
    }
  }
}

.tab-content :deep(.el-space__item:nth-last-child(-n+5)) {
  background-color: red;
  padding-bottom: 0 !important;
}
</style>

<style>
.agent-message-alert {
  background-color: #F2F9ED;
}

.agent-message-alert .el-icon {
  font-size: 30px;
}

.agent-message-alert .url {
  display: flex;
  margin-top: 5px;
  font-size: 12px;
}

.permission-tips-dialog {
  position: relative;
  width: 540px;
  height: 350px;
}

.permission-tips-dialog .permission-tiptxt {
  margin: 25px 0;
}

.permission-tips-dialog .permission-tiptxt,
.permission-tips-dialog .permission-link {
  text-align: center;
  font-size: 14px;
  color: #4B556A;
}

.permission-tips-dialog .permission-link a {
  color: #568CF4;
}

.permission-tips-dialog .el-message-box__btns {
  position: absolute;
  bottom: 0;
  left: 180px;
  height: 72px;
}

.permission-tips-dialog .el-message-box__btns .el-button {
  width: 68px;
  height: 32px;
}
</style>