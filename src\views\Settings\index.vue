<script setup>
import {shallowRef, ref, computed} from 'vue'
import database from '@/views/Database/databaseView.vue'
import knowledge from '@/views/Knowledge/knowledgeView.vue'
import prompt from '@/views/Prompt/promptView.vue'
import application from '@/views/Application/appView.vue'
import user from '@/views/User/userView.vue'
import role from '@/views/Role/roleView.vue'
import plugin from '@/views/Plugin/pluginView.vue'
import { useUserStore } from '@/stores/user'

import { useI18n } from 'vue-i18n'
const { t, locale  } = useI18n()

let activeTab = ref('database')
let activeCom = shallowRef(database)

const userStore = useUserStore()
// 全部菜单
const allMenuList = computed(() => [
  {text: 'MENUS.DATA_SOURCE', path: '/database' },
  {text: 'MENUS.PROMPT', path: '/prompt' },
  {text: 'MENUS.KNOWLEDGE', path: '/knowledge' },
  {text: 'MENUS.PLUGIN', path: '/plugin' },
  {text: 'MENUS.APP', path: '/application' },
  {text: 'MENUS.USER', path: '/user' },
  {text: 'MENUS.ROLE', path: '/role' },
  // {text: t('MENUS.MENU'), path: '/menu' },
])
// 实际菜单
const menuList = computed(() => {
  return allMenuList.value.filter(menu => {
    return userStore.routes.find(item => menu.path.indexOf(item.menuPath) > -1)
  })
})

const handleChange = (tabPaneName) => {
  const currentTab = tabPaneName.paneName
  let tabComponents = {
    database,
    knowledge,
    prompt,
    user,
    role,
    plugin,
    application
  }
  activeCom.value = tabComponents[currentTab] || application
}
</script>

<template>
  <!-- class="settings-tabs" -->
<div class="settings">
  <el-tabs v-model="activeTab" type="border-card" @tab-click="handleChange">
    <el-tab-pane :label="$t(item.text)" :name="item.path.slice(1)" v-for="item in menuList" :key="item.path"></el-tab-pane>
    <!-- <el-tab-pane :label="$t('MENUS.DATA_SOURCE')" name="database"></el-tab-pane>
    <el-tab-pane :label="$t('MENUS.KNOWLEDGE')" name="knowledge"></el-tab-pane>
    <el-tab-pane :label="$t('MENUS.PROMPT')" name="prompt"></el-tab-pane>
    <el-tab-pane :label="$t('MENUS.APP')" name="application"></el-tab-pane>
    <el-tab-pane :label="$t('MENUS.USER')" name="user"></el-tab-pane>
    <el-tab-pane :label="$t('MENUS.ROLE')" name="role"></el-tab-pane>
    <el-tab-pane :label="$t('MENUS.PLUGIN')" name="plugin"></el-tab-pane> -->
  </el-tabs>
  <component :is="activeCom" />
</div>
</template>

<style lang="less" scoped>
.settings {
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}
.el-tabs {
  padding-left: 30px;
  padding-top: 4px;
  background: linear-gradient( 90deg, #E9F0FF 0%, #DEECFF 45%, #E2E7FF 99%);
}
</style>

<style>
.settings .el-tabs--border-card>.el-tabs__content {
  padding: 0;
}
.settings .el-tabs--border-card {
  border: none;
}
.settings .el-tabs--border-card>.el-tabs__header {
  background-color: transparent;
}
.settings .el-tabs--border-card>.el-tabs__header .el-tabs__item {
  border: none;
  font-size: 14px;
  color: #0A121F;
}
.settings .el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active {
  background-color: #F8FAFF;
  color: #3C78EC;
}
.settings .el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active::before {
  position: absolute;
  top: 0;
  content: '';
  width: 100%;
  border-top: 4px solid #568cf4;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
.settings .el-table__inner-wrapper::before {
  height: 0;
}
.el-table__header-wrapper {
  border-radius: 6px;
}
.el-input__inner::placeholder {
  font-size: 12px;
}
</style>
