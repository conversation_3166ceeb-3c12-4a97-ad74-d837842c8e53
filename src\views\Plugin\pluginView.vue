<script setup>
import { useI18n } from 'vue-i18n'
import { Plus } from '@element-plus/icons-vue'
import { ref } from 'vue'
import pluginDialog from './pluginDialog.vue'
import verifyDialog from './verifyDialog.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import * as api from './api'

const { t } = useI18n()

// 插件类型列表
const apiTypeList = ['其他']
// 搜索条件
const searchObj = {
  apiName: '',
  apiType: ''
}
const searchForm = ref(JSON.parse(JSON.stringify(searchObj)))
// 表格Loading
const tableLoading = ref(false)
// 表格数据
const tableData = ref([])
// 分页相关
const pageNum = ref(1)
const pageSize = ref(10)
const pageTotal = ref(0)
// 获取插件列表
const getPluginList = async (page) => {
  tableLoading.value = true
  if (page) {
    pageNum.value = page
  }
  try {
    const res = await api.getPluginList({
      ...searchForm.value,
      pageNum: pageNum.value,
      pageSize: pageSize.value
    })
    tableData.value = res?.list || []
    pageTotal.value = res?.total || 0
    tableLoading.value = false
  } catch (e) {
    tableLoading.value = false
  }
}
// 重置搜索条件
const resetSearch = () => {
  searchForm.value = JSON.parse(JSON.stringify(searchObj))
  getPluginList(1)
}

// 插件对话框实例
const pluginDialogRef = ref()
const pluginId = ref(null)
// 打开插件对话框
const openPluginDialog = (id) => {
  pluginId.value = id || null
  pluginDialogRef.value.dialogVisible = true
}

// 验证对话框实例
const verifyDialogRef = ref()
const verifyData = ref({})
// 打开验证对话框
const openVerifyDialog = (data) => {
  verifyData.value = data || {}
  verifyDialogRef.value.dialogVisible = true
}
// 是否验证成功
const changeVerified = (val) => {
  pluginDialogRef.value.changeVerified(val)
}

// 发布/下线插件
const publishPlugin = async (id, isPublish) => {
  const str = isPublish === '1' ? t('PLUGIN.PUBLISH') : t('PLUGIN.OFFLINE')
  ElMessageBox.confirm(
    `${t('COMMON.CONFIRM')}${str}?`,
    str,
    { type: 'warning', closeOnClickModal: false }
  ).then(async () => {
    await api.publishPlugin({ id, isPublish })
    ElMessage.success(`${str} ${t('COMMON.SUCCESS')}`)
    getPluginList()
  })
}

// 删除插件
const deletePlugin = (id) => {
  ElMessageBox.confirm(
    t('COMMON.DELETE_CONFIRM'),
    t('COMMON.DELETE'),
    { type: 'warning', closeOnClickModal: false }
  ).then(async () => {
    await api.deletePlugin({ id })
    ElMessage.success(t('COMMON.DELETE_SUCCESS'))
    if (pageNum.value !== 1 && tableData.value.length === 1) {
      pageNum.value--
    }
    getPluginList()
  })
}

getPluginList()
</script>

<template>
  <div class="plugin-view">
    <div class="search-box">
      <el-input
        v-model="searchForm.apiName"
        :placeholder="$t('PLUGIN.INPUT_NAME')"
        clearable
      />
      <!-- <el-select
        v-model="searchForm.apiType"
        :placeholder="$t('PLUGIN.SELECT_TYPE')"
        filterable
        clearable
      >
        <el-option
          v-for="item in apiTypeList"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select> -->
      <div class="search-btn-left">
        <el-button type="primary" @click="getPluginList(1)">{{ $t('COMMON.SEARCH') }}</el-button>
        <el-button @click="resetSearch()">{{ $t('COMMON.RESET') }}</el-button>
      </div>
      <div class="search-btn-right">
        <el-button type="primary" :icon="Plus" @click="openPluginDialog()">{{ $t('COMMON.CREATE') }}</el-button>
      </div>
    </div>
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      class="table-box"
      header-cell-class-name="table-box-header"
    >
      <el-table-column prop="apiName" :label="$t('PLUGIN.PLUGIN_NAME')" min-width="130" />
      <!-- <el-table-column prop="apiType" :label="$t('PLUGIN.PLUGIN_TYPE')" width="120" /> -->
      <el-table-column prop="apiComment" :label="$t('PLUGIN.PLUGIN_DESC')" min-width="180" />
      <el-table-column prop="createdBy" :label="$t('COMMON.CREATOR')" width="100" />
      <el-table-column prop="updatedTime" :label="$t('COMMON.LAST_UPDATE')" width="165" />
      <el-table-column :label="$t('COMMON.OPERATIONS')" width="241">
        <template #default="scope">
          <div class="table-box-btn">
            <el-button
              v-if="scope.row.editAuth"
              link
              @click="openPluginDialog(scope.row.id)"
            >
              <el-tooltip :content="$t('COMMON.EDIT')" placement="top">
                <i class="iconfont icon-edit"></i>
              </el-tooltip>
            </el-button>
            <el-button
              v-if="scope.row.verifyAuth"
              link
              @click="openVerifyDialog({ id: scope.row.id })"
            >
              <el-tooltip :content="$t('PLUGIN.VERIFY')" placement="top">
                <i class="iconfont icon-yanzheng"></i>
              </el-tooltip>
            </el-button>
            <el-button
              v-if="scope.row.publishAuth && scope.row.isPublish === '0'"
              link
              @click="publishPlugin(scope.row.id, '1')"
            >
              <el-tooltip :content="$t('PLUGIN.PUBLISH')" placement="top">
                <i class="iconfont icon-fabu"></i>
              </el-tooltip>
          </el-button>
            <el-button
              v-if="scope.row.downlineAuth && scope.row.isPublish === '1'"
              link
              @click="publishPlugin(scope.row.id, '0')"
            >
              <el-tooltip :content="$t('PLUGIN.OFFLINE')" placement="top">
                <i class="iconfont icon-quxiaofabu"></i>
              </el-tooltip>
            </el-button>
            <el-button
              v-if="scope.row.delAuth"
              link
              @click="deletePlugin(scope.row.id)"
            >
              <el-tooltip :content="$t('COMMON.DELETE')" placement="top">
                <i class="iconfont icon-delete"></i>
              </el-tooltip>
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="pagination-box"
      v-model:current-page="pageNum"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="pageTotal"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="getPluginList(1)"
      @current-change="getPluginList()"
    />
    <plugin-dialog
      ref="pluginDialogRef"
      :apiTypeList="apiTypeList"
      :pluginId="pluginId"
      @openVerifyDialog="openVerifyDialog"
      @getPluginList="getPluginList"
    />
    <verify-dialog
      ref="verifyDialogRef"
      :verifyData="verifyData"
      @changeVerified="changeVerified"
      @getPluginList="getPluginList"
    />
  </div>
</template>

<style lang="less" scoped>
.plugin-view {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  padding: 0 24px;
  background: #fff;
}
.table-box-btn i {
  color: #568CF4;
}
</style>
