import { ElMessage } from 'element-plus'
import i18n from "@/lang/index.js";
const { t } = i18n.global
const getDataZoom = (data) => {
  let dataZoom = []
  // 数据量超过60 使用datazoom
  if (data.source.length > 60) {
    dataZoom = [{
      type: 'slider',
      start: 70,
      end: 100
    }]
    if (data.charType === 'BAR_CHART') {
      dataZoom = [{
        type: 'slider',
        start: 90,
        end: 100,
        orient: 'vertical'
      }]
    }
  }
  return dataZoom
}
// 轴配置信息
const getAxisData = (data) => {
  const allAxis = data.source.map(item => item[data.dimension[0]])
  const axisData = Array.from(new Set(allAxis))
  let axis = {
    type: 'category',
    name: data.dimension[0] ? data.dimension[0].replace(/(.{4})/g, '$1\n') : '',
    nameTextStyle: {
      color: '#555',
      // lineHeight: 0,
      // verticalAlign: 'bottom',
      padding: [0, 0, 0, 1],
      fontWeight: 'bold'
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      color: '#333'
    },
    axisLine: {
      lineStyle: {
        color: '#e2e8f0'
      }
    },
    // nameRotate: 10
    // data: axisData
  }
  if(data.charType === 'BAR_CHART') {
    axis.nameTextStyle.lineHeight = 30
    axis.axisLabel.width = 80
    axis.axisLabel.overflow = 'truncate'
  }
  if(data.legendFilterList.length &&  data.legendType && data.dimension.length < 3) {
    axis.data = axisData
  }
  // if (data.axisData && data.axisData.length > 0) {
  //   let axisData = []
  //   data.axisData.forEach(item => {
  //     if(item.name === data.dimension[0]) {
  //       axisData = item.data
  //     }
  //   })
  //   axis.data = axisData.map(item => item.value)
  // }
  return axis
}
// 空数据提示
const getTipTxt = (data) => {
  if (data.source.length === 0) {
    return 'no data'
  }
}
// 求平均值
function getAverage(arr) {
  if (arr.length === 0) return 0
  const sum = arr.reduce((acc, current) => acc + current, 0)
  const average = sum / arr.length
  return average
}
// 多度量 双轴 小度量使用右y轴
// 同时满足以下3条件：a.度量总数>=2、b.存在小度量（最大值小于5）、c.小度量与大度量的均值差异大于20倍
const dealChartData = (data) => {
  let yAxis = [{
    alignTicks: true,
    nameTextStyle: {
      color: '#555',
      fontWeight: 'bold'
    }
  }]

  // 单度量添加轴name
  if(data.dimension.length === 2) {
    yAxis[0].name = data.dimension[1]
  }
  
  // 平均值差异过大
  let smallEnd = []
  // < 5
  let smallValue = []
  // < 1
  let ltOne = []
  // 至少两个度量值才判断是否使用双轴
  if(data.dimension.length && data.dimension.length > 2) {
    let averageList = []
    // 去掉第一个用作x轴的
    data.dimension.slice(1).forEach(key => {
      const dimensionDataList = data.source.map(item => item[key])
      averageList.push({
        name: key,
        average: getAverage(dimensionDataList)
      })
      const maxValue = Math.max(...dimensionDataList)
      if(maxValue <= 5) {
        smallValue.push(key)
      }
      if(maxValue <= 1) {
        ltOne.push(key)
      }
    })
    let average = averageList.map(item => item.average)
    // 小值度量与大值度量的均值差异大于20倍
    let maxAverage = Math.max(...average)
    let smallAverage = []
    averageList.forEach(item => {
     try {
      if(maxAverage / item.average > 20) {
        smallAverage.push(item.name)
      }
     } catch (error) {
      console.log('度量值均值比较报错')
     }
    })

    // smallEnd = Array.from(new Set(smallValue.concat(smallAverage)))
    smallEnd = smallAverage

    if(smallEnd.length) {
      yAxis.push({
        alignTicks: true
      })
    }
  }
  return [yAxis, smallEnd, ltOne]
}
// 调色盘
const customColors = ['#2563EB','#14B8A6','#EA580C','#16A34A','#E11D48','#7C3AED','#F59E0B']
const colorsRGB = ['37,99,235','20,184,166','234,88,12','22,163,74','25,29,72','124,58,237','245,158,11']
// 柱/折图
const createBarAndLineOption = (data) => {
  const [yAxis, smallValue, ltOne] = dealChartData(data)
  let dataset = {}
  let seriesList = []
  let chartType = data.charType == 'COLUMN_CHART' ? 'bar' : 'line'
  if (data.source.length > 0) {
    // 有分组维度+单度量
    if(data.legendFilterList.length && data.legendType && data.dimension.length < 3) {
      let legends = data.legendFilterList.filter(item => item.name === data.legendType)[0].data
      seriesList = legends.map(lgd => {
        const seriesDataTemp = data.source.filter(item => item[data.legendType] === lgd.value)

        const axisDataList = Array.from(new Set(data.source.map(item => item[data.dimension[0]])))
        seriesDataTemp.sort((a, b) => {
          const indexA = axisDataList.indexOf(a[data.dimension[0]])
          const indexB = axisDataList.indexOf(b[data.dimension[0]])
          return indexA - indexB
        })

        const seriesData = seriesDataTemp.map(item => ([String(item[data.dimension[0]]), item[data.dimension[1]]]))

        if(!seriesData.length) return

        return {
          type: chartType,
          name: lgd.value,
          data: seriesData,
          barMaxWidth: 30,
          barMinWidth: 10,
          itemStyle: {}
        }
      })
    }else {
      // 无分组维度 只有x轴/y轴 单度量+多度量
      dataset = {
        dimensions: [...data.dimension],
        source: [...data.source]
      }

      seriesList = data.dimension.slice(1).map(measure => {
        const seriesItem = {
          type: chartType,
          name: measure,
          barMaxWidth: 30,
          barMinWidth: 10,
          itemStyle: {}
        }

        if(yAxis.length > 1 && smallValue.length && smallValue.includes(measure) && data.dimension.length > 2) {
          seriesItem.yAxisIndex = 1
        }

        return seriesItem
      })
    }  
  }
  return {
    title: {
      left: 'center',
      padding: 10,
      text: getTipTxt(data),
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    color: customColors,
    legend: data.charType == 'COLUMN_CHART' ? {
      type: 'scroll',
      left: 'center',
      top: 5,
      itemWidth: 10,
      itemHeight: 10,
      icon: 'rect',
      textStyle: {
        height: 10,
        rich: {
          a: {
            verticalAlign: 'middle',
          },
        }
      }
    } : {
      type: 'scroll',
      left: 'center',
      top: 5,
      textStyle: {
        height: 10,
        rich: {
          a: {
            verticalAlign: 'middle',
          },
        }
      }
    },
    dataset: dataset,
    xAxis: getAxisData(data),
    yAxis: yAxis,
    dataZoom: getDataZoom(data),
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    series: seriesList
  }
};

// 条形图
const createColumnOption = (data) => {
  const [yAxis, smallValue, ltOne] = dealChartData(data)
  let dataset = {}
  let seriesList = []
  if (data.source.length > 0) {
    if(data.legendFilterList.length && data.legendType && data.dimension.length < 3) {
      let legends = data.legendFilterList.filter(item => item.name === data.legendType)[0].data
      seriesList = legends.map(lgd => {
        const seriesDataTemp = data.source.filter(item => item[data.legendType] === lgd.value)

        const seriesData = seriesDataTemp.map(item => ([item[data.dimension[1]], String(item[data.dimension[0]])]))

        if(!seriesData.length) return

        return {
          type: 'bar',
          name: lgd.value,
          data: seriesData,
          barMaxWidth: 30,
          barMinWidth: 10,
          itemStyle: {}
        }
      })
    }else {
      dataset = {
        dimensions: [...data.dimension],
        source: [...data.source]
      }

      seriesList = data.dimension.slice(1).map(item => {
        const seriesItem = {
          type: 'bar',
          name: item,
          barMaxWidth: 30,
          barMinWidth: 10,
          itemStyle: {}
        }

        if(yAxis.length > 1 && smallValue.length && smallValue.includes(item) && data.dimension.length > 2) {
          seriesItem. xAxisIndex = 1
        }

        return seriesItem
      })
    }
  }
  return {
    title: {
      text: getTipTxt(data),
      left: 'center',
      padding: 10,
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    color: customColors,
    legend: {
      type: 'scroll',
      left: 'center',
      top: 5,
      itemWidth: 10,
      itemHeight: 10,
      icon: 'rect',
      textStyle: {
        height: 10,
        rich: {
          a: {
            verticalAlign: 'middle',
          },
        }
      }
    },
    dataset: dataset,
    xAxis: yAxis,
    yAxis: getAxisData(data),
    dataZoom: getDataZoom(data),
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    series: seriesList
  }
};
// 面积图
const createAreaOption = (data) => {
  const [yAxis, smallValue, ltOne] = dealChartData(data)
  let dataset = {}
  let seriesList = []
  if (data.source.length > 0) {
    if(data.legendFilterList.length && data.legendType && data.dimension.length < 3) {
      let legends = data.legendFilterList.filter(item => item.name === data.legendType)[0].data
      seriesList = legends.map((lgd, index) => {
        
        const seriesDataTemp = data.source.filter(item => item[data.legendType] === lgd.value)
        // 数据顺序和x轴不对应
        const axisDataList = Array.from(new Set(data.source.map(item => item[data.dimension[0]])))
        seriesDataTemp.sort((a, b) => {
          const indexA = axisDataList.indexOf(a[data.dimension[0]])
          const indexB = axisDataList.indexOf(b[data.dimension[0]])
          return indexA - indexB
        })

        const seriesData = seriesDataTemp.map(item => ([String(item[data.dimension[0]]), item[data.dimension[1]]]))

        if(!seriesData.length) return

        const colorIndex = index % customColors.length
        return {
          type: 'line',
          name: lgd.value,
          data: seriesData,
          smooth: true,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: `rgba(${colorsRGB[`${colorIndex}`]},0.6)`
              }, {
                offset: 1, color: `rgba(${colorsRGB[`${colorIndex}`]},0)`
              }]
            }
          }
        }
      })
    }else {
      dataset = {
        dimensions: [...data.dimension],
        source: [...data.source]
      }
      seriesList = data.dimension.slice(1).map((item, index) => {
        const colorIndex = index % customColors.length
        const seriesItem = {
          type: 'line',
          name: item,
          smooth: true,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: `rgba(${colorsRGB[`${colorIndex}`]},0.6)`
              }, {
                offset: 1, color: `rgba(${colorsRGB[`${colorIndex}`]},0)`
              }]
            }
          }
        }

        if(yAxis.length > 1 && smallValue.length && smallValue.includes(item) && data.dimension.length > 2) {
          seriesItem.yAxisIndex = 1
        }

        return seriesItem
      })
    } 
  }
  return {
    title: {
      text: getTipTxt(data),
      left: 'center',
      padding: 10,
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    color: customColors,
    legend: {
      type: 'scroll',
      left: 'center',
      top: 5,
      textStyle: {
        height: 10,
        rich: {
          a: {
            verticalAlign: 'middle',
          },
        }
      }
    },
    dataset: dataset,
    xAxis: getAxisData(data),
    yAxis: yAxis,
    dataZoom: getDataZoom(data),
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    series: seriesList
  }
}
// 饼图
const createPieOption = (data) => {
  if (data.timeAxis) {
    ElMessage({
      message: t('CHAT.NO_SUPPORT_PIE'),
      type: 'warning'
    })
    return {}
  }
  let seriesList = []
  let dataset = {}
  if (data.source.length > 0) {

    // 有分组维度+单度量
    if(data.legendFilterList.length && data.legendType && data.dimension.length < 3) {
      let legends = data.legendFilterList.filter(item => item.name === data.legendType)[0].data
      seriesList = legends.map((lgd, index) => {
        ++index
        const seriesDataTemp = data.source.filter(item => item[data.legendType] === lgd.value)

        const seriesData = seriesDataTemp.map(item => ([String(item[data.dimension[0]]), item[data.dimension[1]]]))

        if(!seriesData.length) return

        return {
          type: 'pie',
          name: lgd.value,
          radius: [50, 100],
          center: [`${index * 25}%`, '50%'],
          // encode: {
          //   "itemName": 0,
          //   "value": index
          // },
          data: seriesData.map(item => ({
            name: item[0],
            value: item[1]
          })),
          label: {
            show: true,
            formatter: '{b}: {d}%'
          }
        }
      })
    }else {
      // 无分组维度 单度量+多度量
      dataset = {
        dimensions: [...data.dimension],
        source: [...data.source]
      }

      data.dimension.forEach((item, index) => {
        if (index === 0) return false
        seriesList.push({
          type: 'pie',
          name: item,
          radius: [50, 100],
          center: [`${index * 25}%`, '50%'],
          encode: {
            "itemName": 0,
            "value": index
          },
          label: {
            show: true,
            formatter: '{b}: {d}%'
          }
        })
      })
    }
  }
  return {
    title: {
      text: getTipTxt(data),
      left: 'center',
      padding: 10,
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    grid: {
      top: 50
    },
    color: customColors,
    tooltip: {
      trigger: 'item'
    },
    legend: {
      type: 'scroll',
      left: 0,
      bottom: 'center',
      itemWidth: 10,
      itemHeight: 10,
      icon: 'rect',
      orient: 'vertical',
      textStyle: {
        height: 10,
        rich: {
          a: {
            verticalAlign: 'middle',
          },
        }
      }
    },
    dataset: dataset,
    series: seriesList
  }
};
export default function createChartOption(data) {
  switch (data.charType) {
    case 'COLUMN_CHART': return createBarAndLineOption(data)
    case 'LINE_CHART': return createBarAndLineOption(data)
    case 'BAR_CHART': return createColumnOption(data)
    case 'AREA_CHART': return createAreaOption(data)
    case 'PIE_CHART': return createPieOption(data)
    default: return {}
  }
};
