<script setup>
import { Histogram, Collection, Box, Opportunity } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useI18n } from 'vue-i18n'
import { computed } from 'vue'

const router = useRouter()
const user = useUserStore()
const { t } = useI18n()

// 目前支持的chat场景
const scenes = computed(() => [
  {
    type: 'chat_with_data',
    title: 'Chat Data',
    desc: t('HOME.CHAT_DATA_DESC'),
    icon: Histogram
  },
  {
    type: 'chat_knowledge',
    title: 'Chat Knowledge',
    desc: t('HOME.CHAT_KNOW_DESC'),
    icon: Collection
  },
  {
    type: 'chat_with_scene',
    title: 'Agent Chat',
    desc: t('HOME.CHAT_DATA_APP'),
    icon: Box
  },
  {
    type: 'chat_prc_demo',
    title: 'PRC Demo',
    desc: 'Multi-agent demo on chat knowledge and chat data.',
    icon: Opportunity
  },
  {
    type: 'chat_mct_demo',
    title: 'MCT Demo',
    desc: 'Multi-agent demo on chat knowledge and chat data.',
    icon: Opportunity
  }
])
const sceneList = computed(() => {
  return scenes.value.filter(item => user.scenes.includes(item.type))
})

// 创建对话
const createChat = (scene) => {
  router.push({
    path: '/chatNow',
    query: { scene }
  })
}
// 输入框文本
// const inputText = ref('')
// 输入框回车事件
// const handleInputEnter = (e) => {
//   if (!e.altKey && !e.ctrlKey && !e.metaKey && !e.shiftKey) {
//     e.returnValue = false
//     createChat()
//   }
// }
</script>

<template>
  <div class="home-view">
    <el-divider>{{ $t('HOME.QUICK_STRAT') }}</el-divider>
    <el-row :gutter="24">
      <el-col v-for="item in sceneList" :key="item.type" :span="12">
        <div class="scene-btn" @click="createChat(item.type)">
          <component :is="item.icon" class="scene-icon" />
          <div class="scene-text">
            <div class="scene-text-title">{{ item.title }}</div>
            <div class="scene-text-desc">{{ item.desc }}</div>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- <div class="model-list">
      <el-select model-value="Proxy LLM">
        <el-option
          label="Proxy LLM"
          value="Proxy LLM"
        />
      </el-select>
    </div>
    <div class="chat-input">
      <el-input
        v-model="inputText"
        type="textarea"
        :autosize="{ minRows: 1, maxRows: 4 }"
        resize="none"
        @keydown.enter="handleInputEnter"
      />
      <el-button
        class="send-button"
        size="large"
        :icon="Position"
        text
        @click="createChat()"
      />
    </div> -->
  </div>
</template>

<style lang="less" scoped>
.home-view {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  width: 768px;
  padding: 0 16px;
  margin: auto;
  .el-divider {
    margin-top: -150px;
  }
  .el-col {
    padding: 12px;
    .scene-btn {
      display: flex;
      cursor: pointer;
      border-radius: 4px;
      border: 1px solid rgba(209, 213, 219, .5);
      padding: 16px;
      background: rgba(248, 250, 252, .5);
      color: #030712;
      font-family: 'Segoe UI';
      .scene-icon {
        width: 40px;
        height: 40px;
        color: #409eff;
        margin-right: 16px;
      }
      .scene-text {
        flex: 1;
        .scene-text-title {
          font-size: 18px;
          line-height: 28px;
          font-weight: 600;
        }
        .scene-text-desc {
          font-size: 14px;
          line-height: 20px;
          height: 40px;
          overflow: hidden;
          opacity: .8;
        }
      }
    }
  }
  // .model-list {
  //   margin-top: 32px;
  //   margin-bottom: 8px;
  // }
  // .chat-input {
  //   display: flex;
  //   :deep(.el-textarea__inner) {
  //     font-size: 16px;
  //     padding: 8px 11px;
  //   }
  //   .send-button {
  //     font-size: 18px;
  //     margin-left: 8px;
  //     padding: 12px;
  //   }
  // }
}
</style>
