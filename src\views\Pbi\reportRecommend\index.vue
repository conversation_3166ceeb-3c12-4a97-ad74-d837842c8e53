<script setup>
import { ref, nextTick, onMounted } from 'vue'
import {useRoute} from 'vue-router'
// import * as pbi from 'powerbi-client'
// import 'powerbi-report-authoring'
// import * as api from './api'
import markdownRender from '@/components/MarkdownRender/markdownRender.vue'
import { useI18n } from 'vue-i18n'

import { getToken, getUsername } from '@/utils/auth'
import { fetchEventSource } from '@microsoft/fetch-event-source'

import * as api from './api/index.js'
import defaultImage from './images/default.png';

// 知识库召回 报表卡片信息
// 思考过程
// 缩略图+权限
// 展示
// 无缩略图 -> 默认图片  |  无权限 -> 展示缩略图+蒙版

const { t } = useI18n()

const route = useRoute()

// let props = defineProps({
//   chartData: Object
// })

const showLoading = ref(true)
const showLoginTip = ref(false)
const showThinking = ref(true)
const showReportList = ref(false)

let knowledgeKey = ref('')
// 全量信息
let reprotInfo = []
let reportList = ref([])

const getReportInfo = async() => {
  // 合作伙伴相关的报表推荐 官网相关的报表有哪些
  const res = await api.getReportInfo({
    knowledgeName: route.query?.knowledgeName || '销售智能体',
    userInput: route.query?.userInput || '模拟大总cps报表推荐'
  })
  
  knowledgeKey.value = res.knowledgeKey
  res.knowledgeData.map(item => {
    try {
      // .replaceAll('meta:', '')
      item.text = JSON.parse(item.text.replaceAll('\n', ''))
      item.reportId = item.text.reportID
      item.datasetId = item.text.datasetID
      item.metadata = JSON.parse(item.metadata)
      return item
    } catch (error) {
      console.log('metadata数据解析出错')
    }
  })
  // console.log(res.knowledgeData)

  reprotInfo = res
  // reportList.value = [...res.knowledgeData]

  // if(res.knowledgeData?.length) {
  //   showReportList.value = true
  // }
}

const filterReportList = (hasRightReport, list) => {
  try {
    hasRightReport =  JSON.parse(hasRightReport.replace(/'/g, '"'));
  } catch (error) {
    console.error("模型数据转数组")
  }
  const filteredData = list.filter(item => {
    return hasRightReport.includes(item.text.reportName)
  });
  return filteredData
}

// 思考过程
let realContent = ref('')
let tempStr = ''
// let hasRightReport = null
let thinkEnd = ref(false)
let noGet = ref(true)
const getThinkContent = async() => {
  // const res = await api.getThinkContent({
  //   knowledgeKey: "0c196a96-d4cd-47c6-9056-cab505a5b0ed",
  //   userInput: '纵队相关的报表有哪些',
  //   modelName: 'DeepSeek-V3-OperationAge',
  //   appPromptId: 217
  // })

  let ctrl = new AbortController() // 标记是否结束
  try {
    await fetchEventSource(`${import.meta.env.VITE_APP_BASE_API_URL}/china/report/reportSummary`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': getToken() // 这里不需要token，是个开放的api对外
      },
      body: JSON.stringify({
        appPromptName: route.query?.appPromptName,
        convId: route.query?.convId || '',
        username: route.query?.username || getUsername(),
        knowledgeName: route.query?.knowledgeName || '营销智能体',
        knowledgeKey: knowledgeKey.value || "e66e736f-86ee-4427-a127-ab0b4de53a22",
        userInput: route.query?.userInput || '官网所属报表',
        modelName: route.query?.modelName || 'DeepSeek-V3-OperationAgent',
        // appPromptId: 217
      }),
      signal: ctrl.signal,
      openWhenHidden: true,
      onmessage(event) {
        if(showLoading.value) {
          showLoading.value = false
        }
        try {
          let data = JSON.parse(event.data)

          if(data.isEnd) return

          tempStr += data.data
          // console.log(tempStr)
          const splitstr = tempStr.split('@@@@@@')
          if(noGet.value && tempStr.includes("知识库中提供的内容不足以回答此问题") && splitstr.length === 1 ) {
            realContent.value = tempStr
          }
          if(noGet.value && splitstr.length > 1) {
            reprotInfo.knowledgeData = filterReportList(splitstr[0].replaceAll('\n', ''), reprotInfo.knowledgeData)
            // console.log(reprotInfo)
            if(reprotInfo.knowledgeData.length) {
              getReportsAccessSort()
            }
            noGet.value = false
          }
          if(!noGet.value && splitstr.length > 1){
            // realContent.value = tempStr
            realContent.value = splitstr[1]
          }
          
        } catch (error) {
          console.log('思考过程数据解析出错')
        }
        // try {
        //   let data = JSON.parse(event.data)
        //   if(data.isEnd) return
        //   realContent.value += data.data
        // } catch (error) {
        //   thinkEnd.value = true
        //   console.log('思考过程数据解析出错')
        // }
      },
      onclose() {
        thinkEnd.value = true
        showLoading.value = false
        ctrl.abort()
      },
      onerror(error) {
        thinkEnd.value = true
        showLoading.value = false
        ctrl.abort()
      }
    })
  } catch (error) {
    ctrl.abort()
    console.log('思考过程', error)
  }
}

let reportSort = []
const getReportsAccessSort = async() => {
  const info = reprotInfo.knowledgeData.map(item => ({
    "score": item.score,
    "reportId": item.reportId,
    "datasetId": item.datasetId
  }))
  // "appKey": 
  const params = {
    "appKey": import.meta.env.VITE_APP_COLLECT_APPKEY,
    "source_system": "opsAgent",
    "username": route.query?.username || getUsername() || 'yangxu27',
    "reports": info || []
  }
  try {
    const res = await api.getReportsAccessSort(params)
    if(res?.status === 40003) {
      showLoginTip.value = true
      return
    }else {
      reportSort = res || []
      // console.log('权限',reportSort)
    }
  } catch (error) {
    console.log('获取报表权限信息出错')
  }

  let demo = [
    // {
    //   "score": 1,
    //   "reportId": "5c359c16-9424-4b8a-88e3-2248a4098d88",
    //   "pngBase64": ""
    // }
  ]

  const originData = [...reprotInfo.knowledgeData]

  const aMap = new Map();
  originData.forEach(item => {
    if (!aMap.has(item.reportId)) {
      aMap.set(item.reportId, []);
    }
    aMap.get(item.reportId).push(item);
  });

  const matched = reportSort.map(bItem => {
    const aItems = aMap.get(bItem.reportId) || [];
    return aItems.map(aItem => ({ ...aItem, ...bItem, hasRight: true }));
  }).flat();

  const unmatched = originData.filter(item => !reportSort.some(bItem => bItem.reportId === item.reportId));

  const result = [...matched, ...unmatched];

  // console.log('最终数据', result)
  if(result.length) {
    reportList.value = result
    showReportList.value = true
  }

  // dealData()
  function dealData() {
    // data:image/png;base64,
    reportSort = []

    // 组合数据权限排序
    // reportList.value
    const originData = [...reportList.value]
    const sortData = [...reportSort]

    // const sortedList = sortData.map(item => {
    //   return originData.find(o => o.reportId === item.reportId)
    // })

    const bMap = new Map(sortData.map(item => [item.reportId, item]));

    const mergedB = sortData.map(sItem => {
      const find = originData.find(oItem => {
        return oItem.reportId === sItem.reportId
      })
      return find ? {
        ...find,
        ...sItem,
        hasRight: true
      } : {
        ...sItem
      }
    });

    const remainingA = originData.filter(item => !bMap.has(item.reportId));
    const sortedList = [...mergedB, ...remainingA];

    if(sortedList.length) {
      reportList.value = sortedList
      showReportList.value = true
    }
    // console.log('sortedList', sortedList)
  }

}

const hasToken = async() => {
  try {
    const res = await api.hasToken({
      username: route.query?.username || getUsername() || 'yangxu27'
    })
    if(res?.status === 40003) {
      showLoginTip.value = true
    }
  } catch (error) {
    console.log('获取token出错', error)
  }
}

const noDataFlag = ref(true)
const noDataTips = ref('')
const initPageData = async() => {
  try {
    await hasToken()
    if(showLoginTip.value) {
      showLoading.value = false
      return
    }
    await getReportInfo()
    // await getReportsAccessSort()
    if(reprotInfo.knowledgeData.length) {
      noDataFlag.value = false
      
      getThinkContent()
    }else {
      showLoading.value = false
      noDataTips.value = '很抱歉，暂时未找到和问题相关的报表。'
    }
  } catch(error) {
    console.log('初始化')
  }
}
const cardListRef = ref(null)
const controlBtnRef = ref(null)
const handleMore = () => {
  // if(cardListRef.value.style.height === 'auto') {
  //   cardListRef.value.style.height = '290px'
  //   return
  // }
  cardListRef.value.style.height = 'auto'
  controlBtnRef.value.style.display = 'none'
}

const toggleShowThink = () => {
  showThinking.value = !showThinking.value
}

const handleViewReport = async(data) => {
  const collectParams = {
    operateTime: new Date().getTime(),
    deviceModel: navigator.userAgent,
    appKey: import.meta.env.VITE_APP_COLLECT_APPKEY,
    ssoSource: 'https://opsagent-tst.lenovo.com/',
    source_system: 'opsAgent',
    options: {
      page: [data.text.reportID],
      params: {
        reportLink: data.text.reportLink
      },
      username: route.query?.username || ''
    }
  }
  window.open(data.text.reportLink, "_blank", "noopener,noreferrer")
  try{
    await api.saveClickReportLog(collectParams)
  }catch(error) {
    console.log('保存报表点击日志出错')
  }
}

// azure token
const azureToken = () => {
  const itcode = route.query?.username || getUsername()
  // const popup = window.open(`https://intellidata-tst.lenovo.com/api/aad/redirect?itcode=yangxu27&type=pbi&redirectUrl=https://intellidata-tst.lenovo.com/loggedTips`, "_blank",  "noopener,noreferrer")
  // const popup = window.open(`${window.self.location.origin}/api/aad/redirect?itcode=${itcode}&type=pbi&redirectUrl=${window.self.location.origin}/loggedTips`, "_blank",  "noopener,noreferrer")
  // if (!popup || popup.closed) {
  //   alert('跳转Azure登陆弹窗被拦截，请允许弹窗或手动打开链接。')
  // }
  // window.parent.location.href = `${window.self.location.origin}/api/aad/redirect?itcode=${itcode}&type=pbi&redirectUrl=${window.top.location.href}`
}

const observerDom = ref(null)
let oldHeight = 0
// 获取父页面的 window 对象
const parentWindow = window.parent

const observer = new ResizeObserver(entries => {
  for (let entry of entries) {
    // console.log('当前高度:', entry.contentRect.height);
    const newHeight = entry.contentRect.height
    try {
      const postData = {
        height: Math.ceil(newHeight + 20),
        // requestId: pageData.requestId
        requestId: route.query?.requestId
      }
      oldHeight = newHeight
      parentWindow.postMessage(JSON.stringify(postData), '*')
    }catch (error) {
      console.log('计算iframe高度出错')
    }
  }
})

onMounted(() => {
  // realContent.value = streamContent
  observer.observe(observerDom.value)
})

initPageData()

// 获取ciop/ops对应环境域名 进行azure token获取
const getCiopDomain = (url) => {
  if(url && (url.includes("tst") || url.includes('localhost'))) {
    return {
      ciop: "https://ciop-tst.lenovo.com",
      ops: "https://opsagent-tst.lenovo.com"
    }
  }else if(url && url.includes("uat")) {
    return {
      ciop: "https://ciop-uat.lenovo.com",
      ops: "https://opsagent-uat.lenovo.com"
    }
  }else {
    return {
      ciop: "https://ciop.lenovo.com",
      ops: "https://opsagent.lenovo.com"
    }
  }
}

const {ciop, ops} = getCiopDomain(window.self.location.origin)
const loginUrl = `${ciop}/api/powerbi/aad/redirect?itcode=${route.query?.username || getUsername()}&redirectUrl=${ops}/toTokenTip`

</script>

<template>
  <div class="report-recommend" ref="observerDom" v-loading="showLoading">
    <template v-if="showLoginTip">后台未匹配到您的数据、报表的权限信息，请您点击<a :href="loginUrl" target="_blank" style="color: blue;cursor:pointer;text-decoration: underline;">此处</a>进行初始登陆认证！</template>
    <template v-if="!showLoginTip && !noDataFlag">
      <div class="thinking-content">
        <div class="title">
          <div class="left">
            <i class="iconfont icon-ChatGPT-logo"></i>
            思考过程
          </div>
          <div class="right" :class="{showThink: showThinking}">
            <el-icon @click="toggleShowThink"><ArrowUpBold /></el-icon>
          </div>
        </div>
        <div v-show="showThinking">
          <markdown-render :context="realContent" />
        </div>
      </div>
      <div class="report-list" ref="cardListRef" v-if="showReportList && reportList?.length && thinkEnd">
        <div class="card-item" v-for="item in reportList" :key="item.score">
          <img class="thumbnail" :src="item.pngBase64 ? `data:image/png;base64,${item.pngBase64}` : defaultImage" alt="" @click="handleViewReport(item)">
          <div class="info-block">
            <div class="title">
              <div class="left">
                <el-tooltip
                  :content="item.text.reportName"
                  effect="dark"
                  placement="top"
                >
                  <el-text line-clamp="1">
                    {{ item.text.reportName }}
                  </el-text>
                </el-tooltip>
              </div>
              <div class="right">
                <el-icon><User /></el-icon>
                <span class="name">{{ item.text.reportOwner }}</span>
              </div>
            </div>
            <!-- :content="item.text.report_Desc" -->
            <div class="desc">
              <el-tooltip
                :content="item.text.report_Desc"
                effect="dark"
                placement="top"
              >
                <el-text line-clamp="2">
                  {{ item.text.report_Desc }}
                </el-text>
              </el-tooltip>
            </div>
          </div>
          <div class="blur-mask" v-if="!item.hasRight">
            <div class="lock"></div>
            <div class="tips-text">
              <p class="line">您当前尚未获得该报表的权限</p>
              <p>请发送邮件至 <span class="email">{{item.text.reportOwner}}@lenovo.com</span> 启动申请！</p>
            </div>
          </div>
        </div>
      </div>
      <div class="control-btn" v-if="thinkEnd && reportList?.length > 3" ref="controlBtnRef" @click="handleMore">
        点击展开报表
      </div>
    </template>
    <template v-if="!showLoginTip && noDataFlag">
      {{ noDataTips }}
    </template>
  </div>
</template>

<style scoped lang="less">
@import url("//at.alicdn.com/t/c/font_4728306_rxazw3hrbqr.css");
.report-recommend {
  min-height: 30px;
  // padding: 20px;
  // min-width: 800px;
  :deep(.el-loading-spinner) {
    top: 0;
    margin-top: 0;
    .circular {
      width: 20px !important;
      height: 20px !important;
    }
  }
}
.thinking-content {
  margin: 10px 0;
  .title {
    font-weight: bold;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .right {
    cursor: pointer;
    margin-right: 16px;
    &.showThink {
      transform: rotate(-180deg);
    }
  }
  .icon-ChatGPT-logo {
    color: #3f90e1;
  }
}
.control-btn {
  height: 20px;
  // margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  cursor: pointer;
  color: #2480EB;
}
.report-list {
  display: flex;
  flex-wrap: wrap;
  position: relative;
  height: 290px;
  overflow: hidden;
  transition: height .3s ease-out;
  .card-item {
    position: relative;
    width: calc(100% / 3 - 10px);
    height: 280px;
    padding: 10px;
    background-color: #f5f5f5;
    margin-right: 10px;
    margin-bottom: 10px;
    border-radius: 8px;
    &::nth-child(3n) {
      margin-right: 0;
    }
    &::last-child {
      margin-right: 0;
    }
    .thumbnail {
      width: 100%;
      height: calc(100% - 90px);
      border-radius: 8px;
      object-fit: fill;
      cursor: pointer;
    }
    .blur-mask {
      position: absolute;
      top: 10px;
      left: 10px;
      right: 10px;
      bottom: 90px;
      // transform: translate(-50%, -50%);
      // width: 80%;
      // height: 50%;
      background: rgba(255, 255, 255, 0.2);
      -webkit-backdrop-filter: blur(5px);
      backdrop-filter: blur(10px);
      border-radius: 8px;
      // border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #000;
      font-size: 1.2em;
      opacity: 1;
      // transition: all 0.3s ease;
      cursor: not-allowed;
      .lock {
        width: 30px;
        height: 42px;
        background: url(./images/lock.png) no-repeat center center;
        background-size: cover;
        margin-bottom: 15px;
      }
      .tips-text {
        text-align: center;
        font-size: 14px;
        padding: 0 10px;
        .line {
          margin-bottom: 5px;
        }
      }
      .email {
        color: #2480EB;
      }
    }
  }
}

@media screen and (max-width: 600px) {
  .report-list {
    height: auto;
    .card-item {
      width: calc(100% / 2 - 10px);
    }
  }
  .control-btn {
    display: none;
  }
}

@media screen and (max-width: 400px) {
  .report-list {
    height: auto;
    .card-item {
      width: 100%;
    }
  }
  .control-btn {
    display: none;
  }
}

.info-block {
  height: 90px;
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  .title {
    display: flex;
    justify-content: space-between;
    padding-top: 10px;
    height: 30px;
    .left {
      flex: 1;
      font-size: 16px;
      font-weight: bold;
      overflow: hidden;
      // text-overflow: ellipsis;
      // white-space: nowrap;
      cursor: default;
    }
    .right {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60px;
      height: 20px;
      padding: 0 5px;
      margin-left: 10px;
      border-radius: 4px;
      // background: linear-gradient(to right, rgb(36, 128, 235),rgb(48, 207, 121));
      // color: #fff;
      background-color: #fff;
      color: #999;
      .el-icon {
        font-weight: bold;
      }
      .name {
        width: 60px;
        padding-left: 5px;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .desc {
    height: 40px;
    line-height: 20px;
    padding-top: 10px;
    cursor: default;
    .el-text {
      font-size: 14px;
    }
  }
}
</style>