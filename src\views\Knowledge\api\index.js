import request from '@/utils/axios'

const url = '/'

function getKnowledgeList(data) {
  return request({
    url: url + 'knowledge/space/detailList',
    method: 'post',
    data
  })
}

function addKnowledge(data) {
  return request({
    url: url + 'knowledge/space/add',
    method: 'post',
    data
  })
}

function deleteKnowledge(data) {
  return request({
    url: url + 'knowledge/space/delete',
    method: 'post',
    data
  })
}

function getDataSourceList(id, data) {
  return request({
    url: url + `knowledge/${id}/document/list`,
    method: 'post',
    data
  })
}

function addDataSource(id, data) {
  return request({
    url: url + `knowledge/${id}/document/add`,
    method: 'post',
    data
  })
}

function uploadDocument(id, data) {
  return request({
    url: url + `knowledge/${id}/document/upload`,
    method: 'post',
    data
  })
}

function syncDataSource(id, data) {
  return request({
    url: url + `knowledge/${id}/document/sync`,
    method: 'post',
    data
  })
}

function deleteDataSource(id, data) {
  return request({
    url: url + `knowledge/${id}/document/delete`,
    method: 'post',
    data
  })
}

function getChunkList(id, data) {
  return request({
    url: url + `knowledge/${id}/chunk/list`,
    method: 'post',
    data
  })
}

function getArguments(id, data) {
  return request({
    url: url + `knowledge/${id}/arguments`,
    method: 'post',
    data
  })
}

function saveArguments(id, data) {
  return request({
    url: url + `knowledge/${id}/arguments/save`,
    method: 'post',
    data
  })
}

export {
  getKnowledgeList,
  addKnowledge,
  deleteKnowledge,
  getDataSourceList,
  addDataSource,
  uploadDocument,
  syncDataSource,
  deleteDataSource,
  getChunkList,
  getArguments,
  saveArguments
}
