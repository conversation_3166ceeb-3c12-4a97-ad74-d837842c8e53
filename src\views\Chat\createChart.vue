<template>
  <!-- 图表 -->
  <template v-if="renderChartDom">
    <span class="type-label">{{ $t('CHAT.CHART_TYPE') }}:</span>
    <el-select v-model="chartType" placeholder="Please Select...">
      <el-option v-for="item in chartTypeList" :key="item.value" :label="$t(`CHART.${item.value}`)" :value="item.value" />
    </el-select>
    <!-- 维度筛选器 -->
    <template v-if="chartFilterList.data.length > 0">
      <span class="type-label">{{ $t('CHAT.DIMENSION_FILTER') }}:</span>
      <template v-for="item in chartFilterList.data" :key="item.name">
        <span class="dimension-label">{{ item.name }}</span>
        <el-select v-model="filterValue[item.name]" @change="filterChange" placeholder="Please Select...">
          <el-option v-for="itemIn in item.data" :key="itemIn.value" :label="itemIn.label" :value="itemIn.value" />
        </el-select>
      </template>
    </template>
    <v-chart ref="vChartsDom" :option="chartOption" autoresize style="height: 400px;" />
  </template>
  <!-- 卡片 -->
  <template v-if="cardDataList.length > 0">
    <div class="card-wrap">
      <el-card v-for="item in cardDataList" :key="item._name">
        <el-statistic :title="item._name" :value="item[item._name]" :formatter="(val) => val.toLocaleString()" />
        {{ item.value }}
      </el-card>
    </div>
  </template>
</template>

<script setup>

import VChart from 'vue-echarts';
import { ref, watch, nextTick, reactive } from 'vue';
import * as echarts from 'echarts';
import createChartOption from './createChartOption.js';
// 声明props
const props = defineProps({
  renderData: Object
})
let origi = props.renderData
const endData = {
  tableData: origi.data,
  source: {},
  filterDimensionList: [],
  // ...origi.semanticMap,
  measure: origi.semanticMap.measure,
  dimension: origi.semanticMap.dimension,
  time: origi.semanticMap.time,
  other: origi.semanticMap.other
}
// 维度筛选器列表数据组装
const getDimensionListData = (tableData, dimension) => {
  let dimensionListData = []
  dimension.forEach(x => {
    const currentDimensionInfo = { name: x }
    let currentDimension = new Set()
    tableData.forEach(y => {
      if (y[x]) currentDimension.add(y[x])
    })
    currentDimensionInfo.data = Array.from(currentDimension).map(item => ({
      value: item,
      label: item
    }))
    dimensionListData.push(currentDimensionInfo)
  })
  return dimensionListData
}
// 获取默认维度值
const getDefaultDimensionValue = (dimensionList) => {
  const defaulDimensionListValue = []
  dimensionList.forEach(item => {
    defaulDimensionListValue.push({
      name: item.name,
      value: item.data.length > 0 ? item.data[0].value : ''
    })
  })
  return defaulDimensionListValue
}
// 根据维度筛选数据
const getChartDataFromDimension = (tableData, selectedDimensionValue) => {
  let copyData = Array.from(tableData)
  selectedDimensionValue.forEach(x => {
    let temp = []
    copyData.forEach(item => {
      if (item[x.name] == x.value) temp.push(item)
    })
    copyData = temp
  })
  return copyData
}

const vChartsDom = ref(null)
// 卡片数据
const cardDataList = ref([])
const renderChartDom = ref(false)
// 筛选器列表
const chartFilterList = reactive({
  data: []
})
// 图表类型列表
const chartTypeList = [{
  label: '柱状图',
  value: 'COLUMN_CHART'
}, {
  label: '折线图',
  value: 'LINE_CHART'
}, {
  label: '条形图',
  value: 'BAR_CHART'
}, {
  label: '面积图',
  value: 'AREA_CHART'
}, {
  label: '饼图',
  value: 'PIE_CHART'
}]
// 筛选器值
const filterValue = reactive({})
// 图表类型
const chartType = ref('COLUMN_CHART')
// 生成chartOption的入参
const chartInputData = reactive({
  charType: 'COLUMN_CHART',
  dimension: [],
  source: [],
  timeAxis: false
})

const updateChartData = () => {
  const newChartOption = createChartOption(chartInputData)
  nextTick(() => {
    vChartsDom.value.setOption(newChartOption, {
      notMerge: true
    })
  })
}
const getCurrentFilterValue = (list) => {
  let formaterData = []
  for (let key in list) {
    formaterData.push({
      name: key,
      value: list[key]
    })
  }
  return formaterData
}
// 维度筛选器变化事件
const filterChange = () => {
  const filterChartData = getChartDataFromDimension(endData.tableData, getCurrentFilterValue(filterValue))
  // console.log('筛选后数据', filterChartData)
  chartInputData.source = filterChartData
  if (endData.time.length > 0) {
    chartInputData.dimension = [endData.time[0], ...endData.measure]
  } else {
    chartInputData.dimension = [endData.dimension[0], ...endData.measure]
  }
  updateChartData()
}
// 图表类型
watch(chartType, (val) => {
  chartInputData.charType = val
  updateChartData()
})

const createCharDataFromRules = () => {
  // 卡片图
  if (endData.dimension.length === 0 && endData.time.length === 0 && endData.measure.length !== 0) {
    let headers = []
    if (endData.tableData.length) {
      headers = Object.keys(endData.tableData[endData.tableData.length === 1 ? 0 : 1])
    }
    // if (headers.length === 1 && endData.tableData.length === 1) {
    //   const val = endData.tableData[0][headers[0]]
    //   // 判断是否为数值或数值字符串
    //   if (!isNaN(parseFloat(val)) && isFinite(val)) {
    //     const cardData = []
    //     endData.tableData.forEach(item => {
    //       cardData.push({
    //         _name: headers[0],
    //         ...item
    //       })
    //     })
    //     cardDataList.value = cardData
    //   }
    // }
    // header.length > 1
    if (endData.tableData.length === 1) {
      let temp = []
      headers.forEach(key => {
        const val = endData.tableData[0][key]
        if (!isNaN(parseFloat(val)) && isFinite(val)) {
          temp.push({
            _name: key,
            [key]: endData.tableData[0][key]
          })
        }
      })
      cardDataList.value = temp
    }
    return
  }
  // 时间+度量 折线 x 时间 y 度量
  if (endData.dimension.length == 0 && endData.time.length === 1 && endData.measure.length !== 0) {
    chartInputData.dimension = [endData.time[0], ...endData.measure]
    chartInputData.source = endData.tableData
    chartInputData.timeAxis = true
    renderChartDom.value = true
  }
  // 维度+度量 柱状 x 维度 y 度量 筛选器-其他维度
  if (endData.time.length === 0 && endData.dimension.length > 0 && endData.measure.length > 0) {
    // 数据组装
    const dimensionListDataNoTime = getDimensionListData(endData.tableData, endData.dimension)
    // 过滤掉第一个维度的数据 默认用第一个维度的数据做X轴
    endData.filterDimensionList = dimensionListDataNoTime.filter(item => item.name != endData.dimension[0])
    endData.source.groupChartData = getChartDataFromDimension(endData.tableData, getDefaultDimensionValue(endData.filterDimensionList))

    chartInputData.axisData = dimensionListDataNoTime
    chartInputData.dimension = [endData.dimension[0], ...endData.measure]
    chartInputData.source = endData.source.groupChartData

    chartFilterList.data = endData.filterDimensionList
    // 初始化筛选器默认值
    endData.filterDimensionList.forEach(item => {
      filterValue[item.name] = item.data.length > 0 ? item.data[0].value : ''
    })
    renderChartDom.value = true
  }
  // 时间+维度+度量 折线 x 时间 y 度量 筛选器-维度
  if (endData.time.length === 1 && endData.dimension.length > 0 && endData.measure.length > 0) {
    // 数据组装
    endData.filterDimensionList = getDimensionListData(endData.tableData, endData.dimension)
    endData.source.groupChartData = getChartDataFromDimension(endData.tableData, getDefaultDimensionValue(endData.filterDimensionList))

    chartInputData.dimension = [endData.time[0], ...endData.measure]
    chartInputData.source = endData.source.groupChartData
    chartInputData.timeAxis = true

    chartFilterList.data = endData.filterDimensionList
    // 初始化筛选器默认值
    endData.filterDimensionList.forEach(item => {
      filterValue[item.name] = item.data.length > 0 ? item.data[0].value : ''
    })
    renderChartDom.value = true
  }
  return chartInputData
}
const charData = createCharDataFromRules()
const chartOption = (charData && createChartOption(charData)) || {}

defineExpose({
  updateChartData
})

</script>

<style scoped>
.type-label {
  margin: 0 10px;
  font-size: 15px;
}

.dimension-label {
  margin: 0 5px;
  font-size: 14px;
}

.card-wrap {
  display: flex;

  .el-card {
    width: 200px;
    margin: 10px;
    text-align: center;
  }

  .el-statistic {
    text-align: center;
  }
}

.el-select {
  width: 130px;
}
</style>
