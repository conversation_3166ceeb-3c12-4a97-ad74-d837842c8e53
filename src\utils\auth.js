import Cookies from 'js-cookie'

const Token = 'Authorization'
const UserName = 'username'

export const setToken = (token) => {
  Cookies.set(Token, token)
}
export const getToken = () => {
  return Cookies.get(Token)
}
export const removeToken = () => {
  Cookies.remove(Token)
}

export const setUsername = (userName) => {
  Cookies.set(UserName, userName)
}
export const getUsername = () => {
  return Cookies.get(UserName)
}
export const removeUsername = () => {
  Cookies.remove(UserName)
}
export const removeUserInfo = () => {
  removeUsername()
  removeToken()
}
