import request from '@/utils/axios'

const url = '/'

function getDbTypeList(params) {
  return request({
    url: url + 'chatdata/dataSource/supportType',
    method: 'get',
    params
  })
}

function getUserList(params) {
  return request({
    url: url + 'user/itcode/search',
    method: 'get',
    params
  })
}
function getProjectList(data) {
  return request({
    url: url + 'chatdata/dataSource/ludp/projects',
    method: 'post',
    data
  })
}
function getSchemaList(data) {
  return request({
    url: url + 'chatdata/dataSource/ludp/schemas',
    method: 'post',
    data
  })
}

function getDbList(data) {
  return request({
    url: url + 'chatdata/dataSource/detailList',
    method: 'post',
    data
  })
}

function getClusterList(data) {
  return request({
    url: url + 'chatdata/dataSource/databricksClusterList',
    method: 'post',
    data
  })
}

function getTableList(data) {
  return request({
    url: url + 'chatdata/getTableList',
    method: 'post',
    data
  })
}

function addDb(data) {
  return request({
    url: url + 'chatdata/dataSource/add',
    method: 'post',
    data
  })
}

function editDb(data) {
  return request({
    url: url + 'chatdata/dataSource/edit',
    method: 'post',
    data
  })
}

function deleteDb(data) {
  return request({
    url: url + 'chatdata/dataSource/delete',
    method: 'post',
    data
  })
}

function getMetadata(data) {
  return request({
    url: url + 'chatdata/getFieldByConnectId',
    method: 'post',
    data
  })
}

function saveMetadata(data) {
  return request({
    url: url + 'chatdata/saveMetadata',
    method: 'post',
    data
  })
}

function getWorkspaceOptions(env) {
  return request({
    url: `chatdata/pbi/${env}/groups`,
    method: 'get'
  })
}

function getReportOptions(env, workspaceId) {
  return request({
    url: `/chatdata/pbi/${env}/${workspaceId}/reports`,
    method: 'get'
  })
}

// /chatdata/pbi/{region}/getReportsByToken
function getReportNameList(env) {
  return request({
    url: `/chatdata/pbi/${env}/getReportsByToken`,
    method: 'get'
  })
}

// /chatdata/pbi/getPbiTable
function getPbiTableList(data) {
  return request({
    url: `/chatdata/pbi/getPbiTable`,
    method: 'post',
    data
  })
}

// /chatdata/getFieldMapping
function getFieldMapping(data) {
  return request({
    url: `/chatdata/getFieldMapping`,
    method: 'post',
    data
  })
}

// /chatdata/getReportDescribe
function getReportDescribe(data) {
  return request({
    url: `/chatdata/getReportDescribe`,
    method: 'post',
    data
  })
}

function getPluginList(data) {
  return request({
    url: url + 'apiConfig/list',
    method: 'post',
    data
  })
}

function saveApi(data) {
  return request({
    url: url + 'chatdata/saveDataApiMapping',
    method: 'post',
    data
  })
}

function updateTableData(id) {
  return request({
    url: url + `chatdata/dataSource/${id}/sync`,
    method: 'post'
  })
}

export {
  getDbTypeList,
  getUserList,
  getProjectList,
  getSchemaList,
  getDbList,
  getClusterList,
  getTableList,
  addDb,
  editDb,
  deleteDb,
  getMetadata,
  saveMetadata,
  getWorkspaceOptions,
  getReportOptions,
  getPluginList,
  saveApi,
  updateTableData,
  getReportNameList,
  getPbiTableList,
  getFieldMapping,
  getReportDescribe
}
