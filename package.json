{"name": "da-gpt", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "dev:test": "vite --mode test", "dev:uat": "vite --mode uat", "dev:prod": "vite --mode prod", "build": "vite build", "build:test": "vite build --mode test", "build:uat": "vite build --mode uat", "build:prod": "vite build --mode prod", "build:test-abroad": "vite build --mode test-abroad", "build:uat-abroad": "vite build --mode uat-abroad", "build:prod-abroad": "vite build --mode prod-abroad", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@antv/ava": "^3.3.1", "@microsoft/fetch-event-source": "^2.0.1", "axios": "^1.6.1", "echarts": "^5.5.0", "element-plus": "^2.4.2", "encryptlong": "^3.1.4", "highlight.js": "^11.9.0", "js-cookie": "^3.0.5", "marked": "^11.1.0", "marked-highlight": "^2.0.9", "pinia": "^2.1.7", "powerbi-client": "^2.23.1", "powerbi-report-authoring": "^2.0.0", "sql-formatter": "^15.2.0", "vue": "^3.3.4", "vue-echarts": "^7.0.3", "vue-i18n": "^9.13.1", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.4.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "less": "^4.2.0", "vite": "^4.4.11"}}