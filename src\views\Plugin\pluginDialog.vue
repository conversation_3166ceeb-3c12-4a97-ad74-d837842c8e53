<script setup>
import { useI18n } from 'vue-i18n'
import { Plus, Minus } from '@element-plus/icons-vue'
import { ref, computed } from 'vue'
import * as api from './api'
import { ElLoading } from 'element-plus'

const { locale, t } = useI18n()

// 声明props
const props = defineProps({
  apiTypeList: Array,
  pluginId: Number
})

// 接收传递的方法
const emit = defineEmits([
  'openVerifyDialog',
  'getPluginList'
])

// 参数类型列表
const paramTypeList = ['string', 'number', 'boolean', 'object', 'list']
// 是否必填列表
const isMustList = computed(() => {
  return [t('COMMON.NO'), t('COMMON.YES')]
})
// 取值来源列表
const sourceList = ['模型识别', '业务透传']

// 是否显示对话框
const dialogVisible = ref(false)
// 对话框标题
const dialogTitle = computed(() => {
  return props.pluginId ? t('PLUGIN.EDIT_PLUGIN') : t('PLUGIN.CREATE_PLUGIN')
})

// 校验URL
const validateUrl = (rule, value, callback) => {
  if (!value) {
    callback(new Error(t('COMMON.PLS_INPUT')))
  } else if (value.slice(0, 7) !== 'http://' && value.slice(0, 8) !== 'https://') {
    callback(new Error(t('PLUGIN.HTTP_ERROR')))
  } else {
    callback()
  }
}
// 插件表单数据
const pluginObj = {
  id: undefined,
  apiName: '',
  apiType: '其他',
  apiComment: '',
  callingMethod: '',
  apiPath: '',
  apiHeadersList: [],
  apiRequestList: [],
  apiResponseList: [
    {
      levelId: 1,
      level: 1,
      parentLId: 0,
      responseName: 'data',
      responseType: '0',
      responseComment: t('PLUGIN.DATA_DESC'),
      children: []
    },
    {
      levelId: 2,
      level: 1,
      parentLId: 0,
      responseName: 'status',
      responseType: '0',
      responseComment: t('PLUGIN.STATUS_DESC'),
      children: []
    },
    {
      levelId: 3,
      level: 1,
      parentLId: 0,
      responseName: 'msg',
      responseType: '0',
      responseComment: t('PLUGIN.MSG_DESC'),
      children: []
    }
  ]
}
const pluginForm = ref(JSON.parse(JSON.stringify(pluginObj)))
const pluginRules = computed(() => {
  return {
    apiName: [{ required: true, message: t('COMMON.PLS_INPUT'), trigger: 'blur' }],
    apiType: [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }],
    apiComment: [{ required: true, message: t('COMMON.PLS_INPUT'), trigger: 'blur' }],
    callingMethod: [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }],
    apiPath: [{ required: true, validator: validateUrl, trigger: 'blur' }],
    apiHeadersList: {
      headerName: [{ required: true, trigger: 'blur' }],
      headerType: [{ required: true, trigger: 'change' }],
      headerComment: [{ required: true, trigger: 'blur' }],
      isMust: [{ required: true, trigger: 'change' }]
    },
    apiRequestList: {
      requestName: [{ required: true, trigger: 'blur' }],
      requestType: [{ required: true, trigger: 'change' }],
      requestComment: [{ required: true, trigger: 'blur' }],
      valueSource: [{ required: true, trigger: 'change' }],
      isMust: [{ required: true, trigger: 'change' }]
    }
  }
})
// 根据id查询插件明细
const getPluginDetail = async (id) => {
  const loading = ElLoading.service({ target: '.el-dialog' })
  let data = {}
  try {
    const res = await api.getPluginDetail({ id })
    loading.close()
    data = res || {}
  } catch (e) {
    loading.close()
  }
  return data
}
// 填充数据
const resetData = async (isOpen) => {
  if (isOpen) {
    const res = props.pluginId ? await getPluginDetail(props.pluginId) : {}
    pluginForm.value = {
      id: res.id || undefined,
      apiName: res.apiName || '',
      apiType: res.apiType || '其他',
      apiComment: res.apiComment || '',
      callingMethod: res.callingMethod || '',
      apiPath: res.apiPath || '',
      apiHeadersList: res.apiHeadersList || [],
      apiRequestList: res.apiRequestList || [],
      apiResponseList: res.apiResponseList || [
        {
          levelId: 1,
          level: 1,
          parentLId: 0,
          responseName: 'data',
          responseType: '0',
          responseComment: t('PLUGIN.DATA_DESC'),
          children: []
        },
        {
          levelId: 2,
          level: 1,
          parentLId: 0,
          responseName: 'status',
          responseType: '0',
          responseComment: t('PLUGIN.STATUS_DESC'),
          children: []
        },
        {
          levelId: 3,
          level: 1,
          parentLId: 0,
          responseName: 'msg',
          responseType: '0',
          responseComment: t('PLUGIN.MSG_DESC'),
          children: []
        }
      ]
    }
  } else {
    pluginForm.value = JSON.parse(JSON.stringify(pluginObj))
    isVerified.value = false
  }
}

// 添加请求头
const addHeader = () => {
  pluginForm.value.apiHeadersList.push({
    headerName: '',
    headerType: '0',
    headerComment: '',
    isMust: '0',
    defaultValue: ''
  })
}
// 删除请求头
const delHeader = (index) => {
  pluginForm.value.apiHeadersList.splice(index, 1)
}

// 添加请求数据
const addRequest = () => {
  pluginForm.value.apiRequestList.push({
    requestName: '',
    requestType: '0',
    requestComment: '',
    valueSource: '模型识别',
    isMust: '0'
  })
}
// 删除请求数据
const delRequest = (index) => {
  pluginForm.value.apiRequestList.splice(index, 1)
}

// 响应数据自增ID
let levelId = 3
// 获取当前最大ID
const getMaxLevelId = (arr = pluginForm.value.apiResponseList) => {
  arr.forEach(item => {
    levelId = item.levelId > levelId ? item.levelId : levelId
    getMaxLevelId(item.children)
  })
}
getMaxLevelId()
// 添加响应数据
const addResponse = (row) => {
  levelId++
  const obj = {
    levelId,
    level: row.level + 1,
    parentLId: row.levelId,
    responseName: '',
    responseType: '0',
    responseComment: '',
    children: []
  }
  row.children.push(obj)
}
// 删除响应数据
const delResponse = (id, arr = pluginForm.value.apiResponseList) => {
  for (let i = 0; i < arr.length; i++) {
    if (arr[i].levelId === id) {
      arr.splice(i, 1)
      return
    } else {
      delResponse(id, arr[i].children)
    }
  }
}

// 插件表单实例
const pluginFormRef = ref()
// 验证插件
const verifyPlugin = () => {
  pluginFormRef.value.validate((valid) => {
    if (valid) {
      const data = JSON.parse(JSON.stringify(pluginForm.value))
      delete data.id
      emit('openVerifyDialog', data)
    }
  })
}

// 是否验证成功
const isVerified = ref(false)
const changeVerified = (val) => {
  isVerified.value = val
}

// 提交插件
const submitPlugin = () => {
  pluginFormRef.value.validate(async (valid) => {
    if (valid) {
      const loading = ElLoading.service({ target: '.el-dialog' })
      try {
        if (pluginForm.value.id) {
          await api.updatePlugin(pluginForm.value)
          emit('getPluginList')
        } else {
          await api.addPlugin(pluginForm.value)
          emit('getPluginList', 1)
        }
        loading.close()
        dialogVisible.value = false
      } catch (e) {
        loading.close()
      }
    }
  })
}

// 暴露属性和方法
defineExpose({
  dialogVisible,
  changeVerified
})
</script>

<template>
  <div class="plugin-dialog">
    <el-dialog
      class="setting-page-dialog-wrap"
      v-model="dialogVisible"
      :title="dialogTitle"
      width="80%"
      :close-on-click-modal="false"
      destroy-on-close
      @open="resetData(true)"
      @closed="resetData(false)"
    >
      <el-scrollbar class="plugin-dialog-inner">
        <el-form
          ref="pluginFormRef"
          :model="pluginForm"
          :rules="pluginRules"
          :label-width="locale === 'en_US' ? '150px' : '100px'"
        >
          <el-form-item
            prop="apiName"
            :label="$t('PLUGIN.PLUGIN_NAME')"
            class="w-460"
          >
            <el-input
              v-model.trim="pluginForm.apiName"
              :placeholder="$t('COMMON.PLS_INPUT')"
            />
          </el-form-item>
          <!-- <el-form-item
            prop="apiType"
            :label="$t('PLUGIN.PLUGIN_TYPE')"
            class="w-460"
          >
            <el-select
              v-model="pluginForm.apiType"
              :placeholder="$t('COMMON.PLS_SELECT')"
            >
              <el-option
                v-for="item in props.apiTypeList"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item> -->
          <el-form-item
            prop="apiComment"
            :label="$t('PLUGIN.PLUGIN_DESC')"
            class="w-460"
          >
            <el-input
              v-model="pluginForm.apiComment"
              type="textarea"
              :rows="6"
              resize="none"
              :placeholder="$t('COMMON.PLS_INPUT')"
            />
          </el-form-item>
          <el-form-item :label="$t('PLUGIN.API_TYPE')">
            <span>HTTP</span>
          </el-form-item>
          <el-form-item
            prop="callingMethod"
            :label="$t('PLUGIN.CALL_METHOD')"
            class="w-460"
          >
            <el-select
              v-model="pluginForm.callingMethod"
              :placeholder="$t('COMMON.PLS_SELECT')"
            >
              <el-option label="POST" value="POST" />
              <el-option label="GET" value="GET" />
            </el-select>
          </el-form-item>
          <el-form-item
            prop="apiPath"
            :label="$t('PLUGIN.PLUGIN_URL')"
            class="w-460"
          >
            <el-input
              v-model.trim="pluginForm.apiPath"
              :placeholder="$t('COMMON.PLS_INPUT')"
            />
            <span class="tips">{{ $t('PLUGIN.HTTP_TIPS') }}</span>
          </el-form-item>
          <el-text tag="b">{{ $t('PLUGIN.REQUEST_INFO') }}</el-text>
          <el-form-item :label="$t('PLUGIN.REQUEST_HEAD')">
            <el-table :data="pluginForm.apiHeadersList" border>
              <el-table-column>
                <template #header>
                  <span class="require-symbol">*</span>
                  <span class="title-text">{{ $t('PLUGIN.PARAM_NAME') }}</span>
                </template>
                <template #default="scope">
                  <el-form-item
                    :prop="`apiHeadersList.${scope.$index}.headerName`"
                    :rules="pluginRules.apiHeadersList.headerName"
                  >
                    <el-input
                      v-model.trim="scope.row.headerName"
                      :placeholder="$t('COMMON.PLS_INPUT')"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column>
                <template #header>
                  <span class="require-symbol">*</span>
                  <span class="title-text">{{ $t('PLUGIN.PARAM_TYPE') }}</span>
                </template>
                <template #default="scope">
                  <el-form-item
                    :prop="`apiHeadersList.${scope.$index}.headerType`"
                    :rules="pluginRules.apiHeadersList.headerType"
                  >
                    <el-select
                      v-model="scope.row.headerType"
                      :placeholder="$t('COMMON.PLS_SELECT')"
                    >
                      <el-option
                        v-for="(item, index) in paramTypeList.slice(0, 3)"
                        :key="item"
                        :label="item"
                        :value="`${index}`"
                      />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column>
                <template #header>
                  <span class="require-symbol">*</span>
                  <span class="title-text">{{ $t('PLUGIN.PARAM_DESC') }}</span>
                </template>
                <template #default="scope">
                  <el-form-item
                    :prop="`apiHeadersList.${scope.$index}.headerComment`"
                    :rules="pluginRules.apiHeadersList.headerComment"
                  >
                    <el-input
                      v-model="scope.row.headerComment"
                      :placeholder="$t('COMMON.PLS_INPUT')"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column>
                <template #header>
                  <span class="require-symbol">*</span>
                  <span class="title-text">{{ $t('PLUGIN.REQUIRED') }}</span>
                </template>
                <template #default="scope">
                  <el-form-item
                    :prop="`apiHeadersList.${scope.$index}.isMust`"
                    :rules="pluginRules.apiHeadersList.isMust"
                  >
                    <el-select
                      v-model="scope.row.isMust"
                      :placeholder="$t('COMMON.PLS_SELECT')"
                    >
                      <el-option
                        v-for="(item, index) in isMustList"
                        :key="item"
                        :label="item"
                        :value="`${index}`"
                      />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column>
                <template #header>
                  <span class="title-text">{{ $t('PLUGIN.DEFAULT_VALUE') }}</span>
                </template>
                <template #default="scope">
                  <el-form-item :prop="`apiHeadersList.${scope.$index}.defaultValue`">
                    <el-input
                      v-model="scope.row.defaultValue"
                      :placeholder="$t('COMMON.PLS_INPUT')"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column width="80">
                <template #header>
                  <el-button class="operate-btn" :icon="Plus" circle @click="addHeader()" />
                </template>
                <template #default="scope">
                  <el-button class="operate-btn" :icon="Minus" circle @click="delHeader(scope.$index)" />
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-form-item :label="$t('PLUGIN.REQUEST_DATA')">
            <el-table :data="pluginForm.apiRequestList" border>
              <el-table-column>
                <template #header>
                  <span class="require-symbol">*</span>
                  <span class="title-text">{{ $t('PLUGIN.PARAM_NAME') }}</span>
                </template>
                <template #default="scope">
                  <el-form-item
                    :prop="`apiRequestList.${scope.$index}.requestName`"
                    :rules="pluginRules.apiRequestList.requestName"
                  >
                    <el-input
                      v-model.trim="scope.row.requestName"
                      :placeholder="$t('COMMON.PLS_INPUT')"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column>
                <template #header>
                  <span class="require-symbol">*</span>
                  <span class="title-text">{{ $t('PLUGIN.PARAM_TYPE') }}</span>
                </template>
                <template #default="scope">
                  <el-form-item
                    :prop="`apiRequestList.${scope.$index}.requestType`"
                    :rules="pluginRules.apiRequestList.requestType"
                  >
                    <el-select
                      v-model="scope.row.requestType"
                      :placeholder="$t('COMMON.PLS_SELECT')"
                    >
                      <el-option
                        v-for="(item, index) in paramTypeList.slice(0, 3)"
                        :key="item"
                        :label="item"
                        :value="`${index}`"
                      />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column>
                <template #header>
                  <span class="require-symbol">*</span>
                  <span class="title-text">{{ $t('PLUGIN.PARAM_DESC') }}</span>
                </template>
                <template #default="scope">
                  <el-form-item
                    :prop="`apiRequestList.${scope.$index}.requestComment`"
                    :rules="pluginRules.apiRequestList.requestComment"
                  >
                    <el-input
                      v-model="scope.row.requestComment"
                      :placeholder="$t('COMMON.PLS_INPUT')"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column>
                <template #header>
                  <span class="require-symbol">*</span>
                  <span class="title-text">{{ $t('PLUGIN.VALUE_SOURCE') }}</span>
                </template>
                <template #default="scope">
                  <el-form-item
                    :prop="`apiRequestList.${scope.$index}.valueSource`"
                    :rules="pluginRules.apiRequestList.valueSource"
                  >
                    <el-select
                      v-model="scope.row.valueSource"
                      :placeholder="$t('COMMON.PLS_SELECT')"
                    >
                      <el-option
                        v-for="item in sourceList"
                        :key="item"
                        :label="item"
                        :value="item"
                      />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column>
                <template #header>
                  <span class="require-symbol">*</span>
                  <span class="title-text">{{ $t('PLUGIN.REQUIRED') }}</span>
                </template>
                <template #default="scope">
                  <el-form-item
                    :prop="`apiRequestList.${scope.$index}.isMust`"
                    :rules="pluginRules.apiRequestList.isMust"
                  >
                    <el-select
                      v-model="scope.row.isMust"
                      :placeholder="$t('COMMON.PLS_SELECT')"
                    >
                      <el-option
                        v-for="(item, index) in isMustList"
                        :key="item"
                        :label="item"
                        :value="`${index}`"
                      />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column width="80">
                <template #header>
                  <el-button class="operate-btn" :icon="Plus" circle @click="addRequest()" />
                </template>
                <template #default="scope">
                  <el-button class="operate-btn" :icon="Minus" circle @click="delRequest(scope.$index)" />
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-text tag="b">{{ $t('PLUGIN.RESPONSE_INFO') }}</el-text>
          <el-form-item :label="$t('PLUGIN.RESPONSE_DATA')">
            <el-table :data="pluginForm.apiResponseList" border row-key="levelId" default-expand-all>
              <el-table-column width="200">
                <template #header>
                  <span class="title-text">{{ $t('PLUGIN.PARAM_NAME') }}</span>
                </template>
                <template #default="scope">
                  <el-input
                    v-model.trim="scope.row.responseName"
                    :placeholder="$t('COMMON.PLS_INPUT')"
                    :disabled="scope.row.level === 1"
                  />
                </template>
              </el-table-column>
              <el-table-column width="140">
                <template #header>
                  <span class="title-text">{{ $t('PLUGIN.PARAM_TYPE') }}</span>
                </template>
                <template #default="scope">
                  <el-select
                    v-model="scope.row.responseType"
                    :placeholder="$t('COMMON.PLS_SELECT')"
                    :disabled="scope.row.levelId === 2 || scope.row.levelId === 3"
                    @change="scope.row.children = []"
                  >
                    <el-option
                      v-for="(item, index) in paramTypeList"
                      :key="item"
                      :label="item"
                      :value="`${index}`"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column>
                <template #header>
                  <span class="title-text">{{ $t('PLUGIN.PARAM_DESC') }}</span>
                </template>
                <template #default="scope">
                  <el-input
                    v-model="scope.row.responseComment"
                    :placeholder="$t('COMMON.PLS_INPUT')"
                    :disabled="scope.row.levelId === 2 || scope.row.levelId === 3"
                  />
                </template>
              </el-table-column>
              <el-table-column
                v-if="pluginForm.apiResponseList[0].responseType === '3' || pluginForm.apiResponseList[0].responseType === '4'"
                width="80"
              >
                <template #default="scope">
                  <el-button
                    v-if="scope.row.responseType === '3' || scope.row.responseType === '4'"
                    class="operate-btn"
                    :icon="Plus"
                    circle
                    @click="addResponse(scope.row)"
                  />
                  <el-button
                    v-if="scope.row.level > 1"
                    class="operate-btn"
                    :icon="Minus"
                    circle
                    @click="delResponse(scope.row.levelId)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
      </el-scrollbar>
      <template #footer>
        <el-button @click="dialogVisible = false">{{ $t('COMMON.CANCEL') }}</el-button>
        <el-button type="primary" plain @click="verifyPlugin()">{{ $t('PLUGIN.VERIFY') }}</el-button>
        <el-tooltip placement="top" :content="$t('PLUGIN.SUBMIT_TIPS')">
          <el-button
            type="primary"
            :disabled="!isVerified"
            @click="submitPlugin()"
          >{{ $t('COMMON.SUBMIT') }}</el-button>
        </el-tooltip>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.plugin-dialog {
  :deep(.el-dialog) {
    margin-top: 10vh;
    .el-dialog__body {
      padding: 0;
      .plugin-dialog-inner>.el-scrollbar__wrap {
        max-height: calc(80vh - 116px);
        .el-form {
          padding: 18px 20px 0;
        }
      }
      .el-form-item {
        &.w-460 {
          width: 460px;
        }
        .el-select {
          width: 100%;
        }
        .tips {
          color: #909399;
          font-size: 12px;
        }
        .cell {
          display: flex;
          align-items: center;
          justify-content: center;
          .require-symbol {
            color: #f56c6c;
            margin-right: 4px;
          }
          .title-text {
            color: #333433;
          }
          .el-form-item {
            flex: 1;
          }
          .operate-btn {
            width: 18px;
            height: 18px;
            padding: 0;
            svg {
              width: 10px;
              height: 10px;
            }
          }
        }
      }
      b {
        display: block;
        margin: 0 0 16px 10px;
        color: #303133;
      }
    }
  }
}
</style>
