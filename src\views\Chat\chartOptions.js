// 检查百分号
const checkPercent = (val) => {
  return typeof val === 'string' && val.charAt(val.length - 1) === '%' ? '%' : ''
}
// 去除百分号
const removePercent = (val) => {
  if (checkPercent(val)) {
    return val.slice(0, -1)
  } else {
    return val
  }
}

// 调色盘颜色列表(此为echarts默认颜色列表)
// const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']

// 折线/面积/柱状option模板
const lineOrColumnOption = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  grid: {
    top: 80
  },
  legend: {
    data: []
  },
  xAxis: [
    {
      type: 'category',
      axisTick: {
        alignWithLabel: true
      },
      data: []
    }
  ],
  yAxis: [],
  series: []
}

// 获取折线/面积/柱状option
const getLineOrColumnOption = (data, advice) => {
  const _option = JSON.parse(JSON.stringify(lineOrColumnOption))
  const keys = Object.keys(data[0])
  const xKey = advice.encode?.x || keys[0]
  const yKeys = advice.encode?.y ? (Array.isArray(advice.encode.y) ? advice.encode.y : [advice.encode?.y]) : keys.slice(1)
  const legends = advice.encode?.color ? [...new Set(data.map(item => item[advice.encode.color]))] : yKeys
  let legendData = []
  if (advice.encode?.color && yKeys.length > 1) {
    legends.forEach((legend) => {
      yKeys.forEach((yKey) => {
        legendData.push(`${legend} - ${yKey}`)
      })
    })
  } else {
    legendData = legends
  }
  // _option.grid.right = yKeys.length > 1 ? '20%' : '10%'
  _option.legend.data = legendData
  _option.xAxis[0].data = advice.encode?.color ? [...new Set(data.map(item => item[xKey]))] : data.map(item => item[xKey])
  _option.xAxis[0].boundaryGap = advice.type !== 'area_chart'
  // _option.yAxis = yKeys.map((key, index) => {
  //   const unit = checkPercent(data[0][key])
  //   return {
  //     type: 'value',
  //     name: key,
  //     alignTicks: true,
  //     offset: index ? (index - 1) * 80 : 0,
  //     axisLine: {
  //       show: true,
  //       lineStyle: {
  //         color: colors[index]
  //       }
  //     },
  //     axisLabel: {
  //       formatter: `{value}${unit}`
  //     }
  //   }
  // })
  const unit = checkPercent(data[0][yKeys[0]])
  _option.yAxis = [{
    type: 'value',
    alignTicks: true,
    axisLine: { show: true },
    axisLabel: {
      formatter: `{value}${unit}`
    }
  }]
  _option.series = legendData.map((key) => {
    return {
      name: key,
      type: advice.type === 'column_chart' ? 'bar' : 'line',
      stack: advice.type === 'column_chart' && advice.encode?.color ? 'total' : undefined,
      emphasis: {
        focus: 'series'
      },
      // yAxisIndex: advice.encode?.color ? undefined : index,
      data: advice.encode?.color ?
        _option.xAxis[0].data.map(x => {
          const legend = yKeys.length > 1 ? key.split(' - ')[0] : key
          const obj = data.find(item => item[advice.encode.color] === legend && item[xKey] === x)
          const yKey = yKeys.length > 1 ? key.split(' - ')[1] : yKeys[0]
          return obj ? removePercent(obj[yKey]) : undefined
        }) :
        data.map(item => removePercent(item[key])),
      step: advice.type === 'step_line_chart' ? 'middle' : undefined,
      areaStyle: advice.type === 'area_chart' ? {} : undefined
    }
  })
  return _option
}

// 条形option模板
const barOption = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  grid: {
    top: 80
  },
  legend: {
    data: []
  },
  xAxis: [],
  yAxis: [
    {
      type: 'category',
      axisTick: {
        alignWithLabel: true
      },
      boundaryGap: true,
      data: []
    }
  ],
  series: []
}

// 获取条形option
const getBarOption = (data, advice) => {
  const _option = JSON.parse(JSON.stringify(barOption))
  const keys = Object.keys(data[0])
  const xKeys = advice.encode?.y ? (Array.isArray(advice.encode.y) ? advice.encode.y : [advice.encode?.y]) : keys.slice(1)
  const yKey = advice.encode?.x || keys[0]
  const legends = advice.encode?.color ? [...new Set(data.map(item => item[advice.encode.color]))] : xKeys
  let legendData = []
  if (advice.encode?.color && xKeys.length > 1) {
    legends.forEach((legend) => {
      xKeys.forEach((xKey) => {
        legendData.push(`${legend} - ${xKey}`)
      })
    })
  } else {
    legendData = legends
  }
  // _option.grid.bottom = xKeys.length > 1 ? '20%' : '10%'
  _option.legend.data = legendData
  _option.yAxis[0].data = advice.encode?.color ? [...new Set(data.map(item => item[yKey]))] : data.map(item => item[yKey])
  // _option.xAxis = xKeys.map((key, index) => {
  //   const unit = checkPercent(data[0][key])
  //   return {
  //     type: 'value',
  //     name: key,
  //     alignTicks: true,
  //     offset: index ? (index - 1) * 80 : 0,
  //     axisLine: {
  //       show: true,
  //       lineStyle: {
  //         color: colors[index]
  //       }
  //     },
  //     axisLabel: {
  //       formatter: `{value}${unit}`
  //     }
  //   }
  // })
  const unit = checkPercent(data[0][xKeys[0]])
  _option.xAxis = [{
    type: 'value',
    alignTicks: true,
    axisLine: { show: true },
    axisLabel: {
      formatter: `{value}${unit}`
    }
  }]
  _option.series = legendData.map((key) => {
    return {
      name: key,
      type: 'bar',
      stack: advice.encode?.color ? 'total' : undefined,
      emphasis: {
        focus: 'series'
      },
      // xAxisIndex: advice.encode?.color ? undefined : index,
      data: advice.encode?.color ?
        _option.yAxis[0].data.map(y => {
          const legend = xKeys.length > 1 ? key.split(' - ')[0] : key
          const obj = data.find(item => item[advice.encode.color] === legend && item[yKey] === y)
          const xKey = xKeys.length > 1 ? key.split(' - ')[1] : xKeys[0]
          return obj ? removePercent(obj[xKey]) : undefined
        }) :
        data.map(item => removePercent(item[key]))
    }
  })
  return _option
}

// 饼状/环形option模板
const pieOrDonutOption = {
  tooltip: {
    trigger: 'item'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    top: '10%'
  },
  series: [
    {
      type: 'pie',
      radius: '',
      label: {},
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        },
        label: {
          show: true,
          fontSize: 20,
          fontWeight: 'bold'
        }
      },
      data: []
    }
  ]
}

// 获取饼状/环形option
const getPieOrDonutOption = (data, advice) => {
  const _option = JSON.parse(JSON.stringify(pieOrDonutOption))
  _option.series[0].radius = advice.type === 'pie_chart' ? '70%' : ['40%', '70%']
  _option.series[0].label =  advice.type === 'donut_chart' ? {
    show: false,
    position: 'center',
    formatter: '{b}: {d}%'
  } : { formatter: '{b}: {d}%' }
  const keys = Object.keys(data[0])
  const nameKey = advice.encode?.color || keys[0]
  const valueKey = advice.encode?.y || keys[1]
  _option.series[0].data = data.map(item => {
    return {
      name: item[nameKey],
      value: item[valueKey]
    }
  })
  return _option
}

// 漏斗option模板
const funnelOption = {
  tooltip: {
    trigger: 'item'
  },
  legend: {
    data: []
  },
  series: [
    {
      type: 'funnel',
      sort: 'descending',
      gap: 2,
      label: {
        show: true,
        position: 'inside',
        formatter: '{b}: {d}%'
      },
      emphasis: {
        label: {
          fontSize: 20
        }
      },
      data: []
    }
  ]
}

// 获取漏斗option
const getFunnelOption = (data, advice) => {
  const _option = JSON.parse(JSON.stringify(funnelOption))
  const keys = Object.keys(data[0])
  const nameKey = advice.encode?.color || keys[0]
  const valueKey = advice.encode?.y || keys[1]
  _option.series[0].data = data.map(item => {
    _option.legend.data.push(item[nameKey])
    return {
      name: item[nameKey],
      value: item[valueKey]
    }
  })
  return _option
}

// 散点/气泡option模板
// const scatterOrBubbleOption = {
//   tooltip: {
//     trigger: 'axis',
//     axisPointer: {
//       type: 'cross'
//     }
//   },
//   grid: {},
//   legend: {
//     data: []
//   },
//   xAxis: {},
//   yAxis: {},
//   series: [
//     {
//       type: 'scatter',
//       symbolSize: 10,
//       data: []
//     }
//   ]
// }

// 获取散点/气泡option
// const getScatterOrBubbleOption = (data, advice) => {
//   const _option = JSON.parse(JSON.stringify(scatterOrBubbleOption))
//   const keys = Object.keys(data[0])
//   const xKey = advice.encode?.x || keys[0]
//   const yKey = advice.encode?.y || keys[1]
//   _option.series[0].data = data.map(item => [item[xKey], item[yKey]])
//   return _option
// }

// 目前支持的图表类型
export const chartType = {
  card_chart: 'CARD_CHART',
  line_chart: 'LINE_CHART',
  step_line_chart: 'STEP_LINE_CHART',
  area_chart: 'AREA_CHART',
  column_chart: 'COLUMN_CHART',
  bar_chart: 'BAR_CHART',
  pie_chart: 'PIE_CHART',
  donut_chart: 'DONUT_CHART',
  funnel_chart: 'FUNNEL_CHART',
  // scatter_plot: 'SCATTER_PLOT',
  // bubble_chart: 'BUBBLE_CHART'
}

// 获取echarts所需option
const getOption = (data, advice) => {
  switch (advice.type) {
    case 'line_chart':
      return getLineOrColumnOption(data, advice)
    case 'step_line_chart':
      return getLineOrColumnOption(data, advice)
    case 'area_chart':
      return getLineOrColumnOption(data, advice)
    case 'column_chart':
      return getLineOrColumnOption(data, advice)
    case 'bar_chart':
      return getBarOption(data, advice)
    case 'pie_chart':
      return getPieOrDonutOption(data, advice)
    case 'donut_chart':
      return getPieOrDonutOption(data, advice)
    case 'funnel_chart':
      return getFunnelOption(data, advice)
    // case 'scatter_plot':
    //   return getScatterOrBubbleOption(data, advice)
    // case 'bubble_chart':
    //   return getScatterOrBubbleOption(data, advice)
    default:
      return {}
  }
}

export default getOption
