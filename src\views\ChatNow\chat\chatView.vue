<script setup>
import { CopyDocument, MoreFilled, Position, Document } from '@element-plus/icons-vue'
import { ref, nextTick, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useI18n } from 'vue-i18n'
import { copyToClipboard, loginRedirect } from '@/utils/util'
import { getToken } from '@/utils/auth'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import chartRender from './chartRender.vue'
import markdownRender from '@/components/MarkdownRender/markdownRender.vue'
import { ElMessage } from 'element-plus'
import * as api from './api'
// import sendChatMsgIcon from '../icons/sendChatMsgIcon.vue'
import feedBack from './feedBack.vue'

const route = useRoute()
const router = useRouter()
const user = useUserStore()
const { t } = useI18n()

// 流式请求id 数据洞察使用
const currentRequestId = ref('')

// 踩按钮原因列表
const disLikeBtnList = [{
  id: 1,
  zhTxt: '数据不准确',
  enTxt: 'Inaccurate data'
}, {
  id: 2,
  zhTxt: '图表呈现不佳',
  enTxt: 'Poor chart presentation'
}, {
  id: 3,
  zhTxt: '知识质量不佳',
  enTxt: 'Poor knowledge quality'
}, {
  id: 4,
  zhTxt: '洞察质量不佳',
  enTxt: 'Poor insight quality'
}, {
  id: 5,
  zhTxt: '其他',
  enTxt: 'Other'
}]
// 赞按钮原因列表
const likeBtnList = [{
  id: 1,
  zhTxt: '数据准确',
  enTxt: 'Accurate data'
}, {
  id: 2,
  zhTxt: '图表呈现恰当',
  enTxt: 'Appropriate chart presentation'
}, {
  id: 3,
  zhTxt: '知识质量良好',
  enTxt: 'Good knowledge quality'
}, {
  id: 4,
  zhTxt: '洞察清晰',
  enTxt: 'Clear insights'
}, {
  id: 5,
  zhTxt: '其他',
  enTxt: 'Other'
}]

const getLocalStorageId = () => {
  let currentId = localStorage.getItem('currentId')
  if(currentId === "undefined") {
    return undefined
  }
  return currentId
}

// 欢迎语更多问题状态
const showMore = ref(false)
const showMoreQuestion = (e) => {
  showMore.value = true
  e.target.style.display = 'none'
}

// 欢迎语信息
let welcomeInfo = ref({})
const getWelcomeInfo = async() => {
  const welcome = await api.getWelcomeInfo({
    sceneName: route.query.select
  })
  const recommendQuestion = JSON.parse(welcome.recommendQuestion)
  welcome.recommendQuestion = recommendQuestion.length === 1 && recommendQuestion[0] === "" ? [] : recommendQuestion
  welcomeInfo.value = welcome
}

// 当前选择的模型
const modelName = ref('')
// 模型列表
const modelList = ref([])
// modelName.value = user.model[0] || ''
modelList.value = user.model

// agent对话 不用校验用户模型权限
modelName.value = route.query.scene === 'chat_with_scene' ? route.query.model : ''

// 获取模型列表
const getModelList = async() => {
  try {
    const res = await api.getModelList()
    modelList.value = [...res]
    const hisModel = route.query.model
    if(hisModel) {
      const has = res.filter(item => item.modelName === hisModel)
      if(has.length) {
        modelName.value = hisModel
      }else {
        modelName.value = res[0].modelName
      }
    }else {
      modelName.value = res[0].modelName
    }
  } catch (error) {
    console.log("模型列表数据获取出错")
  }
}

// 当前选择的知识库/数据源
const selectParam = ref('')
// 切换之前的知识库/数据源
let lastSelectParam = ''

// 知识库/数据源列表
const selectList = ref([])
// 获取知识库列表
const getKnowledgeList = async () => {
  const res = await api.getKnowledgeList()
  selectList.value = res || []
  if (route.query.select && selectList.value.includes(route.query.select)) {
    selectParam.value = route.query.select
  } else {
    selectParam.value = selectList.value[0] || ''
  }
}
// 获取数据源列表
const getDbList = async () => {
  const res = await api.getDbList()
  selectList.value = res || []
  if (route.query.select && selectList.value.includes(route.query.select)) {
    selectParam.value = route.query.select
  } else {
    selectParam.value = selectList.value[0] || ''
  }
}
// 获取应用列表
const getAppList = async () => {
  const res = await api.getAppList()
  selectList.value = res || []
  if (route.query.select && selectList.value.includes(route.query.select)) {
    selectParam.value = route.query.select
  } else {
    selectParam.value = selectList.value[0] || ''
  }
}

// 滚动栏实例
const scrollbarRef = ref()
// 消息列表实例
const messageListRef = ref()
// 消息列表滚动至指定位置(不传值则滚动至底部)
const scrollTo = async (num) => {
  // 需要通过nextTick等待DOM更新完成
  await nextTick()
  if (messageListRef.value) {
    // 需要等待echarts全部加载完毕
    setTimeout(() => {
      const max = messageListRef.value.clientHeight
      scrollbarRef.value.setScrollTop(isNaN(num) ? max : max - num)
    })
  }
}
// 滚动事件
const handleScroll = (obj) => {
  if (obj.scrollTop === 0 && (pageNum * pageSize < pageTotal)) {
    pageNum++
    getMessageList()
  }
}

// 历史记录分页相关
let pageNum = 1
let pageSize = 6
let pageTotal = 0
const viewLoading = ref(false)
const chatLoading = ref(false)
// 消息列表
const messageList = ref([])
// 获取消息列表
const getMessageList = async () => {
  if (!route.query.id && !getLocalStorageId()) {
    if(route.query.scene === 'chat_with_scene') {
      await getWelcomeInfo()
      messageList.value = [{ type: 'welcome', roundIndex: -1 }]
    }
    if (route.query.scene === 'chat_mct_demo') {
      messageList.value = [{ type: 'welcome', roundIndex: -1 }]
    }
    return
  }
  viewLoading.value = pageNum === 1
  chatLoading.value = pageNum !== 1
  const oldH = messageListRef.value && messageListRef.value.clientHeight
  try {
    const res = await api.getMessageList({
      convUid: route.query.id,
      pageNum,
      pageSize
    })
    viewLoading.value = false
    chatLoading.value = false
    if (res?.list?.length) {
      const list = res.list.filter(item => {
        if (item.type === 'human' || item.type === 'view') {
          // 判断返回的消息是否为JSON字符串，是则以data形式赋值，否则不做处理
          try {
            const data = JSON.parse(item.messageDetail)
            if (!Array.isArray(data.data)) {
              data.data = []
            }
            // 返回的message是否为空来做报错消息提示展示
            // if (!data.message) {
            //   item.messageDetail = data.aitext
            // } else {
            //   item.messageDetail = data.message
            // }
            // Jira MTYLH-2891 去掉aitext的显示
            if (data.message) {
              item.messageDetail = data.message
            }
            // item.messageDetail = data.aitext
            item.chartData = data
            return true
          } catch (e) {
            return true
          }
        } else {
          return false
        }
      })
      // selectPrompt.value = list[list.length - 1]?.promptId // 历史最后一次prompt记录赋值给当前
      selectSystemPrompt.value = list[list.length - 1]?.sysPromptId // 历史最后一次prompt记录赋值给当前
      selectAppPrompt.value = list[list.length - 1]?.appPromptId // 历史最后一次prompt记录赋值给当前
      pageTotal = res.total || 0

       // 历史会话 滚动至顶部
      const topFlag = route.query.scene === 'chat_with_scene' && route.query.id && pageNum * pageSize >= pageTotal
      if(topFlag && Object.keys(welcomeInfo.value).length === 0) {
        await getWelcomeInfo()
      }

      messageList.value = [
        ...route.query.scene === 'chat_mct_demo' && (pageNum * pageSize >= pageTotal) ? [{ type: 'welcome', roundIndex: -1 }] : [],
        ...topFlag ? [{ type: 'welcome', roundIndex: -1 }] : [],
        ...list,
        ...messageList.value
      ]
      if (pageNum === 1) {
        scrollTo()
      } else {
        scrollTo(oldH)
      }
    }
  } catch (e) {
    viewLoading.value = false
    chatLoading.value = false
  }
}

const showWarning = ref(true)
const sendLoading = ref(false)
// 输入框文本
const userInput = ref('')
// 检测是否出现RCA相关关键字
const isRCA = (str) => {
  const regex = new RegExp('rca|suggestion|action', 'i')
  return regex.test(str)
}
// 检测是否出现QtQ相关关键字
const isQtQ = (str) => {
  const regex = new RegExp('Show QtQ difference of sum expense by fiscal quarter in ISG, LA', 'i')
  return regex.test(str)
}

// 检测是否出现PCON相关关键字
const isPCON = (str) => {
  const regex = new RegExp('Show Total PCON by Geo and Segment in 2023 Q3', 'i')
  return regex.test(str)
}

// 检测是否出现UPPH相关关键字
const isUPPH = (str) => {
  const regex = new RegExp('upph', 'i')
  return regex.test(str)
}
// 获取AI回复
const getAIResponse = (isNew) => {
  sendLoading.value = true
  const userInputVal = userInput.value
  userInput.value = ''
  const roundIndex = messageList.value.length ? messageList.value[messageList.value.length - 1].roundIndex + 1 : 1
  messageList.value.push({
    messageDetail: userInputVal,
    modelName: modelName.value,
    roundIndex,
    type: 'human'
  }, {
    messageDetail: '',
    modelName: modelName.value,
    roundIndex,
    type: 'view',
    evaluateStatus: 0,
    evaluateType: 0,
    otherMessage: null,
    reason: null
  })
  scrollTo()

  // Chat MCT Demo 特殊逻辑，检测到指定关键字使用不同的数据源
  const _RCA = isRCA(userInputVal)
  const params = {
    chatMode: route.query.scene === 'chat_mct_demo' ? 'chat_with_data' : route.query.scene,
    // convUid: route.query.id,
    convUid: route.query.id || getLocalStorageId(),
    modelName: modelName.value,
    selectParam: route.query.scene === 'chat_mct_demo' ? (_RCA ? 'processed_rca_data' : 'MCT_KPI') : selectParam.value,
    userInput: userInputVal,
    // isAddHistory: route.query.scene === 'chat_with_data' ? 1 : 0,
    isAddHistory: 0, // 8.29 yueqi：暂时关闭多轮会话
    // promptId: route.query.scene === 'chat_mct_demo' ? (_RCA ? 119 : 111) : selectPrompt.value
    sysPromptId: route.query.scene === 'chat_mct_demo' ? (_RCA ? 119 : 111) : selectSystemPrompt.value,
    appPromptId: route.query.scene === 'chat_mct_demo' ? (_RCA ? 119 : 111) : selectAppPrompt.value
  }
  fetchChat(params, isNew)
}
// 聊天流式请求
const fetchChat = (params, isNew) => {
  const ctrl = new AbortController()
  try {
    fetchEventSource(
      `${import.meta.env.VITE_APP_BASE_API_URL}/api/v1/chat/completionsV1`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: getToken()
        },
        body: JSON.stringify(params),
        signal: ctrl.signal,
        openWhenHidden: true,
        onopen(res) {
          currentRequestId.value = res.headers.get('requestId')
        },
        onmessage(event) {
          // 判断返回的消息是否为JSON字符串，是则以data形式赋值，否则以knowledge形式赋值
          try {
            const data = JSON.parse(event.data)
            // 开发环境下打印日志方便查看问题
            if (import.meta.env.VITE_APP_MODE !== 'PROD') {
              console.log(data, '处理后的数据')
            }
            if (!('isEnd' in data) && !Array.isArray(data.data)) {
              data.data = []
            }
            // chatKnowladge
            if('isEnd' in data && !data.isEnd) {
              messageList.value[messageList.value.length - 1].messageDetail += data.data
            }
            // 返回的message是否为空来做报错消息提示展示
            // if (!data.message) {
            //   messageList.value[messageList.value.length - 1].messageDetail = data.aitext
            // } else {
            //   messageList.value[messageList.value.length - 1].messageDetail = data.message
            // }
            // Jira MTYLH-2891 去掉aitext的显示
            if (data.message) {
              messageList.value[messageList.value.length - 1].messageDetail = data.message
            }
            // messageList.value[messageList.value.length - 1].messageDetail = data.aitext
            if(!('isEnd' in data)) {
              // 区分实时会话和历史会话
              data.isFetchEventSource = true
              messageList.value[messageList.value.length - 1].chartData = data
            }
            loginRedirect(data.message)
          } catch (e) {
            messageList.value[messageList.value.length - 1].messageDetail = event.data
          }
          scrollTo()
        },
        onclose() {
          ctrl.abort()
          if (isNew) {
            user.setChatList(1)
          // } else if (route.query.select !== selectParam.value) {
          } else if (lastSelectParam !== selectParam.value) {
            // 流式接口请求成功的情况下更新用户最新选择的数据源/知识库
            const queryId = route.query.id || getLocalStorageId()
            user.setChatParam(queryId, selectParam.value)

            lastSelectParam = selectParam.value

            // 刷新页面影响洞察展示
            // router.replace({
            //   path: '/chatNow',
            //   query: {
            //     scene: route.query.scene,
            //     select: selectParam.value,
            //     id: queryId
            //   }
            // })
          }
          sendLoading.value = false
        },
        onerror(error) {
          messageList.value[messageList.value.length - 1].messageDetail = t('CHAT.CHAT_ERROR')
          sendLoading.value = false
          throw new Error(error)
        }
      }
    )
  } catch (e) {
    ctrl.abort()
    messageList.value[messageList.value.length - 1].messageDetail = t('CHAT.CHAT_ERROR')
    sendLoading.value = false
    throw new Error(e)
  }
}
// 是否为新的对话Id
let isNewId = false
// 创建新的对话
const getNewChatId = async () => {
  viewLoading.value = true
  try {
    const res = await api.getNewChatId({ chatMode: route.query.scene })
    viewLoading.value = false
    if (res) {
      isNewId = true
      localStorage.setItem('currentId',res)
      // router.replace({
      //   path: '/chatNow',
      //   query: {
      //     scene: route.query.scene,
      //     select: selectParam.value,
      //     id: res
      //   }
      // }).then(() => {
      //   getAIResponse(true)
      // })
      getAIResponse(true)
    }
  } catch (e) {
    viewLoading.value = false
  }
}
// 打字机效果
let num = 0
let wordTimeout = null
const autoWord = (str) => {
  wordTimeout = setTimeout(() => {
    messageList.value[messageList.value.length - 1].messageDetail = str.substr(0, num)
    scrollTo()
    num += 10
    if (num < str.length + 10) {
      autoWord(str)
    } else {
      clearTimeout(wordTimeout)
      wordTimeout = null
    }
  }, 100)
}
// Chat PRC Demo 假数据
const getPRCDemoData = () => {
  sendLoading.value = true
  const userInputVal = userInput.value
  userInput.value = ''
  const roundIndex = messageList.value.length ? messageList.value[messageList.value.length - 1].roundIndex + 1 : 1
  messageList.value.push({
    messageDetail: userInputVal,
    modelName: modelName.value,
    roundIndex,
    type: 'human'
  }, {
    messageDetail: '',
    modelName: modelName.value,
    roundIndex,
    type: 'view'
  })
  scrollTo()

  setTimeout(() => {
    if (messageList.value.length === 2) {
      const str = '报表名称：[大区排名](https://ciop.lenovo.com/#/MTY5MDk0NzkwOTU0MOWkp+WMuuaOkuWQjQ==), 报表摘要：所有大区PRC Total，REL，SMB，CON的REV和CA完成率和排名\n\n报表名称：[大区销售进展日报](https://ciop.lenovo.com/#/MTY5MDk0ODAyMTg3NeWkp+WMuumUgOWUrui/m+WxlQ==), 报表摘要：所有大区REV和CA Outlook完成率，Order QTD, Order QTD%, Order YoY，SI Booking QTD, SI Booking QTD%， SI Booking QTD YoY\n\n报表名称：[REL战区排名](https://ciop.lenovo.com/#/MTY5MDk0NTkzMTE4NuaImOWMuumUgOWUrui/m+WxlQ==)，报表摘要：REL所有大区，战区，Segment (IDG，ISG，SSG)的REV和CA目标完成率及排名\n\n报表名称：[REL战区销售进展](https://ciop.lenovo.com/#/MTY5MDk0NTkzMTE4NuaImOWMuumUgOWUrui/m+WxlQ==), 报表摘要：所有大区及战区指标，包含REV和CA Outlook完成率，Order QTD, Order QTD%, Order YoY，SI Booking QTD, SI Booking QTD%， SI Booking QTD YoY，总商机，剩余任务，剩余商机，剩余商机V值'
      autoWord(str)
    } else {
      messageList.value[messageList.value.length - 1].messageDetail = '用户想要查看所有大区REL完成率排名情况。'
      messageList.value[messageList.value.length - 1].chartData = {
        type: 'response_pie_chart',
        // aitext: '用户想要查看所有大区REL完成率排名情况。',
        aitext: '',
        sql: 'SELECT 大区, REL完成率 FROM 大区排名 ORDER BY REL完成率 DESC',
        data: [
          { 大区: '华北大区', REL完成率: 0.57 },
          { 大区: '华东大区', REL完成率: 0.47 },
          { 大区: '西南大区', REL完成率: 0.46 },
          { 大区: '东南大区', REL完成率: 0.46 },
          { 大区: '西北大区', REL完成率: 0.39 },
          { 大区: '中东大区', REL完成率: 0.38 },
          { 大区: '华南大区', REL完成率: 0.34 },
          { 大区: '东北大区', REL完成率: 0.31 }
        ]
      }
      scrollTo()
    }
    sendLoading.value = false
  }, 2000)
}
// Chat MCT Demo 特殊逻辑，检测到指定关键字展示特殊按钮
const showRCABtn = (index) => {
  if (route.query.scene === 'chat_mct_demo') {
    const str = messageList.value[index - 1]?.messageDetail
    const _RCA = isRCA(str)
    const _UPPH = isUPPH(str)
    return !_RCA && _UPPH ? (index === messageList.value.length - 1 ? sendLoading.value === false : true) : false
  } else {
    return false
  }
}
const autoSendMessage = (str) => {
  userInput.value = str
  sendMessage()
}
const inputAdvices = [
  'shows MTD of MCR in May,2024',
  'shows MTD of MCR by BU',
  'shows daily trend of ATS of Server in June,2024',
  'RCA on Online UPPH',
  'RCA on Working Hours'
]
// 发送信息
const sendMessage = () => {
  if (sendLoading.value === true) {
    return
  }
  if (!userInput.value) {
    ElMessage.warning(t('CHAT.EMPTY_WARNING'))
    return
  }
  if (route.query.scene === 'chat_prc_demo') {
    getPRCDemoData()
    return
  }
  // if (route.query.id) {
  //   getAIResponse()
  // } else {
  //   getNewChatId()
  // }
  if (route.query.id || getLocalStorageId()) {
    getAIResponse()
  } else {
    getNewChatId()
  }
}
// 输入框回车事件
const handleInputEnter = (e) => {
  if (!e.altKey && !e.ctrlKey && !e.metaKey && !e.shiftKey) {
    e.returnValue = false
    sendMessage()
  }
}

const feedbackLoading = ref(false)
// 评分表单数据
const reviewForm = ref({
  quesType: '',
  score: 0,
  messages: ''
})
// 获取当前评分
const getTheFeedback = async (roundIndex) => {
  feedbackLoading.value = true
  reviewForm.value = {
    quesType: '',
    score: 0,
    messages: ''
  }
  try {
    const res = await api.getTheFeedback({
      convUid: route.query.id || getLocalStorageId(),
      // convUid: route.query.id,
      roundIndex
    })
    feedbackLoading.value = false
    if (res) {
      const { quesType, score, messages } = res
      reviewForm.value = {
        quesType,
        score,
        messages
      }
    }
  } catch (e) {
    feedbackLoading.value = false
  }
}
// 提交评分
const commitFeedback = async (roundIndex, index) => {
  feedbackLoading.value = true
  try {
    const param = {
      convUid: route.query.id || getLocalStorageId(),
      // convUid: route.query.id,
      roundIndex,
      knowledgeSpace: selectParam.value,
      question: messageList.value[index - 1].messageDetail,
      ...reviewForm.value
    }
    const res = await api.commitFeedback(param)
    feedbackLoading.value = false
    if (res) {
      ElMessage.success(`${t('COMMON.SUBMIT')} ${t('COMMON.SUCCESS')}`)
      messageList.value[index].feedbackVisible = false
    } else {
      ElMessage.warning(`${t('COMMON.SUBMIT')} ${t('COMMON.FAIL')}`)
    }
  } catch (e) {
    feedbackLoading.value = false
  }
}

// 选择的提示语ID
// let selectPrompt = ref()
let selectSystemPrompt = ref()
let selectAppPrompt = ref()
// 提示语列表
// const promptList = ref([])
const systemPromptList = ref([])
const appPromptList = ref([])

// 是否为itAdmin。是则显示系统提示语和应用提示语；不是则只显示应用提示语
const isAdmin = ref(user.promptTab)
// 提示语选中的Tab
const promptTabName = ref('application')
const changePromptTab = () => {
  getPromptList()
}
// const promptList = ref([])
// 获取提示语列表
const getPromptList = async () => {
  const res = await api.getPromptList({
    chatScene: route.query.scene,
    promptType: promptTabName.value // application: 应用提示语, system: 系统提示语【只有itadmin可见】
  })
  if (promptTabName.value === 'application') {
    appPromptList.value = res || []
  } else {
    systemPromptList.value = res || []
  }
}
const getAllPromptList = async () => {
  let applicationRes = []
  let systemRes = []
  const res = await api.getPromptList({
    chatScene: route.query.scene,
    promptType: '' // application: 应用提示语, system: 系统提示语【只有itadmin可见】
  })
  res.forEach(element => {
    if(element.promptType === "application") {
      applicationRes.push(element)
    }
    if(element.promptType === "system") {
      systemRes.push(element)
    }
  })
  appPromptList.value = applicationRes
  systemPromptList.value = systemRes

  if(route.query.scene === 'chat_knowledge' && !selectSystemPrompt.value) {
    // 19 知识库默认系统提示词
    selectSystemPrompt.value = 19
  }
}
// 提示语气泡实例
const promptPopoverRef = ref()
// 填充提示语
const setPrompt = (id, type) => {
  // selectPrompt.value = id
  if (type === 'system') {
    selectSystemPrompt.value = id
  } else {
    selectAppPrompt.value = id
  }
  promptPopoverRef.value.hide()
}
// const setPromptVal = (id, type) => {
//   if (type === 'system') {
//     selectSystemPrompt.value = id
//   } else {
//     selectAppPrompt.value = id
//   }
// }
// const handleCascaderChange = (list) => {
//   const type = list[0]
//   const id = list[list.length - 1]
//   // setPrompt(id, type)
// }

watch(
  () => route.query.id,
  () => {
    showWarning.value = true
    if (isNewId) {
      isNewId = false
    } else {
      if (route.query.scene === 'chat_with_data') {
        getDbList()
      } else if (route.query.scene === 'chat_knowledge') {
        getKnowledgeList()
      } else if (route.query.scene === 'chat_with_scene') {
        getAppList()
      }
      pageNum = 1
      pageTotal = 0
      messageList.value = []
      getMessageList()
    }
    // getPromptList()
    // getAllPromptList()
    if ((route.query.scene === 'chat_with_data') || (route.query.scene === 'chat_knowledge')) {
      getAllPromptList()
      getModelList()
    }
  },
  { immediate: true }
)

const dialogTitle = computed(() => {
  const title = t('CHAT.DIALOG_WINDOW')
  return route.query.scene === 'chat_with_scene' ? `${title} [${route.query.select}]` : title
})

// 更新item数据控制反馈状态
const updatedItemData = (type, data) => {
  // 赞/踩点击状态
  if(typeof(type) === 'number') {
    data.evaluateType = type
  }
  // 反馈信息状态
  if(typeof(type) === 'object') {
    data.evaluateStatus = 1
    data.reason = type.tag
    data.otherMessage = type.text
  }
}
const hidePopover = (type, data) => {
  data[type] = false
}

const firstGetSysPrompt = ref(true)
watch(selectParam, async(val) => {
  if(lastSelectParam === '') {
    lastSelectParam = selectParam.value
  }

  if(route.query.scene === 'chat_with_scene' || route.query.scene === 'chat_knowledge') return
  try {
    const res = await api.getSystemPrompt({
      "dbBusiName": val
    })
    // 系统提示词列表数据查询慢 可能会先显示id
    if(firstGetSysPrompt.value) {
      setTimeout(() => {
        selectSystemPrompt.value = res.id
        firstGetSysPrompt.value = false
      }, 3000)
    }else {
      selectSystemPrompt.value = res.id
    }
  } catch (error) {
    console.log('chatData: 默认系统提示词 获取出错')
  }
})
</script>

<template>
  <div class="chat-title">
    <p>{{ dialogTitle }}</p>
    <div class="title-right">
      <div v-if="route.query.scene === 'chat_with_data' || route.query.scene === 'chat_knowledge'" class="prompt-wrap">
        <div class="prompt-select-wrap">
          <span>{{ route.query.scene === 'chat_with_data' ? $t('CHAT.DATA_SOURCE') : $t('PROMPT.TYPE_KNOW') }}</span>
          <el-select class="datasource-select" v-model="selectParam" :placeholder="$t('COMMON.PLS_SELECT')" size="small" filterable>
            <el-option v-for="item in selectList" :key="item" :label="item" :value="item"
              class="datasource-select-option" />
          </el-select>
        </div>
        <div class="prompt-select-wrap" v-if="isAdmin">
          <span>{{ $t('CHAT.SYSTEM_PROMPT') }}</span>
          <el-select v-model="selectSystemPrompt" :placeholder="$t('COMMON.PLS_SELECT')" size="small" filterable>
            <el-option v-for="item in systemPromptList" :key="item.id" :label="item.promptName" :value="item.id"/>
          </el-select>
        </div>
        <div class="prompt-select-wrap">
          <span>{{ $t('CHAT.APP_PROMPT') }}</span>
          <el-select v-model="selectAppPrompt" :placeholder="$t('COMMON.PLS_SELECT')" size="small" clearable filterable>
            <el-option v-for="item in appPromptList" :key="item.id" :label="item.promptName" :value="item.id"/>
          </el-select>
        </div>
        <div class="prompt-select-wrap">
          <span>{{$t('APP.MODEL_NAME')}}</span>
          <el-select class="chat-model-select" v-model="modelName" placement="bottom-end" :placeholder="$t('COMMON.PLS_SELECT')" size="small">
            <el-option v-for="item in modelList" :key="item.modelName" :label="item.modelName" :value="item.modelName"/>
          </el-select>
        </div>
      </div>
    </div>
  </div>
  <div class="chat-wrap">
    <div class="chat-view" v-loading="viewLoading">
      <!-- <div
        v-if="route.query.scene === 'chat_with_data' || route.query.scene === 'chat_knowledge' || route.query.scene === 'chat_with_scene'"
        class="chat-view-top">
        <el-select v-model="selectParam" filterable>
          <el-option v-for="item in selectList" :key="item" :label="item" :value="item" />
        </el-select>
      </div> -->
      <div class="chat-view-layout">
        <el-scrollbar ref="scrollbarRef" class="chat-content" @scroll="handleScroll">
          <div v-if="chatLoading" class="chat-content-loading" v-loading="chatLoading" />
          <ul v-if="messageList.length" ref="messageListRef" class="chat-content-list">
            <div v-if="messageList[0].type === 'welcome'" class="welcome-placeholder" />
            <li v-for="(item, index) in messageList" :key="`${item.roundIndex}-${item.type}`"
              :class="['chat-content-item', item.type]">
              <div style="display: flex;position:relative;">
                <!-- <User v-if="item.type === 'human'" class="chat-human-icon" /> -->
                <i v-if="item.type === 'human'" class="iconfont icon-yonghu1 chat-human-icon"></i>
                <el-icon v-if="item.type === 'view' || item.type === 'welcome'" class="chat-ai-icon">
                  <img src="./imgs/ai.png" alt="">
                </el-icon>
                <div class="welcome-wrap" style="width: calc(100% - 40px);">
                  <template v-if="item.type === 'welcome' && route.query.scene === 'chat_mct_demo'">
                    <div class="welcome-text">Hi, Welcome to IntelliData.</div>
                    <div class="welcome-text">You can chat data with below questions:</div>
                    <div class="welcome-advices">
                      <el-tag v-for="item in inputAdvices" :key="item" size="large" @click="autoSendMessage(item)">{{
                        item }}</el-tag>
                    </div>
                  </template>
                  <template v-if="item.type === 'welcome'">
                    <div class="agent-welcome">
                      <div class="welcome-text-agent wlc-bold">{{ $t('CHAT.WELCOME_HI') }} {{  route.query.select  }}</div>
                      <div class="welcome-text-agent app-desc">{{ welcomeInfo.sceneComment }}</div>
                      <template v-if="welcomeInfo.connectComment">
                        <div class="welcome-text-agent app-datasource">{{ $t('CHAT.WELCOME_DATASOURCE') }}:</div>
                        <div class="welcome-text-agent wlc-dot">{{ welcomeInfo.connectComment }}</div>
                      </template>
                      <div class="demo-question" v-if="welcomeInfo.recommendQuestion.length">
                        <div class="advices-title wlc-bold">
                          <div>{{ $t('CHAT.WELCOME_QUESTION') }}:</div>
                        </div>
                        <div class="welcome-advices">
                          <div v-for="item in welcomeInfo.recommendQuestion.slice(0,3)" :key="item">
                            <el-tag class="agent-tag" disable-transitions @click="autoSendMessage(item)">{{item }}</el-tag>
                          </div>
                          <template v-if="welcomeInfo.recommendQuestion.length > 3">
                            <div v-for="item in welcomeInfo.recommendQuestion.slice(3)" :key="item" v-show="showMore">
                              <el-tag class="agent-tag" disable-transitions @click="autoSendMessage(item)">{{item }}</el-tag>
                            </div>
                            <div class="btn-more" @click="showMoreQuestion($event)">
                              <el-icon style="transform: rotate(90deg);vertical-align: middle;margin-left: 5px;"><DArrowRight /></el-icon>
                              {{ $t('CHAT.MORE_QUESTION') }}
                            </div>
                          </template>
                        </div>
                      </div>
                    </div>
                  </template>
                  <!--                <markdown-render-->
                  <!--                  v-if="item.messageDetail"-->
                  <!--                  :context="item.messageDetail"-->
                  <!--                />-->
                  <!--                Jira MTYLH-2891 去掉aitext的显示-->
                  <markdown-render v-if="item.type === 'human' || (item.type === 'view' && item.chartData?.message)"
                    :context="item.messageDetail" />
                  <div class="chat-knowladge-wrap" v-if="item.type === 'view' && route.query.scene === 'chat_knowledge'">
                    <markdown-render :context="item.messageDetail"/>
                  </div>
                  <chart-render v-if="item.chartData" :chartData="item.chartData" :sessionid="route.query.id || getLocalStorageId()" :requestId="item.requestId || currentRequestId"
                    :userQuestionQtQ="isQtQ(messageList[index - 1]?.messageDetail)"
                    :userQuestionPCON="isPCON(messageList[index - 1]?.messageDetail)"
                    :isRCA="route.query.scene === 'chat_mct_demo' ? isRCA(messageList[index - 1]?.messageDetail) : false" />
                </div>
              </div>
              <div v-if="item.type === 'view'" class="chat-operation">
                <div style="padding-left: 40px">
                  <el-button v-if="showRCABtn(index)" type="primary"
                    @click="autoSendMessage('RCA on Online UPPH with Top 10 factors')">Check RCA</el-button>
                </div>
                <div style="display: flex">
                  <feedBack
                    v-if="item.evaluateType !== 2"
                    :type="1"
                    :title="$t('CHAT.LIKE_REASON')"
                    :reasonList="likeBtnList"
                    :resData="item"
                    :requestId="currentRequestId || item.requestId"
                    @updatedItemData="updatedItemData($event, item)"
                    @hidePopover="hidePopover($event, item)"
                  >
                    <template #optBtn>
                      <el-button class="opt-icon-block" text>
                        <i class="iconfont icon-zan"></i>
                      </el-button>
                    </template>
                  </feedBack>
                  <feedBack
                    v-if="item.evaluateType !== 1"
                    :type="2"
                    :title="$t('CHAT.DISLIKE_REASON')"
                    :reasonList="disLikeBtnList"
                    :resData="item"
                    :requestId="currentRequestId || item.requestId"
                    @updatedItemData="updatedItemData($event, item)"
                    @hidePopover="hidePopover($event, item)"
                  >
                    <template #optBtn>
                      <el-button class="opt-icon-block" text>
                        <i class="iconfont icon-cai"></i>
                      </el-button>
                    </template>
                  </feedBack>
                  <!-- <el-tooltip :content="$t('COMMON.COPY')" placement="top"> -->
                    <el-button class="chat-operation-button" size="large" :icon="CopyDocument" text
                      @click="copyToClipboard(item.messageDetail)" />
                  <!-- </el-tooltip> -->
                  <el-popover v-model:visible="item.feedbackVisible" placement="bottom" :width="320" trigger="click"
                    @before-enter="getTheFeedback(item.roundIndex)">
                    <template #reference>
                      <div>
                        <el-tooltip :content="$t('CHAT.RATING')" placement="top">
                          <el-button class="chat-operation-button" style="display: none;" size="large" :icon="MoreFilled" text />
                        </el-tooltip>
                      </div>
                    </template>
                    <div class="chat-review" v-loading="feedbackLoading">
                      <el-form :model="reviewForm" label-width="75px" size="large">
                        <el-form-item :label="$t('CHAT.QA_CATEGORY')" prop="quesType">
                          <el-select v-model="reviewForm.quesType" :placeholder="$t('COMMON.PLS_SELECT')"
                            :teleported="false">
                            <el-option v-for="item in user.feedbackSelect" :key="item" :label="item" :value="item" />
                          </el-select>
                        </el-form-item>
                        <el-form-item :label="$t('CHAT.QA_RATING')" prop="score">
                          <el-rate v-model="reviewForm.score" clearable />
                        </el-form-item>
                        <el-form-item prop="messages" label-width="0">
                          <el-input v-model="reviewForm.messages" type="textarea" :rows="3" resize="none"
                            :placeholder="$t('COMMON.PLS_INPUT')" />
                        </el-form-item>
                        <el-button class="chat-review-btn" @click="commitFeedback(item.roundIndex, index)">
                          {{ $t('COMMON.SUBMIT') }}
                        </el-button>
                      </el-form>
                    </div>
                  </el-popover>
                </div>
              </div>
            </li>
          </ul>
          <el-empty v-else :description="$t('CHAT.START_CHAT')" />
        </el-scrollbar>
        <div v-if="user.scenes.includes(route.query.scene)" class="chat-view-input">
          <!-- <img src="@/assets/chatgpt.png" width="24" height="24" style="margin-bottom: 8px" /> -->
          <div class="input-layout">
            <!-- <div v-if="showWarning" class="input-warning">
              <div class="input-warning-text">{{ $t('CHAT.INPUT_WARNING') }}</div>
              <div class="input-warning-close" @click="showWarning = false">×</div>
            </div> -->
            <el-input v-model="userInput" type="textarea" :placeholder="$t('CHAT.START_CHAT')"
              :autosize="{ minRows: 2, maxRows: 4 }" resize="none" @keydown.enter="handleInputEnter" />
          </div>
          <el-button class="send-button" size="large" text :loading="sendLoading" @click="sendMessage()">
            <img class="send-img" src="./imgs/send.png" alt="send">
          </el-button>
        </div>
      </div>
      <!-- <el-popover v-if="route.query.scene === 'chat_with_data' || route.query.scene === 'chat_knowledge'"
        ref="promptPopoverRef" placement="top" :width="280" trigger="click"> -->
        <!-- <template #reference>
          <div class="prompt-button">
            <el-tooltip :content="$t('CHAT.CLICK_PROMPT')" placement="top">
              <el-button size="large" :icon="Document" text round />
            </el-tooltip>
          </div>
        </template> -->
        <!-- <div class="prompt-inner">
          <el-tabs v-model="promptTabName" @tab-change="changePromptTab" v-if="isAdmin">
            <el-tab-pane :label="$t('PROMPT.SYSTEM')" name="system">
              <el-scrollbar class="prompt-inner-content">
                <ul v-if="systemPromptList.length" class="prompt-inner-list">
                  <li v-for="item in systemPromptList" :key="item.id" class="prompt-inner-item"
                    :class="{ active: selectSystemPrompt === item.id }" @click="setPrompt(item.id, 'system')">
                    <div>
                      <div class="prompt-inner-item-title">{{ item.promptName }}</div>
                    </div>
                  </li>
                </ul>
                <el-empty v-else />
              </el-scrollbar>
            </el-tab-pane>
            <el-tab-pane :label="$t('PROMPT.APPLICATION')" name="application">
              <el-scrollbar class="prompt-inner-content">
                <ul v-if="appPromptList.length" class="prompt-inner-list">
                  <li v-for="item in appPromptList" :key="item.id" class="prompt-inner-item"
                    :class="{ active: selectAppPrompt === item.id }" @click="setPrompt(item.id, 'app')">
                    <div>
                      <div class="prompt-inner-item-title">{{ item.promptName }}</div>
                    </div>
                  </li>
                </ul>
                <el-empty v-else />
              </el-scrollbar>
            </el-tab-pane>
          </el-tabs>
        </div> -->
      <!-- </el-popover> -->
    </div>
  </div>
</template>

<style lang="less" scoped>
.chat-wrap {
  display: flex;
  flex: 1;
  height: 0;
  border-radius: 12px;
  background-color: #fff;
}

.chat-view {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 0;
  height: 100%;

  // .chat-view-top {
  //   display: flex;
  //   display: none;
  //   align-items: center;
  //   justify-content: center;
  //   padding: 8px 16px;
  //   border-bottom: 1px solid #f3f4f6;
  // }

  .chat-view-layout {
    display: flex;
    flex-direction: column;
    //flex: 1;
    //height: calc(100vh - 49px);
    height: calc(100vh - 100px);

    .chat-content {
      flex: 1;
      padding: 0 30px;

      .chat-content-loading {
        height: 50px;
      }

      .chat-content-list {
        color: #0f172a;
        font-size: 16px;
        line-height: 28px;
        padding-bottom: 32px;

        .welcome-placeholder {
          height: 24px;
        }

        .chat-content-item {
          padding: 24px 16px;
          border-radius: 12px;
          overflow-wrap: break-word;

          .chat-ai-icon {
            width: 36px;
            height: 36px;
            margin-top: 4px;
            margin-right: 10px;
            background-color: #568CF4;
            border-radius: 4px;

            img {
              transform: scale(0.5);
            }
          }
        }

        .chat-content-item.human {
          :deep(.markdown-render p) {
            float: right;
            border-radius: 12px;
            border-top-right-radius: 0;
            background-color: #DDE8FF;
            padding: 4px 12px;
            margin-left: 45px;
            // text-align: right;
            font-size: 12px;
            color: #0A121F;
          }

          .chat-human-icon {
            position: absolute;
            right: -5px;
            top: 1px;
            width: 36px;
            height: 36px;
            border-radius: 4px;
            background: linear-gradient(129deg, #EBF0FF 0%, #DEECFF 49%, #E7E4FF 100%);
            color: #568CF4;
            font-size: 22px;
            text-align: center;
            line-height: 36px;
          }
        }

        .chat-content-item.view {
          // background: #f1f5f9;
          .chat-knowladge-wrap {
            display: inline-flex;
            margin: 2px 40px 0 0;
            border-radius: 12px;
            border-top-left-radius: 0;
            background-color: #F5F8FF;
            font-size: 12px;
            color: #0A121F;
            .markdown-render {
              padding: 4px 12px;
            }
          }
          .chat-operation {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            border-top: 1px solid #e5e7eb;
            margin-top: 16px;
            padding-top: 16px;

            .chat-operation-button {
              font-size: 16px;
              padding: 12px;
            }
          }
        }

        .chat-content-item.welcome {

          // background: #f1f5f9;
          .welcome-wrap {
            padding: 12px;
            background-color: #F5F8FF;
            border-radius: 12px;
            border-top-left-radius: 0;
          }

          .welcome-advices {
            padding-bottom: 8px;

            // .el-tag {
            //   cursor: pointer;
            //   margin: 12px 12px 0 0;
            //   font-size: 14px;
            // }
          }
        }
      }

      .el-empty {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
      }
    }

    .chat-view-input {
      display: flex;
      align-items: flex-end;
      padding: 24px 48px 40px;
      position: relative;

      .input-layout {
        flex: 1;
        margin: 0 8px;

        .input-warning {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-left: 16px;
          background: #eeeeee;
          border-radius: 4px;

          .input-warning-text {
            padding: 8px 0;
            line-height: 1.5;
          }

          .input-warning-close {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            font-size: 30px;
            font-weight: lighter;
            cursor: pointer;
          }
        }

        :deep(.el-textarea__inner) {
          font-size: 16px;
          // padding: 8px 11px;
          height: auto;
        }

        :deep(.el-textarea__inner::placeholder) {
          font-size: 12px;
          color: #ACB2BB;
          line-height: 24px;
        }
      }

      .send-button {
        position: absolute;
        right: 63px;
        bottom: 40px;
        font-size: 18px;
        padding: 0;

        &:hover {
          background-color: transparent;
        }

        .send-img {
          width: 24px;
          height: 24px;
        }
      }

      &::after {
        content: '';
        width: 100%;
        height: 32px;
        position: absolute;
        top: -32px;
        right: 0;
        background-image: linear-gradient(to top, #fff, transparent);
      }
    }
  }

  // .chat-content-list {
  //   .human-item {
  //     display: flex;
  //     justify-content: flex-end;
  //   }
  // }

  .prompt-button {
    // display: none;
    position: absolute;
    bottom: 128px;
    right: 30px;
    width: 40px;
    height: 40px;

    .el-button {
      font-size: 18px;
      width: 40px;
      height: 40px;
      box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    }
  }
}

// .welcome-wrap {
//   background-color: #F5F8FF;
//   border-radius: 12px;
//   border-top-left-radius: 0;
//   padding: 10px;
// }
.agent-welcome {
  .wlc-bold {
    font-weight: bold;
  }

  .welcome-text-agent {
    font-size: 12px;
    position: relative;
    &.table-info {
      padding-left: 12px;
    }
    &.wlc-dot {
      padding-left: 10px;
    }
  }

  .advices-title {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
  }

  .el-tag {
    background-color: #fff;
    border: none;
    color: #000;
    margin-top: 10px;
    font-size: 12px;
    cursor: pointer;
    box-shadow: 0 1px 5px #e2e2e2;
  }
  .btn-more {
    font-size: 12px;
    color: #3C78EC;
    cursor: pointer;
  }
}


.chat-review {
  .chat-review-btn {
    width: 100%;
    margin-top: 12px;
  }
}

.prompt-inner {
  .prompt-inner-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .prompt-inner-label {
      width: 100px;
      font-weight: bold;
      color: #000000;
    }

    .el-select {
      flex: 1;
    }
  }

  .prompt-inner-content {
    margin: 0 -12px;

    :deep(.el-scrollbar__wrap) {
      max-height: 400px;

      .prompt-inner-list {
        padding: 0 12px;

        .prompt-inner-item {
          padding: 12px;
          line-height: 1.5;
          border-bottom: 1px solid rgba(5, 5, 5, 0.06);
          cursor: pointer;

          &.active {
            background: #e7ecf9;
          }

          &:hover {
            background: #e7ecf9;
          }

          .prompt-inner-item-title {
            color: rgba(0, 0, 0, 0.88);
          }

          .prompt-inner-item-desc {
            color: rgba(0, 0, 0, 0.45);
          }
        }

        .prompt-inner-item:last-child {
          border: none;
        }
      }
    }
  }
}

.chat-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // height: 50px;
  padding: 9px 10px;
  border-bottom: 1px solid #E5E7ED;
  font-size: 14px;
  color: #0A121F;
  letter-spacing: 1px;
  min-height: 50px;

  .title-right {
    display: flex;

    .chat-view-top {
      margin-right: 10px;
      span {
        margin-right: 5px;
        font-size: 12px;
      }
    }

    :deep(.el-input__inner::placeholder) {
      font-size: 12px;
    }
    .prompt-wrap {
      display: flex;
      flex-wrap: wrap;
    }
    .prompt-select-wrap {
      margin-right: 10px;
      &:last-child {
        margin-right: 0;
      }
      span {
        margin-right: 4px;
        font-size: 12px;
      }
    }
    .el-select {
      width: 150px;
      margin-top: 1px;
      &.chat-model-select {
        width: 120px;
      }
    }
  }
}

.chat-main {
  flex: 1;
  height: 0;
}

.datasource-select-option {
  margin: 0 7px;

  &.el-select-dropdown__item:hover {
    border-radius: 4px;
    background: #F0F5FF;
  }
}
</style>
