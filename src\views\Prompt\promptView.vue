<script setup>
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/user'
import { Plus } from '@element-plus/icons-vue'
import { ref, computed } from 'vue'
import promptDialog from './promptDialog.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import * as api from './api'

const { locale, t } = useI18n()
const user = useUserStore()

// 提示语类型列表
const promptTypeList = computed(() => {
  return promptType.value === 'system' ? {
    chat_with_data_ludp: `${t('PROMPT.TYPE_DATA')}-LUDP`,
    chat_with_data_synapse: `${t('PROMPT.TYPE_DATA')}-Synapse`,
    chat_with_data_hana: `${t('PROMPT.TYPE_DATA')}-<PERSON>a`,
    chat_with_data_databricks: `${t('PROMPT.TYPE_DATA')}-Databricks`,
    chat_with_data_postgresql: `${t('PROMPT.TYPE_DATA')}-Postgresql`,
    chat_with_data_pbi: `${t('PROMPT.TYPE_DATA')}-PBI`,
    chat_knowledge: t('PROMPT.TYPE_KNOW'),
    chat_insight: t('PROMPT.TYPE_INSIGHT')
  } : {
    chat_with_data: t('PROMPT.TYPE_DATA'),
    chat_knowledge: t('PROMPT.TYPE_KNOW'),
    chat_insight: t('PROMPT.TYPE_INSIGHT')
  }
})
// 提示语场景列表
const applySceneList = ref([])
// 获取提示语查询条件列表
const getFilterList = async () => {
  const res = await api.getFilterList()
  applySceneList.value = res?.applyScene || []
}
// 用户列表
const userList = ref([])
// 获取用户列表
const getUserList = async () => {
  const res = await api.getUserList()
  userList.value = res || []
}

// 搜索条件
const searchObj = {
  promptName: '',
  chatScene: '',
  applyScene: '',
  createdBy: ''
}
const searchForm = ref(JSON.parse(JSON.stringify(searchObj)))
const promptType = ref(user.promptSystem ? 'system' : 'application')
// 表格Loading
const tableLoading = ref(false)
// 表格数据
const tableData = ref([])
// 分页相关
const pageNum = ref(1)
const pageSize = ref(10)
const pageTotal = ref(0)
// 获取提示语列表
const getPromptList = async (page) => {
  tableLoading.value = true
  if (page) {
    pageNum.value = page
  }
  try {
    const res = await api.getPromptList({
      ...searchForm.value,
      promptType: promptType.value,
      pageNum: pageNum.value,
      pageSize: pageSize.value
    })
    tableData.value = res?.list || []
    pageTotal.value = res?.total || 0
    tableLoading.value = false
  } catch (e) {
    tableLoading.value = false
  }
}
// 重置搜索条件
const resetSearch = () => {
  searchForm.value = JSON.parse(JSON.stringify(searchObj))
  getPromptList(1)
}

// 提示语对话框实例
const promptDialogRef = ref()
const promptData = ref({})
// 打开提示语对话框
const openPromptDialog = (data = {}, type = 'edit') => {
  // 对id是0的做特殊处理
  if(Object.keys(data).length) {
    data.id = String(data.id)
  }
  promptData.value = JSON.parse(JSON.stringify(data))
  // view代表查看模式：弹框不可编辑
  if (type === 'view') {
    promptDialogRef.value.dialogView = true
  } else {
    promptDialogRef.value.dialogView = false
  }
  promptDialogRef.value.dialogVisible = true
}

// 删除提示语
const deletePrompt = (id) => {
  ElMessageBox.confirm(
    t('COMMON.DELETE_CONFIRM'),
    t('COMMON.DELETE'),
    { type: 'warning', closeOnClickModal: false }
  ).then(async () => {
    await api.deletePrompt({ id })
    ElMessage.success(t('COMMON.DELETE_SUCCESS'))
    if (pageNum.value !== 1 && tableData.value.length === 1) {
      pageNum.value--
    }
    getPromptList()
  })
}

getUserList()
getFilterList()
getPromptList()
</script>

<template>
  <div class="prompt-view">
    <el-tabs v-if="user.promptSystem" v-model="promptType" @tab-change="resetSearch()">
      <el-tab-pane :label="$t('PROMPT.SYSTEM')" name="system" />
      <el-tab-pane :label="$t('PROMPT.APPLICATION')" name="application" />
    </el-tabs>
    <div class="search-box">
      <el-input
        v-model="searchForm.promptName"
        :placeholder="$t('PROMPT.INPUT_NAME')"
        clearable
      />
      <el-select
        v-model="searchForm.chatScene"
        :placeholder="$t('PROMPT.SELECT_TYPE')"
        filterable
        clearable
      >
        <el-option
          v-for="(val, key) in promptTypeList"
          :key="key"
          :label="val"
          :value="key"
        />
      </el-select>
      <el-select
        v-if="promptType === 'application'"
        v-model="searchForm.applyScene"
        :placeholder="$t('PROMPT.SELECT_SCENE')"
        filterable
        clearable
      >
        <el-option
          v-for="item in applySceneList"
          :key="item.value"
          :label="item[locale === 'en_US' ? 'nameEn' : 'nameCn']"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="searchForm.createdBy"
        :placeholder="$t('PROMPT.SELECT_CREATOR')"
        filterable
        clearable
      >
        <el-option
          v-for="item in userList"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
      <div class="search-btn-left">
        <el-button type="primary" @click="getPromptList(1)">{{ $t('COMMON.SEARCH') }}</el-button>
        <el-button @click="resetSearch()">{{ $t('COMMON.RESET') }}</el-button>
      </div>
      <div class="search-btn-right">
        <el-button type="primary" :icon="Plus" @click="openPromptDialog()">{{ $t('COMMON.CREATE') }}</el-button>
      </div>
    </div>
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      class="table-box"
      header-cell-class-name="table-box-header"
    >
      <el-table-column prop="promptName" :label="$t('PROMPT.PROMPT_NAME')" width="200" />
      <el-table-column prop="chatScene" :label="$t('PROMPT.PROMPT_TYPE')" width="145">
        <template #default="scope">
          {{ promptTypeList[scope.row.chatScene] }}
        </template>
      </el-table-column>
      <el-table-column v-if="promptType === 'application'" prop="applyScene" :label="$t('PROMPT.APP_SCENE')" width="180">
        <template #default="scope">
          {{ applySceneList.find(x => x.value === scope.row.applyScene)?.[locale === 'en_US' ? 'nameEn' : 'nameCn'] }}
        </template>
      </el-table-column>
      <el-table-column v-if="promptType === 'system'" prop="subChatScene" :label="$t('PROMPT.ROLE_DESC')" width="300" />
      <el-table-column prop="content" :label="$t('PROMPT.CONTENT')" min-width="400" />
      <el-table-column prop="createdBy" :label="$t('COMMON.CREATOR')" width="100" />
      <el-table-column prop="updatedTime" :label="$t('COMMON.LAST_UPDATE')" width="165" />
      <el-table-column :label="$t('COMMON.OPERATIONS')" width="130" fixed="right">
        <template #default="scope">
          <div class="table-box-btn">
            <el-button
              link
              @click="openPromptDialog(scope.row, 'view')"
            >
              <el-tooltip :content="$t('PROMPT.VIEW')" placement="top">
                <i class="iconfont icon-chakanxiangqing"></i>
              </el-tooltip>
            </el-button>
            <el-button
              v-if="scope.row.editAuth"
              link
              @click="openPromptDialog(scope.row)"
            >
              <el-tooltip :content="$t('COMMON.EDIT')" placement="top">
                <i class="iconfont icon-edit"></i>
              </el-tooltip>
            </el-button>
            <el-button
              v-if="scope.row.delAuth"
              link
              @click="deletePrompt(scope.row.id)"
            >
              <el-tooltip :content="$t('COMMON.DELETE')" placement="top">
                <i class="iconfont icon-delete"></i>
              </el-tooltip>
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="pagination-box"
      v-model:current-page="pageNum"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="pageTotal"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="getPromptList(1)"
      @current-change="getPromptList()"
    />
    <prompt-dialog
      ref="promptDialogRef"
      :promptType="promptType"
      :promptTypeList="promptTypeList"
      :applySceneList="applySceneList"
      :promptData="promptData"
      @getPromptList="getPromptList"
    />
  </div>
</template>

<style lang="less" scoped>
// @import url('//at.alicdn.com/t/c/font_4728306_ekwp8yphsul.css');
.prompt-view {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  padding: 0 24px;
  background-color: #fff;
  :deep(.el-tabs__header) {
    margin: 0;
  }
  :deep(.el-tabs__nav-wrap::after) {
    height: 0;
  }
}
.table-box-btn i {
  color: #568CF4;
}
</style>
