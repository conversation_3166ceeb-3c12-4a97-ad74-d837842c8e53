<script setup>
import { CirclePlus } from '@element-plus/icons-vue'
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from 'vue-i18n'
import {
  getMenuTableList,
  addMenuConfig,
  editMenuConfig,
  deleteMenuConfig
} from './api'

const {t} = useI18n()
const searchMenuForm = ref({ lv1Name: '' })
const pageNum = ref(1)
const pageSize = ref(10)
const startRow = ref(0)
const endRow = ref(0)
const pageTotal = ref(0)
const tableData = ref([])
// 搜索接口
const getSearchListFn = async () => {
  try {
    const params = {
      lv1Name: searchMenuForm.value.lv1Name,
      pageNum: pageNum.value,
      pageSize: pageSize.value
    }
    const res = await getMenuTableList(params)
    if (Object.prototype.toString.call(res) === '[object Object]') {
      startRow.value = res.startRow
      endRow.value = res.endRow
      pageTotal.value = res.total
      tableData.value = res.list
    }
  } catch (e) {
    console.log(e)
  }
}
const onSearchBtnFn = () => {
  pageNum.value = 1
  getSearchListFn()
}
// 重置
const onResetsBtnFn = () => {
  searchMenuForm.value = { lv1Name: '' }
  pageNum.value = 1
  getSearchListFn()
}
// 分页
const handleCurrentChange = (val) => {
  pageNum.value = val
  getSearchListFn()
}

const dialogFormVisible = ref(false)
const dialogTopTitle = ref(t('MENUS.CREATE_MENU'))
const dialogFormData = ref({
  id: '',
  parentId: 0,
  menuName: '',
  menuPath: '',
  sequence: '',
  isPublic: '1'
})
const rulesDialog = {
  menuName: [{ required: true, message: t('MENUS.RULE_MENU'), trigger: 'blur' }],
  menuPath: [{ required: true, message: t('MENUS.RULE_PATH'), trigger: 'blur' }],
  sequence: [{ required: true, message: t('MENUS.RULE_SORT'), trigger: 'blur' }],
  isPublic: [{ required: true, message: '请选择是否开放权限', trigger: 'change' }]
}
const ruleDialogForm = ref()
// 新增
const handleAddMenuFn = () => {
  dialogTopTitle.value = t('MENUS.CREATE_MENU')
  dialogFormVisible.value = true
}
// 弹层数据回显
const dialogFormDataEchoFn = (row) => {
  dialogFormData.value.id = row.id
  dialogFormData.value.menuName = row.lv1Name
  dialogFormData.value.menuPath = row.menuPath
  dialogFormData.value.sequence = row.sequence
  dialogFormData.value.isPublic = row.isPublic ? '1' : '2'
}
// 修改菜单 OR 复制菜单
const modifyMenuFn = (row, funTxt) => {
  dialogTopTitle.value = funTxt
  dialogFormDataEchoFn(row)
  dialogFormVisible.value = true
}
// 新增接口
const addMenuPortFn = async () => {
  try {
    const newData = {
      parentId: dialogFormData.value.parentId,
      menuName: dialogFormData.value.menuName,
      menuPath: dialogFormData.value.menuPath,
      sequence: dialogFormData.value.sequence,
      isPublic: dialogFormData.value.isPublic === '1' ? true : false
    }
    const res = await addMenuConfig(newData)
    if (res === 'success') {
      dialogFormVisible.value = false
      ElMessage({
        message: t('MENUS.CREATE_SUCCESS'),
        type: 'success'
      })
      getSearchListFn()
    } else {
      ElMessage.error('新增失败')
    }
  } catch (error) {
    console.log(error)
  }
}
// 修改接口
const editMenuPortFn = async () => {
  try {
    const newData = {
      id: dialogFormData.value.id,
      parentId: dialogFormData.value.parentId,
      menuName: dialogFormData.value.menuName,
      menuPath: dialogFormData.value.menuPath,
      sequence: dialogFormData.value.sequence,
      isPublic: dialogFormData.value.isPublic === '1' ? true : false
    }
    const res = await editMenuConfig(newData)
    if (res === 'success') {
      resetForm()
      ElMessage({
        message: t('MENUS.EDIT_SUCCESS'),
        type: 'success'
      })
      getSearchListFn()
    } else {
      ElMessage.error('修改失败')
    }
  } catch (error) {
    console.log(error)
  }
}
// 提交验证
const submitForm = () => {
  ruleDialogForm.value.validate((valid) => {
    if (valid) {
      if (dialogTopTitle.value === t('MENUS.EDIT_MENU')) {
        editMenuPortFn()
      } else {
        addMenuPortFn()
      }
    } else {
      console.log('error submit!!')
      return false
    }
  })
}
// 列表中开关改变事件
const listRowSwitchChange = (row) => {
  dialogFormDataEchoFn(row)
  editMenuPortFn()
}
// 取消
const resetForm = () => {
  dialogFormVisible.value = false
  dialogFormData.value = {
    id: '',
    parentId: 0,
    menuName: '',
    menuPath: '',
    sequence: '',
    isPublic: '1'
  }
  ruleDialogForm.value.resetFields()
}

// 删除接口
const deleteMenuPortFn = async (id) => {
  try {
    const params = { id }
    const res = await deleteMenuConfig(params)
    if (res === 'success') {
      ElMessage({
        message: t('MENUS.DELETE_SUCCESS'),
        type: 'success'
      })
      pageNum.value = 1
      getSearchListFn()
    } else {
      ElMessage.error('删除失败')
    }
  } catch (error) {
    console.log(error)
  }
}
// 删除菜单
const deleteMenuFn = (row) => {
  ElMessageBox.confirm(
    t('MENUS.DELETE_TIPS'),
    '',
    { type: 'warning', closeOnClickModal: false }
  ).then(() => {
    deleteMenuPortFn(row.id)
  }).catch(() => {
    console.log('取消删除')
  })
}

getSearchListFn()
</script>

<template>
  <div class="menu-view">
    <div class="menu-config-box">
      <div class="search-form-box">
        <el-form
          :inline="true"
          :model="searchMenuForm"
          size="small"
          class="demo-form-inline"
          @submit.prevent
        >
          <el-form-item>
            <el-button type="primary" :icon="CirclePlus" @click="handleAddMenuFn">{{$t('MENUS.CREATE')}}</el-button>
          </el-form-item>
          <el-form-item :label="$t('MENUS.MENUNAME')">
            <el-input
              v-model="searchMenuForm.lv1Name"
              :placeholder="$t('COMMON.PLS_INPUT') + ' ' + $t('MENUS.MENUNAME')"
              clearable
              @keyup.enter="onSearchBtnFn"
              @clear="onSearchBtnFn"
              style="width: 175px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onResetsBtnFn">{{$t('COMMON.RESET')}}</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="table-box">
        <el-table
          height="100%"
          :data="tableData"
          :header-cell-style="{ background: '#e7ecf9', height: '55px' }"
          header-cell-class-name="my_table_header"
          border
        >
          <el-table-column prop="lv1Name" :label="$t('MENUS.MENUNAME')" min-width="110"></el-table-column>
          <el-table-column prop="menuPath" :label="$t('MENUS.PATH')" min-width="110"></el-table-column>
          <el-table-column prop="sequence" :label="$t('MENUS.SORT')" min-width="55"></el-table-column>
          <el-table-column :label="$t('MENUS.PERMISSION')" min-width="115">
            <template #default="scope">
              <el-switch v-model="scope.row.isPublic" @change="listRowSwitchChange(scope.row)" />
            </template>
          </el-table-column>
          <el-table-column prop="operator" :label="$t('MENUS.OPERATOR')" min-width="90"></el-table-column>
          <el-table-column prop="updateTime" :label="$t('MENUS.UPDATE')" min-width="170"></el-table-column>
          <el-table-column :label="$t('MENUS.OPERATION')" min-width="135">
            <template #default="scope">
              <span class="table_button" @click="modifyMenuFn(scope.row, $t('MENUS.EDIT_MENU'))">
                <el-tooltip class="item" effect="dark" :content="$t('MENUS.EDIT_MENU')" placement="top">
                  <i class="iconfont icon-icon_jilu" />
                </el-tooltip>
              </span>
              <span class="table_button" @click="modifyMenuFn(scope.row, $t('MENUS.COPY_MENU'))">
                <el-tooltip class="item" effect="dark" :content="$t('MENUS.COPY_MENU')" placement="top">
                  <i class="iconfont icon-icon_jishiben" />
                </el-tooltip>
              </span>
              <span class="table_button" @click="deleteMenuFn(scope.row)">
                <el-tooltip class="item" effect="dark" :content="$t('MENUS.DELETE_MENU')" placement="top">
                  <i class="iconfont icon-icon_delete" />
                </el-tooltip>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination_box">
        <div class="pagination_title">
          <span>{{ startRow }}-{{ endRow }}</span> of
          <span> {{ pageTotal }} </span>
          result
        </div>
        <el-pagination
          layout="prev, pager, next"
          :page-size="pageSize"
          :current-page="pageNum"
          @current-change="handleCurrentChange"
          :total="pageTotal"
        ></el-pagination>
      </div>
      <el-dialog
        :title="dialogTopTitle"
        width="480px"
        v-model="dialogFormVisible"
        @close="resetForm()"
        :close-on-click-modal="false"
      >
        <el-form
          :model="dialogFormData"
          :rules="rulesDialog"
          size="small"
          label-width="100px"
          ref="ruleDialogForm"
        >
          <el-form-item :label="$t('MENUS.MENUNAME')" prop="menuName">
            <el-input v-model="dialogFormData.menuName" :placeholder="$t('COMMON.PLS_INPUT') + ' ' +$t('MENUS.MENUNAME')"></el-input>
          </el-form-item>
          <el-form-item :label="$t('MENUS.PATH')" prop="menuPath">
            <el-input v-model="dialogFormData.menuPath" :placeholder="$t('COMMON.PLS_INPUT') + ' ' +$t('MENUS.PATH')"></el-input>
          </el-form-item>
          <el-form-item :label="$t('MENUS.SORT')" prop="sequence">
            <el-input v-model="dialogFormData.sequence" :placeholder="$t('COMMON.PLS_INPUT') + ' ' +$t('MENUS.SORT')"></el-input>
          </el-form-item>
          <el-form-item :label="$t('MENUS.KFQX')" prop="isPublic">
            <el-select v-model="dialogFormData.isPublic" :placeholder="$t('COMMON.PLS_SELECT') + ' ' +$t('MENUS.PERMISSION')">
              <el-option :label="$t('MENUS.ACTIVE')" value="1" />
              <el-option :label="$t('MENUS.NO_ACTIVE')" value="2" />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button size="small" @click="resetForm()">{{$t('MENUS.CANCEL')}}</el-button>
            <el-button size="small" type="primary" @click="submitForm()">{{$t('MENUS.SUBMIT')}}</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<style lang="less" scoped>
.menu-view {
  display: flex;
  flex-direction: column;
  flex: 1;
  margin: 24px;
  box-shadow: 0 2px 11px 0 rgba(14, 33, 66, 0.1);
  border-radius: 6px;
  overflow: hidden;
}
.menu-config-box {
  margin: 21px 10px 0;
  font-family: PingFangSC-Semibold;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  :deep(.search-form-box) {
    .el-button {
      height: 32px;
    }
    .el-input {
      height: 32px;
    }
    .el-form-item__label {
      font-size: 14px;
      color: #333333;
      height: 32px;
      line-height: 32px;
    }
    .el-select__tags-text {
      max-width: 80px;
    }
  }
  :deep(.table-box) {
    user-select: text;
    flex: auto;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    .el-table {
      .table_button {
        display: inline-block;
        margin-right: 20px;
        cursor: pointer;
      }
      .table-method-link-sty {
        display: inline-block;
        color: rgba(24, 144, 255, 1);
        font-size: 14px;
        line-height: 20px;
        border-bottom: 1px solid rgba(24, 144, 255, 1);
      }
    }
    .my_table_header {
      padding: 8px 0;
      border-right: 1px solid #E5E4E4;
      .cell {
        font-size: 14px;
        color: #333433;
        letter-spacing: 0.88px;
        font-weight: 600;
      }
    }
  }
  .pagination_box {
    margin: 20px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    .pagination_title {
      font-size: 16px;
      span {
        color: #0F91FF;
      }
    }
  }
  :deep(.el-dialog__wrapper) {
    .el-dialog__body {
      padding: 0 20px;
    }
    .el-form-item {
      margin-bottom: 15px;
    }
    .el-dialog__footer {
      text-align: center;
      padding: 10px 20px 20px;
    }
  }
}
</style>
