<script setup>
import {
  Plus,
  Search,
  ChatDotRound,
  Share,
  Top,
  Minus,
  Delete,
  Coin,
  Collection,
  Files,
  Operation,
  House,
  User,
  Menu,
  ChromeFilled
} from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import { ref, computed } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { copyToClipboard } from '@/utils/util'
import { useUserStore } from '@/stores/user'
import { useI18n } from 'vue-i18n'
import * as api from './api'

const router = useRouter()
const route = useRoute()
const user = useUserStore()
const { locale, t } = useI18n()

// 搜索名称
const searchInput = ref('')
// 筛选日期
const searchDate = ref(null)
// 日期快捷选项
const shortcuts = computed(() => [
  {
    text: t('LEFT_BAR.LAST_1_DAY'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)
      return [start, end]
    }
  },
  {
    text: t('LEFT_BAR.LAST_7_DAYS'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: t('LEFT_BAR.LAST_30_DAYS'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  }
])

// 滚动栏实例
const scrollbarRef = ref()
let searchTimer = null
let immediate = true
// 获取聊天列表(函数防抖 延时0.5秒 立即执行)
const setChatList = (pageNum) => {
  clearTimeout(searchTimer)
  if (immediate) {
    user.setChatList(pageNum, {
      userInput: searchInput.value,
      startDate: searchDate.value ? searchDate.value[0] : '',
      endDate: searchDate.value ? searchDate.value[1] : ''
    }).then(() => {
      scrollbarRef.value && scrollbarRef.value.setScrollTop(0)
    })
    immediate = false
    searchTimer = setTimeout(() => {
      immediate = true
    }, 500)
  } else {
    searchTimer = setTimeout(() => {
      user.setChatList(pageNum, {
        userInput: searchInput.value,
        startDate: searchDate.value ? searchDate.value[0] : '',
        endDate: searchDate.value ? searchDate.value[1] : ''
      }).then(() => {
        scrollbarRef.value && scrollbarRef.value.setScrollTop(0)
      })
      immediate = true
    }, 500)
  }
}
const handleCurrentChange = (pageNum) => {
  setChatList(pageNum)
}

// 复制分享链接
const copyUrl = (item) => {
  const url = `${location.origin}/chat?scene=${item.chatMode}&select=${item.selectParam}&id=${item.convUid}`
  copyToClipboard(url)
}

// 置顶
const toTop = async (item) => {
  await api.toTop({
    convUid: item.convUid,
    toTop: item.toTop === '1' ? '0' : '1'
  })
  ElMessage.success(`${item.toTop === '1' ? t('LEFT_BAR.UNPIN') : t('LEFT_BAR.PIN')} ${t('COMMON.SUCCESS')}`)
  setChatList(1)
}

// 删除对话
const deleteChat = (convUid) => {
  ElMessageBox.confirm(
    t('COMMON.DELETE_CONFIRM'),
    t('COMMON.DELETE'),
    { type: 'warning', closeOnClickModal: false }
  ).then(async () => {
    await api.deleteChat({ convUid })
    ElMessage.success(t('COMMON.DELETE_SUCCESS'))
    let pageNum = user.chatPage.pageNum
    if (pageNum !== 1 && user.chatList.length === 1) {
      pageNum--
    }
    setChatList(pageNum)
    if (route.query.id === convUid) {
      router.push('/')
    }
  })
}

// 下拉按钮
const handleCommand = (command, item) => {
  switch (command) {
    case 'share':
      copyUrl(item)
      break
    case 'toTop':
      toTop(item)
      break
    case 'delete':
      deleteChat(item.convUid)
      break
    default:
      break
  }
}

// 左下全部菜单
const allMenuList = computed(() => [
  { icon: ChatDotRound, text: t('MENUS.PROMPT'), path: '/prompt' },
  { icon: Coin, text: t('MENUS.DATA_SOURCE'), path: '/database' },
  { icon: Collection, text: t('MENUS.KNOWLEDGE'), path: '/knowledge' },
  { icon: Files, text: t('MENUS.PLUGIN'), path: '/plugin' },
  { icon: Operation, text: t('MENUS.APP'), path: '/application' },
  { icon: House, text: t('MENUS.USER'), path: '/user' },
  { icon: User, text: t('MENUS.ROLE'), path: '/role' },
  { icon: Menu, text: t('MENUS.MENU'), path: '/menu' },
])
// 左下实际菜单
const menuList = computed(() => {
  return allMenuList.value.filter(menu => {
    return user.routes.find(item => menu.path.indexOf(item.menuPath) > -1)
  })
})

// 变更语言
const setLocale = () => {
  const lang = localStorage.getItem('lang') === 'en_US' ? 'zh_CN' : 'en_US'
  localStorage.setItem('lang', lang)
  locale.value = lang
}

setChatList()
</script>

<template>
  <div class="left-bar">
    <div class="top-btn">
      <el-button
        type="primary"
        size="large"
        :icon="Plus"
        @click="router.push('/')"
      >{{ $t('LEFT_BAR.NEW_CHAT') }}</el-button>
      <el-input
        v-model="searchInput"
        :placeholder="$t('LEFT_BAR.SEARCH_HISTORY')"
        :prefix-icon="Search"
        clearable
        @input="setChatList(1)"
      />
      <el-date-picker
        v-model="searchDate"
        type="daterange"
        unlink-panels
        :range-separator="$t('LEFT_BAR.TO')"
        :start-placeholder="$t('LEFT_BAR.START_DATE')"
        :end-placeholder="$t('LEFT_BAR.END_DATE')"
        value-format="YYYY-MM-DD"
        :shortcuts="shortcuts"
        @change="setChatList(1)"
      />
    </div>
    <el-scrollbar ref="scrollbarRef" class="middle-chat" v-loading="user.chatLoading">
      <ul class="chat-list">
        <li
          v-for="item in user.chatList"
          :key="item.convUid"
          class="chat-item"
          :class="{
            isTop: item.toTop === '1',
            active: route.query.id === item.convUid
          }"
          @click="router.push({
            path: '/chat',
            query: {
              scene: item.chatMode,
              select: item.selectParam,
              id: item.convUid
            }
          })"
        >
          <ChatDotRound class="chat-item-icon" />
          <div class="chat-item-title">{{ item.userInput }}</div>
          <div @click.stop>
            <el-dropdown trigger="click" @command="(command) => handleCommand(command, item)">
              <MoreFilled class="chat-item-more" />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :icon="Share" command="share">
                    {{ $t('LEFT_BAR.SHARE') }}
                  </el-dropdown-item>
                  <el-dropdown-item :icon="item.toTop === '1' ? Minus : Top" command="toTop">
                    {{ item.toTop === '1' ? $t('LEFT_BAR.UNPIN') : $t('LEFT_BAR.PIN') }}
                  </el-dropdown-item>
                  <el-dropdown-item :icon="Delete" command="delete">
                    {{ $t('COMMON.DELETE') }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </li>
      </ul>
    </el-scrollbar>
    <el-pagination
      :current-page="user.chatPage.pageNum"
      :page-size="user.chatPage.pageSize"
      :total="user.chatPage.pageTotal"
      :pager-count="5"
      layout="prev, pager, next"
      @current-change="handleCurrentChange"
    />
    <div v-if="menuList.length" class="bottom-menu">
      <ul class="menu-list">
        <li
          v-for="item in menuList"
          :key="item.path"
          class="menu-item"
          :class="{ active: route.path === item.path }"
          @click="router.push(item.path)"
        >
          <component :is="item.icon" class="menu-item-icon" />
          <span>{{ item.text }}</span>
        </li>
      </ul>
    </div>
    <div class="language-button">
      <el-tooltip :content="$t('LEFT_BAR.LANGUAGE')" placement="top">
        <el-button
          size="large"
          :icon="ChromeFilled"
          text
          round
          @click="setLocale()"
        />
      </el-tooltip>
    </div>
  </div>
</template>

<style lang="less" scoped>
.left-bar {
  position: relative;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e5e7eb;
  //height: 100%;
  height: calc(100vh - 50px);
  .top-btn {
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
    .el-button {
      width: 100%;
      height: 44px;
      font-size: 16px;
      margin-bottom: 8px;
    }
    :deep(.el-date-editor) {
      width: 100%;
      margin-top: 8px;
    }
  }
  .middle-chat {
    flex: 1;
    .chat-list {
      padding: 0 8px;
      .chat-item {
        display: flex;
        align-items: center;
        margin: 8px 0;
        padding: 0 8px;
        height: 32px;
        border-radius: 4px;
        cursor: pointer;
        transition-property: color,background-color,border-color,text-decoration-color,fill,stroke;
        transition-timing-function: cubic-bezier(.4,0,.2,1);
        transition-duration: .15s;
        .chat-item-icon {
          width: 16px;
          height: 16px;
        }
        .chat-item-title {
          flex: 1;
          margin: 0 8px;
          white-space: nowrap;
          overflow: hidden;
          line-height: 1.5;
        }
        .chat-item-more {
          width: 16px;
          height: 16px;
          opacity: 0;
        }
      }
      .chat-item.isTop {
        background-color: #f5f7fa;
      }
      .chat-item:hover {
        background-color: #e7ecf9;
        .chat-item-more {
          opacity: 1;
        }
      }
      .chat-item.active {
        background-color: #e7ecf9;
      }
    }
  }
  :deep(.el-pagination) {
    border-top: 1px solid #e5e7eb;
    padding: 2px 0;
    justify-content: center;
    .el-pager li {
      min-width: 28px;
    }
  }
  .bottom-menu {
    border-top: 1px solid #e5e7eb;
    padding: 8px 0;
    .menu-list {
      padding: 0 8px;
      .menu-item {
        display: flex;
        align-items: center;
        margin: 8px 0;
        padding: 0 8px;
        height: 32px;
        border-radius: 4px;
        cursor: pointer;
        transition-property: color,background-color,border-color,text-decoration-color,fill,stroke;
        transition-timing-function: cubic-bezier(.4,0,.2,1);
        transition-duration: .15s;
        .menu-item-icon {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }
      }
      .menu-item:hover {
        background-color: #e7ecf9;
      }
      .menu-item.active {
        background-color: #e7ecf9;
      }
    }
  }
  .language-button {
    position: absolute;
    bottom: 25px;
    right: 25px;
    width: 40px;
    height: 40px;
    .el-button {
      font-size: 18px;
      width: 40px;
      height: 40px;
      box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    }
  }
}
</style>
