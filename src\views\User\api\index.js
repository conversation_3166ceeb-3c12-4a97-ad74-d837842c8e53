import request from '@/utils/axios'

const url = '/user/'

function getUserList(data) {
  return request({
    url: url + 'list',
    method: 'post',
    data
  })
}
function getItcodeInfo(data) {
  return request({
    url: url + 'itcode/info?itcode=' + data,
    method: 'get'
  })
}

function getDepartmentList() {
  return request({
    url: url + 'department/list',
    method: 'get'
  })
}

function getUnitList() {
  return request({
    url: 'bu/listAll',
    method: 'get'
  })
}

function getGroupList() {
  return request({
    url: 'group/listAll',
    method: 'get'
  })
}

function batchAddUser(data) {
  return request({
    url: url + 'batchAdd',
    method: 'post',
    data
  })
}

function editUser(data) {
  return request({
    url: url + 'edit',
    method: 'post',
    data
  })
}

function deleteUser(id) {
  return request({
    url: `${url}delete/${id}`,
    method: 'get'
  })
}

function batchDeleteUser(data) {
  return request({
    url: url + 'batchDel',
    method: 'post',
    data
  })
}

function exportUser(data) {
  return request({
    url: url + 'export',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    noInlayerData: true
  })
}

function batchAddRole(data) {
  return request({
    url: url + 'batchAddRole',
    method: 'post',
    data
  })
}

function batchDelRole(data) {
  return request({
    url: url + 'batchDelRole',
    method: 'post',
    data
  })
}

export {
  getUserList,
  getItcodeInfo,
  getDepartmentList,
  getUnitList,
  getGroupList,
  batchAddUser,
  editUser,
  deleteUser,
  batchDeleteUser,
  exportUser,
  batchAddRole,
  batchDelRole
}
