<script setup>
import { ref, nextTick, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import chartRender from './chartRender.vue'
import markdownRender from '@/components/MarkdownRender/markdownRender.vue'
import * as api from './index'
const route = useRoute()

let queryParams = route.query
let mctChatData = ref({})
const getSphereData = async () => {
  let id = queryParams.convId || "8e4c3776-b9eb-4117-b96b-ca8a3805728f"
  let res = await api.getSphereData(id)
  console.log(res, '新数据结果')
  if (!Array.isArray(res.data)) {
    res.data = []
  }
  mctChatData.value = res
}


// 滚动栏实例
const scrollbarRef = ref()
// 消息列表实例
const messageListRef = ref()
// 消息列表滚动至指定位置(不传值则滚动至底部)
const scrollTo = async (num) => {
  // 需要通过nextTick等待DOM更新完成
  await nextTick()
  if (messageListRef.value) {
    // 需要等待echarts全部加载完毕
    setTimeout(() => {
      const max = messageListRef.value.clientHeight
      scrollbarRef.value.setScrollTop(isNaN(num) ? max : max - num)
    })
  }
}
// 滚动事件
const handleScroll = (obj) => {
  if (obj.scrollTop === 0 && (pageNum * pageSize < pageTotal)) {
    pageNum++
    getMessageList()
  }
}

// 历史记录分页相关
let pageNum = 1
let pageSize = 6
let pageTotal = 0
const viewLoading = ref(false)
const chatLoading = ref(false)
// 消息列表
const messageList = ref([])
// 获取消息列表
const getMessageList = async () => {
  viewLoading.value = pageNum === 1
  chatLoading.value = pageNum !== 1
  const oldH = messageListRef.value && messageListRef.value.clientHeight
  try {
    const res = await api.getMessageList({
      convUid: route.query.id,
      pageNum,
      pageSize
    })
    viewLoading.value = false
    chatLoading.value = false
    if (res?.list?.length) {
      const list = res.list.filter(item => {
        if (item.type === 'human' || item.type === 'view') {
          // 判断返回的消息是否为JSON字符串，是则以data形式赋值，否则不做处理
          try {
            const data = JSON.parse(item.messageDetail)
            if (!Array.isArray(data.data)) {
              data.data = []
            }
            // 返回的message是否为空来做报错消息提示展示
            // if (!data.message) {
            //   item.messageDetail = data.aitext
            // } else {
            //   item.messageDetail = data.message
            // }
            // Jira MTYLH-2891 去掉aitext的显示
            if (data.message) {
              item.messageDetail = data.message
            }
            // item.messageDetail = data.aitext
            item.chartData = data
            return true
          } catch (e) {
            return true
          }
        } else {
          return false
        }
      })
      // selectPrompt.value = list[list.length - 1]?.promptId // 历史最后一次prompt记录赋值给当前
      pageTotal = res.total || 0
      messageList.value = [
        ...route.query.scene === 'chat_mct_demo' && (pageNum * pageSize >= pageTotal) ? [{ type: 'welcome', roundIndex: -1 }] : [],
        ...list,
        ...messageList.value
      ]
      if (pageNum === 1) {
        scrollTo()
      } else {
        scrollTo(oldH)
      }
    }
  } catch (e) {
    viewLoading.value = false
    chatLoading.value = false
  }
}


const getLocalStorageId = () => localStorage.getItem('currentId')

onMounted(() => {
  if (queryParams.convId) {
    getSphereData()
  }
})

</script>

<template>
  <div class="chat-wrap">
    <div class="chat-view" v-loading="viewLoading">
      <div class="chat-view-layout">

        <el-scrollbar ref="scrollbarRef" class="chat-content" @scroll="handleScroll">
          <div v-if="chatLoading" class="chat-content-loading" v-loading="chatLoading" />
          <ul ref="messageListRef" class="chat-content-list">
            <li class="'chat-content-item' view">
              <div style="display: flex;position:relative;">
                <div class="welcome-wrap" style="width: calc(100% - 40px);">
                  <markdown-render v-if="mctChatData.message" :context="mctChatData.message" />
                  <chart-render v-if="mctChatData.data"
                                :chartData="mctChatData"
                                :sessionid="queryParams.convId || getLocalStorageId()"
                                :requestId="mctChatData.requestId"/>
                  <el-empty v-else :description="$t('CHAT.START_CHAT')" />
                </div>
              </div>
            </li>
          </ul>
        </el-scrollbar>

      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.chat-wrap {
  display: flex;
  flex: 1;
  height: 0;
  border-radius: 12px;
  background-color: #fff;
}

.chat-view {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 0;
  height: 100%;

  .chat-view-top {
    display: flex;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-bottom: 1px solid #f3f4f6;
  }

  .chat-view-layout {
    display: flex;
    flex-direction: column;
    //flex: 1;
    //height: calc(100vh - 49px);
    // height: calc(100vh - 100px);

    .chat-content {
      flex: 1;
      // padding: 0 30px;

      .chat-content-loading {
        height: 50px;
      }

      .chat-content-list {
        color: #0f172a;
        font-size: 16px;
        line-height: 28px;
        padding-bottom: 32px;

        .welcome-placeholder {
          height: 24px;
        }

        .chat-content-item {
          padding: 24px 16px;
          border-radius: 12px;
          overflow-wrap: break-word;

          .chat-ai-icon {
            width: 36px;
            height: 36px;
            margin-top: 4px;
            margin-right: 10px;
            background-color: #568CF4;
            border-radius: 4px;

            img {
              transform: scale(0.5);
            }
          }
        }

        .chat-content-item.human {
          :deep(.markdown-render p) {
            float: right;
            border-radius: 12px;
            border-top-right-radius: 0;
            background-color: #DDE8FF;
            padding: 4px 12px;
            // text-align: right;
            font-size: 12px;
            color: #0A121F;
          }

          .chat-human-icon {
            position: absolute;
            right: -5px;
            top: 1px;
            width: 36px;
            height: 36px;
            border-radius: 4px;
            background: linear-gradient(129deg, #EBF0FF 0%, #DEECFF 49%, #E7E4FF 100%);
            color: #568CF4;
            font-size: 22px;
            text-align: center;
            line-height: 36px;
          }
        }

        .chat-content-item.view {
          // background: #f1f5f9;

          .chat-operation {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            border-top: 1px solid #e5e7eb;
            margin-top: 16px;
            padding-top: 16px;

            .chat-operation-button {
              font-size: 16px;
              padding: 12px;
            }
          }
        }

        .chat-content-item.welcome {

          // background: #f1f5f9;
          .welcome-wrap {
            padding: 24px 16px;
            background-color: #F5F8FF;
            border-radius: 12px;
            border-top-left-radius: 0;
          }

          .welcome-advices {
            display: flex;
            flex-wrap: wrap;
            padding-bottom: 8px;

            .el-tag {
              cursor: pointer;
              margin: 12px 12px 0 0;
              font-size: 14px;
            }
          }
        }
      }

      .el-empty {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
      }
    }

    .chat-view-input {
      display: flex;
      align-items: flex-end;
      padding: 24px 48px 40px;
      position: relative;

      .input-layout {
        flex: 1;
        margin: 0 8px;

        .input-warning {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-left: 16px;
          background: #eeeeee;
          border-radius: 4px;

          .input-warning-text {
            padding: 8px 0;
            line-height: 1.5;
          }

          .input-warning-close {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            font-size: 30px;
            font-weight: lighter;
            cursor: pointer;
          }
        }

        :deep(.el-textarea__inner) {
          font-size: 16px;
          // padding: 8px 11px;
          height: auto;
        }

        :deep(.el-textarea__inner::placeholder) {
          font-size: 12px;
          color: #ACB2BB;
          line-height: 24px;
        }
      }

      .send-button {
        position: absolute;
        right: 63px;
        bottom: 40px;
        font-size: 18px;
        padding: 0;

        &:hover {
          background-color: transparent;
        }

        .send-img {
          width: 24px;
          height: 24px;
        }
      }

      &::after {
        content: '';
        width: 100%;
        height: 32px;
        position: absolute;
        top: -32px;
        right: 0;
        background-image: linear-gradient(to top, #fff, transparent);
      }
    }
  }

  // .chat-content-list {
  //   .human-item {
  //     display: flex;
  //     justify-content: flex-end;
  //   }
  // }

  .prompt-button {
    // display: none;
    position: absolute;
    bottom: 128px;
    right: 30px;
    width: 40px;
    height: 40px;

    .el-button {
      font-size: 18px;
      width: 40px;
      height: 40px;
      box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    }
  }
}

// .welcome-wrap {
//   background-color: #F5F8FF;
//   border-radius: 12px;
//   border-top-left-radius: 0;
//   padding: 10px;
// }
.agent-welcome {
  .welcome-text-agent {
    font-size: 12px;
  }

  .advices-title {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    font-weight: bold;

    .btn-refresh {
      color: #3C78EC;
    }
  }

  .agent-tag {
    background-color: #fff;
    border: none;
    color: #4B556A;
    margin-top: 10px;
    margin-right: 10px;
    cursor: pointer;
  }
}


.chat-review {
  .chat-review-btn {
    width: 100%;
    margin-top: 12px;
  }
}

.prompt-inner {
  .prompt-inner-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .prompt-inner-label {
      width: 100px;
      font-weight: bold;
      color: #000000;
    }

    .el-select {
      flex: 1;
    }
  }

  .prompt-inner-content {
    margin: 0 -12px;

    :deep(.el-scrollbar__wrap) {
      max-height: 400px;

      .prompt-inner-list {
        padding: 0 12px;

        .prompt-inner-item {
          padding: 12px;
          line-height: 1.5;
          border-bottom: 1px solid rgba(5, 5, 5, 0.06);
          cursor: pointer;

          &.active {
            background: #e7ecf9;
          }

          &:hover {
            background: #e7ecf9;
          }

          .prompt-inner-item-title {
            color: rgba(0, 0, 0, 0.88);
          }

          .prompt-inner-item-desc {
            color: rgba(0, 0, 0, 0.45);
          }
        }

        .prompt-inner-item:last-child {
          border: none;
        }
      }
    }
  }
}

.chat-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // height: 50px;
  padding: 9px 32px;
  border-bottom: 1px solid #E5E7ED;
  font-size: 14px;
  color: #0A121F;
  letter-spacing: 1px;
  min-height: 50px;

  .title-right {
    display: flex;

    &>div {
      margin-right: 10px
    }

    :deep(.el-input__inner::placeholder) {
      font-size: 12px;
    }
  }
}

.chat-main {
  flex: 1;
  height: 0;
}

.datasource-select-option {
  margin: 0 7px;

  &.el-select-dropdown__item:hover {
    border-radius: 4px;
    background: #F0F5FF;
  }
}
</style>
