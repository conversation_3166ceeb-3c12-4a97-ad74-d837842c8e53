import axios from '@/utils/axios'

const baseUrl = '/user/'

const getVCode = () => {
  return axios({
    url: baseUrl + 'getAuthCode.do',
    method: 'post'
  })
}
const login = (params) => {
  return axios({
    url: baseUrl + 'login.do',
    method: 'post',
    data: params
  })
}

const getMenus = () => {
  return axios({
    url: baseUrl + 'menus.do',
    method: 'get'
  })
}

// 查询用户是否有权限页面权限（interior：internal-内部/external-外部）和 数据中心的四个权限【无权限DATACENTER_TEMP为空数组】
const getUserRights = () => {
  return axios({
    url: baseUrl + 'info',
    method: 'get'
  })
}

export {
  getVCode,
  login,
  getMenus,
  getUserRights
}
