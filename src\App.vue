<script setup>
import topNav from './components/TopNav/index.vue'
import leftNav from './components/LeftNav/index.vue'
import { useRoute, RouterView } from 'vue-router'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import en from 'element-plus/dist/locale/en.mjs'
import { useI18n } from 'vue-i18n'
const { locale } = useI18n()
const route = useRoute()
let whiteList = ['/login', '/loggedTips', '/aihub', '/fifa', '/sphere', '/pbiEmbed', '/reportRecommend', '/database'] // 全屏页白名单【登录页，接入robbie的aihub,sphere，fifa足球】
</script>

<template>
  <div v-if="whiteList.includes(route.path)" class="app">
    <router-view />
  </div>
  <div v-else class="app">
    <topNav></topNav>
    <div class="main-layout">
      <leftNav></leftNav>
      <div class="content-wrap">
        <el-config-provider :locale="locale === 'en_US' ? en : zhCn">
          <router-view :key="route.fullPath" />
        </el-config-provider>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.main-layout {
  display: flex;
  height: calc(100vh - 50px);
  overflow: hidden;
  .content-wrap {
    flex: 1;
    overflow: hidden; // 很重要，不能省略，搭配flex:1使用，保证布局准确不超出
    //width: 500px;
    background: linear-gradient( 120deg, #F0F4F9 0%, #E7EDFD 100%);
  }
}
</style>
