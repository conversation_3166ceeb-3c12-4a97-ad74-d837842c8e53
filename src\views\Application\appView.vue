<script setup>
import { useI18n } from 'vue-i18n'
import { Plus } from '@element-plus/icons-vue'
import { ref } from 'vue'
import appDialog from './appDialog.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import * as api from './api'

const { locale, t } = useI18n()
// 应用类型列表
const appTypeList = [{
  label: 'NATIVE_APP',
  value: 'native'
}, {
  label: 'MULTI_AGENT_APP',
  value: 'mutli-agent'
}]
// 应用查询条件列表
const filterObj = {
  applyScene: [],
  bu: [],
  bg: [],
  kpi: {}
}
const filterList = ref(JSON.parse(JSON.stringify(filterObj)))
// 获取应用查询条件列表
const getFilterList = async () => {
  const res = await api.getFilterList()
  filterList.value = res || JSON.parse(JSON.stringify(filterObj))
}

// 搜索条件
const searchObj = {
  sceneName: '',
  sceneType: '',
  appType: ''
  // bu: '',
  // bg: '',
  // kpiCategory: '',
  // kpi: []
}
const searchForm = ref(JSON.parse(JSON.stringify(searchObj)))
// 表格Loading
const tableLoading = ref(false)
// 表格数据
const tableData = ref([])
// 分页相关
const pageNum = ref(1)
const pageSize = ref(10)
const pageTotal = ref(0)
// 获取应用列表
const getAppList = async (page) => {
  tableLoading.value = true
  if (page) {
    pageNum.value = page
  }
  try {
    const res = await api.getAppList({
      ...searchForm.value,
      pageNum: pageNum.value,
      pageSize: pageSize.value
    })
    tableData.value = res?.list || []
    pageTotal.value = res?.total || 0
    tableLoading.value = false
  } catch (e) {
    tableLoading.value = false
  }
}
// 重置搜索条件
const resetSearch = () => {
  searchForm.value = JSON.parse(JSON.stringify(searchObj))
  getAppList(1)
}

// 应用对话框实例
const appDialogRef = ref()
const appData = ref({})
const onlyRead = ref(false)
// 打开应用对话框
const openAppDialog = (data = {}, isRead = false) => {
  appData.value = JSON.parse(JSON.stringify(data))
  onlyRead.value = isRead
  
  if(Object.keys(data).length === 0) {
    appDialogRef.value.activeType = ''
    appDialogRef.value.dialogApplicationType = true
  }else {
    if(appData.value.appType === 'native') {
      appDialogRef.value.activeType = 1
    }
    if(appData.value.appType === 'mutli-agent') {
      appDialogRef.value.activeType = 2
    }
    appDialogRef.value.dialogVisible = true
  }
}

// 发布/下线应用
const publishApp = async (id, publishStatus) => {
  const str = publishStatus === '1' ? t('PLUGIN.PUBLISH') : t('PLUGIN.OFFLINE')
  ElMessageBox.confirm(
    `${t('COMMON.CONFIRM')}${str}?`,
    str,
    { type: 'warning', closeOnClickModal: false }
  ).then(async () => {
    await api.publishApp({ id, publishStatus })
    ElMessage.success(`${str} ${t('COMMON.SUCCESS')}`)
    getAppList()
  })
}

// 删除应用
const deleteApp = (id) => {
  ElMessageBox.confirm(
    t('COMMON.DELETE_CONFIRM'),
    t('COMMON.DELETE'),
    { type: 'warning', closeOnClickModal: false }
  ).then(async () => {
    await api.deleteApp({ id })
    ElMessage.success(t('COMMON.DELETE_SUCCESS'))
    if (pageNum.value !== 1 && tableData.value.length === 1) {
      pageNum.value--
    }
    getAppList()
  })
}

getFilterList()
getAppList()
</script>

<template>
  <div class="app-view">
    <div class="search-box">
      <el-input
        v-model="searchForm.sceneName"
        :placeholder="$t('COMMON.PLS_INPUT') + ' ' + $t('APP.INPUT_APPNAME')"
        clearable
      />
      <el-select
        v-model="searchForm.appType"
        :placeholder="$t('COMMON.PLS_SELECT') + ' ' + $t('APP.APP_TYPE')"
        filterable
        clearable
      >
        <el-option
          v-for="item in appTypeList"
          :key="item.value"
          :label="$t(`APP.${item.label}`)"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="searchForm.sceneType"
        :placeholder="$t('COMMON.PLS_SELECT') + ' ' + $t('APP.SELECT_SCENE')"
        filterable
        clearable
      >
        <el-option
          v-for="item in filterList.applyScene"
          :key="item.value"
          :label="item[locale === 'en_US' ? 'nameEn' : 'nameCn']"
          :value="item.value"
        />
      </el-select>
      <!-- <el-select
        v-model="searchForm.bu"
        :placeholder="$t('COMMON.PLS_SELECT') + ' BU'"
        filterable
        clearable
      >
        <el-option
          v-for="item in filterList.bu"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select> -->
      <!-- <el-select
        v-model="searchForm.bg"
        :placeholder="$t('COMMON.PLS_SELECT') + ' BG'"
        filterable
        clearable
      >
        <el-option
          v-for="item in filterList.bg"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select> -->
      <!-- <el-select
        v-model="searchForm.kpiCategory"
        :placeholder="$t('COMMON.PLS_SELECT') + ' ' + $t('APP.SELECT_KPITYPE')"
        filterable
        clearable
        @change="searchForm.kpi = []"
      >
        <el-option
          v-for="(val, key) in filterList.kpi"
          :key="key"
          :label="key"
          :value="key"
        />
      </el-select> -->
      <!-- <el-select
        v-model="searchForm.kpi"
        :placeholder="$t('COMMON.PLS_SELECT') + ' KPI'"
        filterable
        multiple
        collapse-tags
        collapse-tags-tooltip
        clearable
      >
        <el-option
          v-for="item in filterList.kpi[searchForm.kpiCategory]"
          :key="item.value"
          :label="item[locale === 'en_US' ? 'nameEn' : 'nameCn']"
          :value="item.value"
        />
      </el-select> -->
      <div class="search-btn-left">
        <el-button type="primary" @click="getAppList(1)">{{ $t('COMMON.SEARCH') }}</el-button>
        <el-button @click="resetSearch()">{{ $t('COMMON.RESET') }}</el-button>
      </div>
      <div class="search-btn-right">
        <el-button type="primary" :icon="Plus" @click="openAppDialog()">{{ $t('COMMON.CREATE') }}</el-button>
      </div>
    </div>
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      class="table-box"
      header-cell-class-name="table-box-header"
    >
      <el-table-column prop="sceneName" :label="$t('APP.INPUT_APPNAME')" min-width="150" />
      <el-table-column prop="appType" :label="$t('APP.APP_TYPE')" min-width="100">
        <template #default="scope">
          {{ scope.row.appType === 'mutli-agent' ? $t('APP.MULTI_AGENT_APP') : $t('APP.NATIVE_APP') }}
        </template>
      </el-table-column>
      <el-table-column prop="sceneType" :label="$t('APP.SELECT_SCENE')" width="180">
        <template #default="scope">
          {{ filterList.applyScene.find(x => x.value === scope.row.sceneType)?.[locale === 'en_US' ? 'nameEn' : 'nameCn'] }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="bu" label="BU" width="100" />
      <el-table-column prop="bg" label="BG" width="100" />
      <el-table-column prop="kpiCategory" :label="$t('APP.SELECT_KPITYPE')" width="150" />
      <el-table-column prop="kpi" label="KPI" min-width="200">
        <template #default="scope">
          {{ scope.row.kpi?.map(x => filterList.kpi[scope.row.kpiCategory]?.find(y => y.value === x)?.[locale === 'en_US' ? 'nameEn' : 'nameCn']).join(',') }}
        </template>
      </el-table-column> -->
      <el-table-column prop="comment" :label="$t('APP.DESC')" min-width="200" />
      <el-table-column prop="publishStatus" :label="$t('APP.STATE')" width="130">
        <template #default="scope">
          <div>
            <el-tag style="border-radius:12px" v-if="scope.row.publishStatus === '0'" type="info">{{$t('APP.UNPUBLISHED')}}</el-tag>
            <el-tag style="border-radius:12px" v-if="scope.row.publishStatus === '1'" type="success">{{$t('APP.PUBLISHED')}}</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createdBy" :label="$t('COMMON.CREATOR')" width="100" />
      <el-table-column prop="updatedTime" :label="$t('COMMON.LAST_UPDATE')" width="165" />
      <el-table-column :label="$t('COMMON.OPERATIONS')" width="200" fixed="right">
        <template #default="scope">
          <div class="table-box-btn">
            <el-button
              v-if="scope.row.publishStatus === '1'"
              link
              @click="openAppDialog(scope.row, true)"
            >
              <el-tooltip :content="$t('COMMON.DETAIL')" placement="top">
                <i class="iconfont icon-chakanxiangqing"></i>
              </el-tooltip>
            </el-button>
            <el-button
              v-if="scope.row.publishStatus === '0' && scope.row.editAuth"
              link
              @click="openAppDialog(scope.row)"
            >
              <el-tooltip :content="$t('COMMON.EDIT')" placement="top">
                <i class="iconfont icon-edit"></i>
              </el-tooltip>
            </el-button>
            <el-button
              v-if="scope.row.publishStatus === '0' && scope.row.publishAuth"
              link
              @click="publishApp(scope.row.id, '1')"
            >
              <el-tooltip :content="$t('PLUGIN.PUBLISH')" placement="top">
                <i class="iconfont icon-fabu"></i>
              </el-tooltip>
            </el-button>
            <el-button
              v-if="scope.row.publishStatus === '1' && scope.row.publishAuth"
              link
              @click="publishApp(scope.row.id, '0')"
            >
              <el-tooltip :content="$t('PLUGIN.OFFLINE')" placement="top">
                <i class="iconfont icon-quxiaofabu"></i>
              </el-tooltip>
            </el-button>
            <el-button
              v-if="scope.row.publishStatus === '0' && scope.row.delAuth"
              link
              @click="deleteApp(scope.row.id)"
            >
              <el-tooltip :content="$t('COMMON.DELETE')" placement="top">
                <i class="iconfont icon-delete"></i>
              </el-tooltip>
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="pagination-box"
      v-model:current-page="pageNum"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="pageTotal"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="getAppList(1)"
      @current-change="getAppList()"
    />
    <app-dialog
      ref="appDialogRef"
      :filterList="filterList"
      :appData="appData"
      :onlyRead="onlyRead"
      @getAppList="getAppList"
    />
  </div>
</template>

<style lang="less" scoped>
.app-view {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  padding: 0 24px;
  background: #fff;
  .el-tag {
    font-size: 14px;
  }
}
.table-box-btn i {
  color: #568CF4;
}
</style>
