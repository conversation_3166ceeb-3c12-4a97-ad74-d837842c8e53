import request from '@/utils/axios'

const url = '/apiConfig/'

function getPluginList(data) {
  return request({
    url: url + 'list',
    method: 'post',
    data
  })
}

function getPluginDetail(data) {
  return request({
    url: url + 'detail',
    method: 'post',
    data
  })
}

function verifyPlugin(data) {
  return request({
    url: url + 'varification',
    method: 'post',
    data
  })
}

function addPlugin(data) {
  return request({
    url: url + 'add',
    method: 'post',
    data
  })
}

function updatePlugin(data) {
  return request({
    url: url + 'update',
    method: 'post',
    data
  })
}

function deletePlugin(data) {
  return request({
    url: url + 'delete',
    method: 'post',
    data
  })
}

function publishPlugin(data) {
  return request({
    url: url + 'publishOrOffline',
    method: 'post',
    data
  })
}

export {
  getPluginList,
  getPluginDetail,
  verifyPlugin,
  addPlugin,
  updatePlugin,
  deletePlugin,
  publishPlugin
}
