import { ElMessage } from 'element-plus'
import i18n from '@/lang'
import { getUsername } from './auth'

const { t } = i18n.global

// 拷贝至剪贴板
export const copyToClipboard = (text) => {
  const dom = document.createElement('textarea')
  dom.setAttribute('readonly', 'readonly')
  dom.value = text
  document.body.appendChild(dom)
  dom.select()
  const result = document.execCommand('copy')
  ElMessage({
    message: `${t('COMMON.COPY')} ${result ? t('COMMON.SUCCESS') : t('COMMON.FAIL')}`,
    type: result ? 'success' : 'error'
  })
  document.body.removeChild(dom)
}

// 驼峰转下划线
export const camelToUnderline = (camelStr) => {
  return camelStr.replace(/[A-Z]/g, (s) => {
    return ' ' + s.toLowerCase()
  }).trim().replaceAll(' ', '_')
}

// 获取图片静态资源URL
export const getImageUrl = (path) => {
  return new URL(path, import.meta.url).href
}

// LUDP/Synapse/Databricks登录跳转
export const loginRedirect = (msg, env) => {
  // env 环境：tst/prd
  env = import.meta.env.VITE_APP_MODE !== 'PROD' ? 'tst' : 'prd'
  const itcode = getUsername()
  const redirectUrl = encodeURIComponent(location.href)
  switch (msg) {
    case 'Need to log in to ludp':
      location.href = `${import.meta.env.VITE_APP_BASE_API_URL}/ludpAdmin/redirect?itcode=${itcode}&redirectUrl=${redirectUrl}&env=${env}`
      break
    case 'Need to log in to synapse':
      location.href = `${import.meta.env.VITE_APP_BASE_API_URL}/aad/redirect?itcode=${itcode}&type=synapse&redirectUrl=${redirectUrl}`
      break
    case 'Need to log in to databricks':
      location.href = `${import.meta.env.VITE_APP_BASE_API_URL}/aad/redirect?itcode=${itcode}&type=databricks&redirectUrl=${redirectUrl}`
      break
    default:
      break
  }
}
//判断数组对象的所有字段是否都不是null
export const isAllNotNull = (arr) => {
  if (!Array.isArray(arr) || arr.length === 0) {
    return false
  }
  return !arr.every(item => Object.values(item).every(value => value === null || value === undefined))
}
