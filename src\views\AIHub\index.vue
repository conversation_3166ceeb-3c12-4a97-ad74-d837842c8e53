<script setup>
import { onMounted } from 'vue';
import './AIHubSDK.umd.min'



onMounted(() => {


  console.log(AIHubSDK.version(), '版本')

  AIHubSDK.init({
    id: "#aiHub",
    showIcon: true,
    appId: "fc0543acea924cde988dd8b0a48c73f8",
    platform: "web",
    channel: "Chat_data_poc",
    lang: "cn",
    username: "leixw1",
    signnature: "c76c4556e749d5053ee3eb3c1fea437cadd30092",
    timeStamp: "1713860632000", // 后端提供：与签名一致13
    randomStr: "1dg23fdjdjdkdJDu" // 后端提供：与签名一致16
  }).then((res) => {
    console.log(res, '初始化成功')
    // AIHubSDK.sendWelcomeIndex('1')
    // AIHubSDK.sendAgentName({
    //   AgentName: '', // 为空默认调用聊天逻辑
    // })
    // AIHubSDK.sendQuestion("问题") // 调用对话接口方法。根据传入的问题进行回答，模拟手动输入问题

  }).catch((err) => {
    console.log(err, '初始化失败')
  })

});
</script>

<template>
  <div>
    <h1>AIHub</h1>
    <div style="height: 500px; border: 1px solid #f33">
      <div id="aiHub"></div>
    </div>
  </div>
</template>

<style scoped lang="less">

</style>
