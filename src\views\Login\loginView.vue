<script setup>
import { User, Lock } from '@element-plus/icons-vue'
import * as api from './api/index'
import { setToken, setUsername } from '@/utils/auth'
import { ref } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

// 登录表单数据
const userInfo = ref({
  username: '',
  password: '',
  vcode: '',
  codeSrc: '',
  key: '',
  token: ''
})
const loginRules = {
  username: [{ required: true, message: '请输入itcode', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  vcode: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
}
// 登录表单实例
const loginFormRef = ref()
// 防抖函数
const proxyClick = (function () {
  let timer = null
  return function (FUNC, time = 500) {
    let self = this, args = arguments
    if (timer) {
      return
    }
    timer = setTimeout(function () {
      FUNC.apply(self, args)
      timer = null
    }, time)
  }
})()
// 登录表单验证
const verify = () => {
  loginFormRef.value.validate((valid) => {
    if (valid) {
      proxyClick(login)
    } else {
      return false
    }
  })
}
// 登录按钮loading
const loginLoading = ref(false)
// 登录请求
const login = async () => {
  loginLoading.value = true
  const params = {
    username: userInfo.value.username.trim(),
    password: userInfo.value.password.trim(),
    authCode: userInfo.value.vcode.trim(),
    key: userInfo.value.key
  }
  try {
    const res = await api.login(params)
    loginLoading.value = false
    if (res === 'refresh') {
      getVCode()
    } else {
      userInfo.value.token = res.Authorization
      setUserInfo()
      // 跳转重定向地址，如果没有，则跳转至首页
      const redirectPath = route.query.redirect?.split('?redirectUrl=')[1]
      location.href = redirectPath ? decodeURIComponent(redirectPath) : '/'
    }
  } catch (e) {
    loginLoading.value = false
  }
}
// 获取验证码
const getVCode = async () => {
  const res = await api.getVCode()
  userInfo.value.codeSrc = res.srcCode
  userInfo.value.key = res.key
}
// 保存用户信息
const setUserInfo = () => {
  setToken(userInfo.value.token)
  setUsername(userInfo.value.username)
}
// 按回车键登录
const isCaps = (e) => {
  if (e.keyCode === 13 && loginLoading.value === false) {
    proxyClick(login)
  }
}

getVCode()
</script>

<template>
  <div class="login">
    <div class="wrap">
      <h2>用户登录</h2>
      <div class="userinfo-wrap">
        <el-form :model="userInfo" :rules="loginRules" ref="loginFormRef">
          <el-form-item prop="username">
            <el-input
              placeholder="请输入itcode"
              @keyup="isCaps"
              v-model="userInfo.username"
              :prefix-icon="User"
            ></el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              show-password
              @keyup="isCaps"
              placeholder="请输入密码"
              v-model="userInfo.password"
              :prefix-icon="Lock"
            ></el-input>
          </el-form-item>
          <el-form-item prop="vcode">
            <div class="verification-code">
              <el-input
                placeholder="验证码"
                @keyup="isCaps"
                v-model="userInfo.vcode">
              </el-input>
              <div class="code-canvas" @click="getVCode">
                <img :src="userInfo.codeSrc" alt="验证码">
                <i class="el-icon-refresh-right"></i>
              </div>
            </div>
          </el-form-item>
        </el-form>
        <el-button type="primary" class="login-btn" @click="verify()" :loading="loginLoading">登录</el-button>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.login {
  width: 100vw;
  height: 100vh;
  background-image: url('./img/login-bg.png');
  background-size: cover;
  h1 {
    text-align: center;
  }
  .wrap {
    width: 614px;
    height: 484px;
    position: absolute;
    top: 46%;
    left: 50%;
    margin-top: -190px;
    margin-left: -307px;
    background-image: url('./img/content.png');
    h2 {
      text-align: center;
      font-family: Helvetica;
      font-size: 24px;
      color: #4EB7FF;
      font-weight: 400;
      margin-top: 80px;
    }
    .login-btn {
      width: 300px;
    }
  }
  .userinfo-wrap {
    margin-top: 20px;
    width: 300px;
    margin-left: 150px;
    .verification-code {
      display: flex;
      .el-input {
        max-width: 200px;
      }
      .code-canvas {
        flex: 1;
        display: flex;
        align-items: center;
        cursor: pointer;
        img {
          width: 68px;
          margin: 0 7px;
        }
        i {
          color: #409eff;
          font-weight: bold;
        }
        &:hover {
          transform: scale(1.2);
        }
      }
    }
  }
}
</style>
