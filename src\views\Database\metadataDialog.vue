<script setup>
import { useI18n } from 'vue-i18n'
import { ref, computed } from 'vue'
import { ElLoading, ElMessage } from 'element-plus'
import * as api from './api'

const { t, locale } = useI18n()


// 声明props
const props = defineProps({
  dbId: Number,
  dbData: Object
})

// 接收传递的方法
const emit = defineEmits(['getDbList'])

// 是否显示对话框
const dialogVisible = ref(false)
// 已进行的步骤
const stepActive = ref(0)

// 语义类型列表
// const semanticTypeList = ['MEASURE', 'DIMENSION', 'TIME', 'OTHER']
const semanticTypeList = [{
  name: 'MEASURE',
  value: 'Metric'
}, {
  name: 'DIMENSION',
  value: 'Dimension'
}, {
  name: 'TIME',
  value: 'Timestamp'
}, {
  name: 'OTHER',
  value: 'Other'
}]

// metadata表单数据
const metadataObj = {
  id: null,
  tableList: [],
  isAi: '0',
  remark: ''
}
const metadataForm = ref(JSON.parse(JSON.stringify(metadataObj)))
const selectTable = ref('')
// 搜索条件
const searchObj = {
  columnName: '',
  columnType: '',
  isVisible: ''
}
const searchForm = ref(JSON.parse(JSON.stringify(searchObj)))
// 分页相关
const pageNum = ref(1)
const pageSize = ref(10)
const pageTotal = ref(0)
// 字段列表
const fieldList = computed(() => metadataForm.value.tableList?.[selectTable.value]?.fieldList || [])
// 表格数据
const tableData = ref([])
// 全可见开关
let visibleSwitch = computed({
  get() {
    return !tableData.value.some(x => !x.isVisible)
  },
  set() {
    const visible = tableData.value.some(x => !x.isVisible)
    tableData.value.forEach(item => {
      item.isVisible = visible
    })
    if (visible && fieldList.value.filter(x => x.isVisible).length > 50) {
      ElMessage.warning('当前已选字段超过50，这将影响查询准确度。建议控制字段数在50以下。')
    }
  }
})

//主键
const primaryKey = ref([])
//外键
const foreignKey = ref([])
primaryKey.value = ['opportunity_id', 'account_id']
foreignKey.value = ['opportunity.account_id = account.account_id']

const keyVisible = ref(false)
const handleEditKey = () => {
  keyVisible.value = true
}
const handleCloseKey = () => {
  keyVisible.value = false
  dialogForeignList.value = []
}
const confirmDialogKey = () => {

}
//数据表主键
const dialogPrimaryKey = ref([])
const dialogPrimaryList = ref([
  { label: 'opportunity_id', value: 'opportunity_id' },
  { label: 'account_id', value: 'account_id' }
])
//数据表外键
const dialogForeignList = ref([])
const currentTableArr = ref(['opportunity', 'account'])
const anotherTableArr = ref(['opportunity1', 'account1'])

const handleClickAdd = () => {
  dialogForeignList.value.push({
    currentTable: '',
    currentTableList: currentTableArr.value.map(item => ({ label: item, value: item })),
    anotherTable: '',
    anotherTableList: anotherTableArr.value.map(item => ({ label: item, value: item }))
  })
}

const removeForeignItem = (item, index) => {
  dialogForeignList.value.splice(index, 1)
  // 如果删除的外键是当前选中的，清空当前选中
  if (item.currentTable === dialogPrimaryKey.value) {
    dialogPrimaryKey.value = []
  }
}
//枚举值管理
const enumVisible = ref(false)
const isEnum = ref('')
const enumTableData = ref([])
// enumTableData.value = [
//   {
//     value: 'NB',
//     supplimentalComment: ''
//   }
// ]
const handleEnum = (scope) => {
  enumVisible.value = true
  isEnum.value = scope.row.isEnum ? '1' : '2'
}
const closeEnumDialog = () => {
  enumVisible.value = false
  isEnum.value = ''
  enumTableData.value = []
}
const confirmEnumDialog = () => {

}

// 筛选字段列表
const getTableData = (page) => {
  if (page) {
    pageNum.value = page
  }
  const _list = fieldList.value.filter(item => {
    const { columnName, semanticType, isVisible } = searchForm.value
    if (columnName) {
      const regex = new RegExp(columnName, 'gi')
      if (!regex.test(item.columnName)) {
        return false
      }
    }
    if (semanticType && item.semanticType !== semanticType) {
      return false
    }
    if (isVisible !== '' && item.isVisible !== isVisible) {
      return false
    }
    return true
  })

  // 复选框 null值处理
  _list.forEach(item => {
    item.isAddFieldMapping = item.isAddFieldMapping === null ? false : item.isAddFieldMapping
    item.isSyncEnumValue = item.isSyncEnumValue === null ? false : item.isSyncEnumValue
    return item
  })
  pageTotal.value = _list.length
  const startIndex = (pageNum.value - 1) * pageSize.value
  const endIndex = pageNum.value * pageSize.value
  tableData.value = _list.slice(startIndex, endIndex)
}
// 重置搜索条件
const resetSearch = () => {
  searchForm.value = JSON.parse(JSON.stringify(searchObj))
  getTableData(1)
}

// 同步
const updateTableData = async () => {
  const loading = ElLoading.service({ target: '.el-dialog' })
  try {
    // metadataForm.value.tableList[selectTable.value].fieldList = JSON.parse(JSON.stringify(fieldList.value))
    const res = await api.updateTableData(props.dbId)
    if (res) {
      await getMetadata(props.dbId)
      resetSearch()
      // 弹消息提示同步成功
      ElMessage.success(t('DATA_SOURCE.UpdateSuccess'))
    }
    loading.close()
  } catch (e) {
    ElMessage.error(t('DATA_SOURCE.UpdateFaile'))
    loading.close()
  }
}
// pbi字段匹配
const pbiFieldMap = async() => {
  const loading = ElLoading.service({ target: '.el-dialog' })
  try {
    // metadataForm.value.tableList[selectTable.value].fieldList = JSON.parse(JSON.stringify(fieldList.value))
    const params = {
      connectId: metadataForm.value.id,
      type: props.dbData.dbType,
      tableName: metadataForm.value.tableList[selectTable.value].tableName,
      fieldList: metadataForm.value.tableList[selectTable.value].fieldList
    }
    const res = await api.getFieldMapping(params)
    if (res.length) {
      metadataForm.value.tableList[selectTable.value].fieldList = res
      getTableData(1)
    }
    loading.close()
  } catch (e) {
    ElMessage.error("字段同步异常")
    loading.close()
  }
}

// 获取metadata数据
const getMetadata = async (id) => {
  const loading = ElLoading.service({ target: '.el-dialog' })
  try {
    const res = await api.getMetadata({ id })
    metadataForm.value = res || JSON.parse(JSON.stringify(metadataObj))
    selectTable.value = metadataForm.value.tableList.length ? 0 : ''
    getTableData()
    loading.close()
  } catch (e) {
    loading.close()
  }
}
// 填充数据
const resetData = (isOpen) => {
  stepActive.value = 0
  selectTable.value = ''
  if (isOpen) {
    getMetadata(props.dbId)
  } else {
    metadataForm.value = JSON.parse(JSON.stringify(metadataObj))
    selectTable.value = ''
    searchForm.value = JSON.parse(JSON.stringify(searchObj))
    pageNum.value = 1
    pageTotal.value = 0
    tableData.value = []
  }
}
// 全可见/全不可见
const setVisible = () => {
  const visible = tableData.value.some(x => !x.isVisible)
  tableData.value.forEach(item => {
    item.isVisible = visible
  })
  if (visible && fieldList.value.filter(x => x.isVisible).length > 50) {
    ElMessage.warning('当前已选字段超过50，这将影响查询准确度。建议控制字段数在50以下。')
  }
}
// 可见开关
const visibleChange = (val) => {
  if (val && fieldList.value.filter(x => x.isVisible).length > 50) {
    ElMessage.warning('当前已选字段超过50，这将影响查询准确度。建议控制字段数在50以下。')
  }
}

// metadata下一步
const nextStep = () => {
  stepActive.value++
}
// metadata上一步
const prevStep = () => {
  stepActive.value--
}
// 保存Metadata
const saveMetadata = async () => {
  const loading = ElLoading.service({ target: '.el-dialog' })
  try {
    await api.saveMetadata({
      ...metadataForm.value,
      dbType: props.dbData.dbType
    })
    loading.close()
    dialogVisible.value = false
    emit('getDbList')
  } catch (e) {
    loading.close()
  }
}
// 提交表单
const submitForm = () => {
  saveMetadata()
}

// get ai remark
const getAIRemark = async() => {
  const loading = ElLoading.service({ target: '.el-dialog' })
  try {
    // metadataForm.value.remark = 'test'
    const res = await api.getReportDescribe({
      tableList: metadataForm.value.tableList
    })
    if(res && res === "知识库中提供的内容不足以回答此问题") {
      ElMessage.error(res)
    }
    if(res && res !== "知识库中提供的内容不足以回答此问题") {
      metadataForm.value.remark = res
    }
    loading.close()
  } catch(error) {
    loading.close()
    console.log('获取AI描述错误')
  }
}
// 暴露属性和方法
defineExpose({ dialogVisible })
</script>

<template>
  <div class="metadata-dialog">
    <el-dialog class="setting-page-dialog-wrap" v-model="dialogVisible" :title="$t('DATA_SOURCE.METADATA_MGT')"
      width="90%" :close-on-click-modal="false" destroy-on-close @open="resetData(true)" @closed="resetData(false)">
      <el-scrollbar class="metadata-dialog-inner">
        <el-form :model="metadataForm" :label-width="locale === 'en_US' ? '180px' : '110px'" :inline="true">
          <div v-if="stepActive === 0">
            <el-text tag="b" style="margin-bottom: 18px">{{ $t('DATA_SOURCE.TABLE_INFO') }}</el-text>
            <el-form-item :label="$t('DATA_SOURCE.TABLE_NAME')" class="w-45">
              <el-select v-model="selectTable" :placeholder="$t('COMMON.PLS_SELECT')" @change="resetSearch()">
                <el-option v-for="(item, index) in metadataForm.tableList" :key="index" :label="item.tableName"
                  :value="index" />
              </el-select>
            </el-form-item>
            <el-form-item v-if="metadataForm.tableList[selectTable] && dbData.dbType !== 'pbi'" class="w-45">
              <template #label>
                <span>{{ $t('DATA_SOURCE.TABLE_BUSINESS') }}</span>
                <el-tooltip :content="$t('DATA_SOURCE.DEMO_TIPS2')" placement="top" popper-class="metadata-tip">
                  <QuestionFilled class="question-icon" />
                </el-tooltip>
              </template>
              <el-input v-model="metadataForm.tableList[selectTable].tableBusiName"
                :placeholder="$t('COMMON.PLS_INPUT')" />
            </el-form-item>
            <el-form-item v-if="metadataForm.tableList[selectTable] && dbData.dbType !== 'pbi'" style="width: calc(90% + 35px);">
              <template #label>
                <span>{{ $t('DATA_SOURCE.TABLE_DESC') }}</span>
                <el-tooltip :content="$t('DATA_SOURCE.DEMO_TIPS')" placement="top" popper-class="metadata-tip">
                  <QuestionFilled class="question-icon" />
                </el-tooltip>
              </template>
              <el-input v-model="metadataForm.tableList[selectTable].tableDescription" type="textarea"
                :rows="4" resize="none" :placeholder="$t('COMMON.PLS_INPUT')" />
            </el-form-item>
            <!-- <div class="table-relation" v-if="dbData.dbType !== 'pbi'">
              <div class="table-relation-header" v-if="dbData.dbType !== 'pbi'">
                <el-text tag="b">表关系</el-text>
              </div>
              <div class="primaryKey key-box">
                <span>主键</span>
                <div>
                  <div class="key-item" v-for="(item, index) in primaryKey" :key="index" type="info">{{ item }}</div>
                </div>
              </div>
              <div class="foreignKey key-box">
                <span>外键</span>
                <div>
                  <div class="key-item" v-for="(item, index) in foreignKey" :key="index" type="info">{{ item }}</div>
                </div>
              </div>
              <el-button type="primary" size="small" @click="handleEditKey">编辑</el-button>
            </div> -->
            <el-text tag="b">{{ $t('DATA_SOURCE.Attribute_Info') }}</el-text>
            <div class="search-box">
              <el-input v-model="searchForm.columnName" :placeholder="$t('DATA_SOURCE.TABLE_ENTER')" clearable />
              <el-select v-model="searchForm.semanticType"
                :placeholder="$t('COMMON.PLS_SELECT') + $t('DATA_SOURCE.TYPE')" filterable clearable>
                <el-option v-for="item in semanticTypeList" :key="item.value" :label="$t(`DATA_SOURCE.${item.name}`)"
                  :value="item.value" />
              </el-select>
              <el-select v-model="searchForm.isVisible"
                :placeholder="$t('COMMON.PLS_SELECT') + $t('DATA_SOURCE.VIEIBILITY')" filterable clearable>
                <el-option :label="$t('COMMON.YES')" :value="true" />
                <el-option :label="$t('COMMON.NO')" :value="false" />
              </el-select>
              <div class="search-btn-left">
                <el-button type="primary" @click="getTableData(1)">{{ $t('COMMON.SEARCH') }}</el-button>
                <el-button @click="resetSearch()">{{ $t('COMMON.RESET') }}</el-button>
                <el-button type="success" @click="updateTableData" v-if="dbData.dbType !== 'pbi'">{{ $t('DATA_SOURCE.UPDATE') }}</el-button>
                <el-button type="success" @click="pbiFieldMap" v-if="dbData.dbType === 'pbi'">字段匹配</el-button>
              </div>
              <div class="search-btn-right">
                <span class="switch-label">{{ $t('DATA_SOURCE.VISIBLESWITCH') }}</span>
                <el-switch v-model="visibleSwitch" />
                <!--                <el-button @click="setVisible()">{{ tableData.some(x => !x.isVisible) ? '全可见' : '全不可见' }}</el-button>-->
                <span>{{ fieldList.filter(x => x.isVisible).length }} / {{ fieldList.length }}</span>
              </div>
            </div>
            <el-table :data="tableData" header-cell-class-name="table-box-header">
              <el-table-column prop="columnName" :label="$t('DATA_SOURCE.COLUMN_NAME')" width="150" />
              <el-table-column prop="attributeName" :label="$t('DATA_SOURCE.ATTRIBUTE_NAME')" v-if="dbData.dbType !== 'pbi'" width="150" />
              <el-table-column prop="columnType" :label="$t('DATA_SOURCE.COLUMN_TYPE')" width="120" />
              <el-table-column prop="columnDescription" :label="$t('DATA_SOURCE.DESCRIPTION')" v-if="dbData.dbType !== 'pbi'" width="150" />
              <el-table-column prop="columnComment" min-width="200">
                <template #header>
                  <span>{{ $t('DATA_SOURCE.BUSI_NAME') }}</span>
                  <el-tooltip :content="$t('DATA_SOURCE.DEMO_TIPS2')" placement="top" popper-class="metadata-tip">
                    <QuestionFilled class="question-icon" />
                  </el-tooltip>
                </template>
                <template #default="scope">
                  <el-input v-model="scope.row.columnComment" :placeholder="$t('COMMON.PLS_INPUT')" />
                </template>
              </el-table-column>
              <el-table-column prop="supplimentalComment" :label="$t('DATA_SOURCE.ADD_INFO')" min-width="200">
                <template #default="scope">
                  <el-input v-model="scope.row.supplimentalComment"
                    :placeholder="$t('COMMON.PLS_INPUT')" />
                </template>
              </el-table-column>
              <!-- <el-table-column v-if="dbData.dbType !== 'pbi'" prop="isAi" label="是否枚举" width="100"></el-table-column>
              <el-table-column v-if="dbData.dbType !== 'pbi'" class-name="enumSetting" prop="isPrimaryKey" label="枚举值管理" width="100">
                <template #default="scope">
                  <el-icon @click="handleEnum(scope)"><Setting /></el-icon>
                </template>
              </el-table-column> -->
              <el-table-column prop="semanticType" :label="$t('DATA_SOURCE.LANGUAGE_TYPE')" width="160">
                <template #default="scope">
                  <el-select v-model="scope.row.semanticType" :placeholder="$t('COMMON.PLS_SELECT')">
                    <el-option v-for="item in semanticTypeList" :key="item.value"
                      :label="$t(`DATA_SOURCE.${item.name}`)" :value="item.value" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="加入字段映射库" v-if="dbData.dbType === 'pbi'">
                <template #default="scope">
                  <el-checkbox v-model="scope.row.isAddFieldMapping" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="自动同步枚举值" v-if="dbData.dbType === 'pbi'">
                <template #default="scope">
                  <el-checkbox v-model="scope.row.isSyncEnumValue" size="small" />
                </template>
              </el-table-column>
              <el-table-column prop="isVisible" width="120">
                <template #header>
                  <span>{{ $t('DATA_SOURCE.COLUMN_VIEIBILITY') }}</span>
                  <el-tooltip :content="$t('DATA_SOURCE.DEMO_TIPS3')" placement="top" popper-class="metadata-tip">
                    <QuestionFilled class="question-icon" />
                  </el-tooltip>
                </template>
                <template #default="scope">
                  <div style="text-align: center">
                    <el-switch v-model="scope.row.isVisible" @change="visibleChange" />
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination class="pagination-box" v-model:current-page="pageNum" :page-size="pageSize"
              :total="pageTotal" layout="total, prev, pager, next, jumper" @current-change="getTableData()" />
          </div>
          <div v-if="stepActive === 1">
            <el-form-item :class="locale === 'en_US' ? 'ai-recom' : ''" :label="$t('DATA_SOURCE.AI_RECOM')">
              <el-button type="primary" @click="getAIRemark">{{ $t('DATA_SOURCE.GETBUTTON') }}</el-button>
            </el-form-item>
            <el-form-item :label="$t('DATA_SOURCE.DESCRIPTION')" class="w-600">
              <el-input v-model="metadataForm.remark" type="textarea" :rows="6" resize="none"
                :placeholder="$t('COMMON.PLS_INPUT')" />
            </el-form-item>
          </div>
        </el-form>
      </el-scrollbar>
      <template #footer>
        <el-button v-if="stepActive === 0" type="primary" @click="nextStep()">{{ $t('COMMON.NEXT') }}</el-button>
        <el-button v-if="stepActive === 1" @click="prevStep()">{{ $t('COMMON.BACK') }}</el-button>
        <el-button v-if="stepActive === 1" type="primary" @click="submitForm()">{{ $t('COMMON.SUBMIT') }}</el-button>
      </template>
    </el-dialog>
    <el-dialog v-model="keyVisible" title="Edit" width="50%">
      <el-scrollbar class="key-dialog-inner">
        <div class="dialog-inner-box">
          <div class="label-box">
            <span class="label">数据表主键</span>
            <span class="label" v-if="dialogForeignList.length !== 0">数据表外键</span>
          </div>
          <div>
            <div class="primary-box">
              <div class="primary-select-box">
                <el-select v-model="dialogPrimaryKey" multiple collapse-tags
                  :placeholder="$t('COMMON.PLS_SELECT')" clearable>
                  <el-option v-for="item in dialogPrimaryList" :key="item.value" :label="item.value"
                    :value="item.value" />
                </el-select>
              </div>
            </div>
            <div class="foreign-box" v-if="dialogForeignList.length !== 0">
              <div class="foreign-item-box">
                <div class="foreign-item" v-for="(item, index) in dialogForeignList" :key="item.currentTable">
                  <div class="current-select-box">
                    <el-select v-model="item.currentTable" :placeholder="$t('COMMON.PLS_SELECT')">
                      <el-option v-for="option in item.currentTableList" :key="option.value" :label="option.label" :disabled="dialogForeignList.some(x => x.currentTable === option.value && x !== item)"
                        :value="option.value" />
                    </el-select>
                    <span> = </span>
                  </div>
                  <div class="another-select-box">
                    <el-select v-model="item.anotherTable" :placeholder="$t('COMMON.PLS_SELECT')">
                      <el-option v-for="option in item.anotherTableList" :key="option.value" :label="option.label" :disabled="dialogForeignList.some(x => x.anotherTable === option.value && x !== item)"
                        :value="option.value" />
                    </el-select>
                    <el-icon @click="removeForeignItem(item, index)"><Remove /></el-icon>
                  </div>
                </div>
              </div>
            </div>
            <div class="add-box">
              <div @click="handleClickAdd">+ 添加</div>
            </div>
          </div>
        </div>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseKey">取消</el-button>
          <el-button type="primary" @click="confirmDialogKey">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="enumVisible" title="枚举值管理" width="600px">
      <div class="enum-dialog-inner">
        <div class="label-box">
          <span class="label">是否枚举：</span>
          <span class="label" v-show="isEnum === '1'">枚举值：</span>
        </div>
        <div class="enum-box">
          <div class="radio-box">
            <el-radio-group v-model="isEnum">
              <el-radio label="1">是</el-radio>
              <el-radio label="2">否</el-radio>
            </el-radio-group>
          </div>
          <div v-show="isEnum === '1'" class="button-box">
            <el-button>同步数据库中该列枚举候选值</el-button>
          </div>
          <div class="table-box" v-show="isEnum === '1'">
            <el-table :data="enumTableData" style="width: 100%">
              <el-table-column prop="value" label="枚举值" width="150"></el-table-column>
              <el-table-column prop="supplimentalComment" :label="$t('DATA_SOURCE.ADD_INFO')">
                <template #default="scope">
                  <el-input v-model="scope.row.supplimentalComment" clearable
                    :placeholder="$t('COMMON.PLS_INPUT')" />
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeEnumDialog">取消</el-button>
          <el-button type="primary" @click="confirmEnumDialog">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.metadata-dialog {
  :deep(.el-dialog) {
    margin-top: 10vh;

    .el-dialog__body {
      padding: 0;

      .metadata-dialog-inner>.el-scrollbar__wrap {
        max-height: calc(80vh - 116px);

        .el-form {
          padding: 18px 20px 0;
          &>div .el-text {
            font-size: 13px;
            color: #3A4A69;
          }
        }
      }

      .question-icon {
        width: 14px;
        height: 14px;
        margin: 9px 0 0 4px;
      }

      .ai-recom {
        .el-form-item__label {
          text-align: right;
          line-height: 1.5;
        }
      }

      .el-form-item {
        &.w-600 {
          width: 600px;
        }
        &.w-45 {
          width: 45%;
        }

        .el-select {
          width: 100%;
        }

        // .el-textarea__inner {
        //   width: 100%;
        // }
      }

      b {
        display: block;
        color: #303133;
      }

      .search-box {
        padding: 18px 0 8px;

        .el-input,
        .el-select {
          width: 220px;
        }

        .search-btn-right {
          align-items: center;

          .el-button,
          .el-switch {
            margin-right: 8px;
          }

          .switch-label {
            margin-right: 8px;
          }
        }
      }
    }
  }
  .table-relation-header {
    display: flex;
    margin: 10px 0;
    margin-right: 20px;
    .el-text {
      margin-right: 20px;
    }
  }
  .table-relation {
    display: flex;
    margin: 10px 0;
    align-items: baseline;
    // justify-content: space-between;
    // margin-left: 60px;
    span {
      margin-right: 20px;
    }
    .key-box {
      display: flex;
      margin-right: 20px;
    }
    .primaryKey {
      min-width: 20%;
    }
    .foreignKey {
      min-width: 20%;
      margin-right: 50px;
    }
    .key-item {
      margin-bottom: 4px;
      color: #a8abb2;
    }
  }
  .dialog-inner-box {
    display: flex;
    padding: 20px
  }
  .label-box {
    display: flex;
    flex-direction: column;
    margin-right: 20px;
    .label {
      line-height: 30px;
      margin-bottom: 20px
    }
  }
  .primary-select-box {
    width: 450px;
    .el-select {
      width: 100%;
    }
  }
  .current-select-box {
    margin-right: 4px;
  }
  .primary-box {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }
  .foreign-box {
    display: flex;
    .el-icon {
      margin-left: 6px;
      cursor: pointer;
    }
  }
  .foreign-item {
    display: flex;
    margin-bottom: 20px;
  }
  .add-box {
    text-align: center;
    line-height: 30px;
    cursor: pointer;
    border: 1px dashed #dcdfe6;
  }
  .enum-dialog-inner {
    padding: 20px;
    display: flex;
  }
  .enum-box {
    flex: 1
  }
  .radio-box, .button-box {
    margin-bottom: 20px;
  }
}
</style>
<style lang="less">
.metadata-tip {
  max-width: 260px;
}
</style>
