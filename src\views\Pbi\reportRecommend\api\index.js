import request from '@/utils/axios'
const getReportInfo = (data) => {
  return request({
    // url: `china/report/getKnowledgeContent?knowledgeName=${data.knowledgeName}&userInput=${data.userInput}`,
    url: `china/report/getKnowledgeContent`,
    method: 'post',
    data: data,
    // headers: {'Content-Type':'application/x-www-form-urlencoded'}
  })
}

const getThinkContent = (data) => {
  return request({
    url: `china/report/reportSummary`,
    method: 'post',
    data: data
  })
}
// http://localhost:8083/pbi/reportsAccessSort
const getReportsAccessSort = (data) => {
  return request({
    url: `pbi/reportsAccessSort`,
    method: 'post',
    // headers: {
    //   'Authorization': 'YLBU5P6NAXX3NINFHGJRXLKSUUB64HAZMS7JA5DXDLBIDSLT6LSN7SSIGQVK4L3RKPBEOBCEWTV2Q'
    // },
    data: data
  })
}

// 点击报表日志保存
// http://localhost:8083/pbi/saveClickReportLog
const saveClickReportLog = (data) => {
  return request({
    url: `pbi/saveClickReportLog`,
    method: 'post',
    data: data
  })
}

// 验证Azure登陆
const hasToken = (data) => {
  return request({
    url: `pbi/getPbiAccessToken?username=${data.username}`,
    method: 'get'
  })
}

export {
  getReportInfo,
  getThinkContent,
  getReportsAccessSort,
  saveClickReportLog,
  hasToken
}