import request from '@/utils/axios'

const url = '/'

function getKnowledgeList(params) {
  return request({
    url: url + 'knowledge/space/nameList',
    method: 'post',
    params
  })
}

function getDbList(params) {
  return request({
    url: url + 'chatdata/dataSource/nameList',
    method: 'get',
    params
  })
}

function getAppList(params) {
  return request({
    url: url + 'scene/publishingNameList',
    method: 'get',
    params
  })
}

function getNewChatId(data) {
  return request({
    url: url + 'api/v1/chat/dialogue/new',
    method: 'post',
    data
  })
}

function getChatData(data) {
  return request({
    url: url + 'api/v1/chat/completionsData',
    method: 'post',
    data
  })
}

function getMessageList(data) {
  return request({
    url: url + 'api/v1/chat/dialogue/messages/history',
    method: 'post',
    data
  })
}

function getFeedbackSelect(params) {
  return request({
    url: url + 'api/v1/feedback/select',
    method: 'get',
    params
  })
}

function getTheFeedback(data) {
  return request({
    url: url + 'api/v1/feedback/find',
    method: 'post',
    data
  })
}

function commitFeedback(data) {
  return request({
    url: url + 'api/v1/feedback/commit',
    method: 'post',
    data
  })
}

function getPromptList(data) {
  return request({
    url: url + 'prompt/list',
    method: 'post',
    data
  })
}

// insight
function getInsight(data) {
  return request({
    url: url + 'scene/api/summary?sessionId=' +data.sessionId,
    method: 'get'
  })
}

// 通过数据源查系统提示词
function getSystemPrompt(data) {
  return request({
    url: url + 'chatdata/dataSource/defaultSystemPrompt',
    method: 'post',
    data
  })
}

// 创建反馈
function createFeedback (data) {
  return request({
    url: url + 'api/v1/evaluate/commiteChatIsLike',
    method: 'post',
    data
  })
}

// 更新反馈信息
function updateFeedbackInfo (data) {
  return request({
    url: url + 'api/v1/evaluate/commiteEvaluateReason',
    method: 'post',
    data
  })
}

// 获取提示语信息
function getWelcomeInfo (data) {
  return request({
    url: url + 'scene/getWelcomeMessage',
    method: 'post',
    data
  })
}

// 模型列表
function getModelList() {
  return request({
    url: url + 'model/list',
    method: 'get'
  })
}

export {
  getKnowledgeList,
  getDbList,
  getAppList,
  getNewChatId,
  getChatData,
  getMessageList,
  getFeedbackSelect,
  getTheFeedback,
  commitFeedback,
  getPromptList,
  getInsight,
  getSystemPrompt,
  createFeedback,
  updateFeedbackInfo,
  getWelcomeInfo,
  getModelList
}
