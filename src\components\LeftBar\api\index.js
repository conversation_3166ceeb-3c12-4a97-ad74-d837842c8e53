import request from '@/utils/axios'

const url = '/'

function getChatList(data) {
  return request({
    url: url + 'api/v1/chat/dialogue/list',
    method: 'post',
    data
  })
}

function toTop(data) {
  return request({
    url: url + 'api/v1/chat/toTop',
    method: 'post',
    data
  })
}

function deleteChat(data) {
  return request({
    url: url + 'api/v1/chat/dialogue/delete',
    method: 'post',
    data
  })
}

export {
  getChatList,
  toTop,
  deleteChat
}
