export default {
  COMMON: {
    THINK_PROCESS: 'Thought Process',
    SEARCH: 'Search',
    RESET: 'Reset',
    CREATE: 'Create',
    CREATOR: 'Creator',
    LAST_UPDATE: 'Last Update Time',
    OPERATIONS: 'Operations',
    EDIT: 'Edit',
    COPY: 'Copy',
    DELETE: 'Delete',
    SETTING: 'Settings',
    RENAME: 'Rename',
    ALL: 'All',
    COLLECT: 'Favorites',
    DETAIL: 'Detail',
    DELETE_CONFIRM: 'Make sure to delete?',
    DELETE_SUCCESS: 'Delete Success',
    CONFIRM: 'Make sure to ',
    SUCCESS: 'Success',
    FAIL: 'Fail',
    STATUS: 'Status',
    RESULT: 'Result',
    PLS_INPUT: 'Please input',
    PLS_SELECT: 'Please select',
    MUST_BE_NUM: 'Must be a number',
    NEXT: 'Next',
    BACK: 'Back',
    CANCEL: 'Cancel',
    SUBMIT: 'Submit',
    CLOSE: 'Close',
    NO: 'No',
    YES: 'Yes',
    AGENT_MARKET: 'Agent Marketplace',
    INTELLIGENT_ANALYSIS: 'Intelligent Analysis',
    KNOWLEDGE_SERVICE: 'Knowledge Service',
    InsightSuccess: 'Data insight completed',
    InsightFailed: 'Data insight failed. Please try refreshing the page or contact the administrator'
  },
  LEFT_BAR: {
    NEW_CHAT: 'New Chat',
    SEARCH_HISTORY: 'Search History',
    START_DATE: 'Start Date',
    TO: 'To',
    END_DATE: 'End Date',
    LAST_1_DAY: 'Last 1 day',
    LAST_7_DAYS: 'Last 7 days',
    LAST_30_DAYS: 'Last 30 days',
    SHARE: 'Share',
    PIN: 'Pin',
    UNPIN: 'Unpin',
    LANGUAGE: 'Language'
  },
  MENUS: {
    PROMPT: 'Prompt',
    DATA_SOURCE: 'Data Source',
    KNOWLEDGE: 'Knowledge',
    PLUGIN: 'Plugin Management',
    APP: 'App Management',
    USER: 'User Management',
    ROLE: 'Role Management',
    MENU: 'Menu Management',

    CREATE: 'Create',
    MENUNAME: 'Menu Name',
    RESET: 'Reset',
    PATH: 'Route',
    SORT: 'Rank',
    PERMISSION: 'Open Access',
    OPERATOR: 'Operator',
    UPDATE: 'Updated Time',
    OPERATION: 'Operation',
    CREATE_MENU: 'Create Menu',
    CREATE_SUCCESS: 'Create Success',
    DELETE_SUCCESS: 'Delete Success',
    EDIT_SUCCESS: 'Edit Success',
    EDIT_MENU: 'Edit Menu',
    COPY_MENU: 'Copy Menu',
    DELETE_MENU: 'Delete Menu',
    KFQX: 'Access',
    ACTIVE: 'On',
    NO_ACTIVE: 'Off',
    CANCEL: 'Cancel',
    SUBMIT: 'Submit',
    DELETE_TIPS: 'This operation will permanently delete the Menu. Do you want to continue?',
    RULE_MENU: 'Please enter a menu name',
    RULE_PATH: 'Please enter path',
    RULE_SORT: 'Please enter sort',
  },
  HOME: {
    QUICK_STRAT: 'Quick Start',
    CHAT_DATA_DESC: 'Chat with your private data through natural language.',
    CHAT_KNOW_DESC: 'Chat with your private knowledge through natural language.',
    CHAT_DATA_APP: 'Chat with your agent through natural language.'
  },
  CHAT: {
    START_CHAT: 'Start a chat',
    DIALOG_WINDOW: 'Dialogue Window',
    INPUT_WARNING: 'The content is generated by the large language model based on the user\'s questions and private knowledge which cannot be guaranteed to 100% accuracy. Please use it as a reference.',
    EMPTY_WARNING: 'Message to be sent cannot be empty',
    CHAT_ERROR: 'Sorry, we met some errors, please try again later.',
    RATING: 'Rating',
    QA_CATEGORY: 'Category',
    QA_RATING: 'Rating',
    CLICK_PROMPT: 'Click to select the prompt',
    ADVICES: 'Advices',
    CHART_TYPE: 'Chart type',
    DIMENSION_FILTER: 'Filtering dimension',
    DIMENSION_GROUP: 'Grouping dimension',
    NO_SUPPORT_PIE: 'The current data is not suitable for pie charts',
    VIEW_SQL: 'view SQL statements',
    DATA_INSIGHT: 'Data Insight ',
    INTELLIGENT_PREDICTION: 'Intelligent prediction',
    RENAME_SUCCESS: 'Rename success',
    DATA_SOURCE: 'Data source',
    SYSTEM_PROMPT: 'System prompt',
    APP_PROMPT: 'Application prompt',
    NEW_NAME: 'New name',
    OTHER_REASON: 'Please enter other reasons',
    LIKE_REASON: 'Please choose the reason that satisfies you',
    DISLIKE_REASON: 'Please choose the reason for your dissatisfaction',
    FEEDBACK_SUCCESS: 'Feedback successful',
    TIPS_FEEDBACK: 'Please select the feedback content',
    WELCOME_HI: 'Hello, welcome to',
    WELCOME_DATASOURCE: 'The data source information for this App is as follows',
    WELCOME_QUESTION: 'You can ask me questions like',
    MORE_QUESTION: 'More question',
    INSIGHT_SUCCESS: 'Data insight completed',
    INSIGHT_FAIL: 'Data insight failure'
  },
  CHART: {
    CARD_CHART: 'Card Chart',
    LINE_CHART: 'Line Chart',
    STEP_LINE_CHART: 'Step Line Chart',
    AREA_CHART: 'Area Chart',
    COLUMN_CHART: 'Column Chart',
    BAR_CHART: 'Bar Chart',
    PIE_CHART: 'Pie Chart',
    DONUT_CHART: 'Donut Chart',
    FUNNEL_CHART: 'Funnel Chart',
    SCATTER_PLOT: 'Scatter Plot',
    BUBBLE_CHART: 'Bubble Chart'
  },
  PROMPT: {
    VIEW: 'VIEW',
    VIEW_PROMPT: 'View Prompt',
    INPUT_NAME: 'Please input prompt name',
    SELECT_TYPE: 'Please select prompt type',
    TYPE_KNOW: 'Knowledge Base',
    TYPE_DATA: 'Data Source',
    TYPE_INSIGHT: 'Insight',
    SELECT_SCENE: 'Please select application scene',
    SELECT_CREATOR: 'Please select prompt creator',
    SYSTEM: 'System',
    APPLICATION: 'Application',
    PROMPT_NAME: 'Prompt Name',
    PROMPT_TYPE: 'Prompt Type',
    APP_SCENE: 'Application Scene',
    ROLE_DESC: 'Role Description',
    CONTENT: 'Content',
    CREATE_PROMPT: 'Create Prompt',
    EDIT_PROMPT: 'Edit Prompt',
    CONTENT_TIP: 'The following prompts will improve the accuracy of SQL generation. 1. Business knowledge related to the scenario. 2. A sample of FAQ pairs related to that data source. The question is queried in natural language, and the answer is a correct SQL statement.',
    DEFAULT_CONTENT: 'Based on the known information below, provide users with professional and concise answers to their questions.\n            If the answer cannot be obtained from the provided content, please say: "The information provided in the knowledge base is not sufficient to answer this question." It is forbidden to make up information randomly. When answering, it is best to summarize according to points 1.2.3.\n            known information:\n            {\'{\'}context{\'}\'}\n            question:\n            {\'{\'}question{\'}\'},when answering, use the same language as the "user".'
  },
  DATA_SOURCE: {
    UpdateSuccess: 'Synchronization succeeded',
    UpdateFaile: 'Synchronization failure',
    UPDATE: 'Update',
    INPUT_NAME: 'Please input data source name',
    INPUT_DESC: 'Please input description',
    SELECT_TYPE: 'Please select data source type',
    SELECT_CREATOR: 'Please select data source creator',
    DATA_SOURCE_TYPE: 'Data Source Type',
    DATA_SOURCE_NAME: 'Data Source Name',
    DATA_SOURCE_DESC: 'Data Source Description',
    METADATA_OWNER: 'Metadata Owner',
    BIND_API: 'Bind API',
    METADATA_MGT: 'Metadata Management',
    CREATE_DATA_SOURCE: 'Create Data Source',
    EDIT_DATA_SOURCE: 'Edit Data Source',
    COPY_DATA_SOURCE: 'Copy Data Source',
    NO_CLUSTERS: 'No clusters were queried',
    NO_TABLES: 'No tables were queried',
    ENVIRONMENT: 'Environment',
    PROD: 'Prod',
    TEST: 'Test',
    HOST: 'Host',
    PORT: 'Port',
    DATABASE: 'Database',
    USERNAME: 'Username',
    PASSWORD: 'Password',
    VNET: 'VNET',
    WORKSPACE: 'Workspace',
    REPORT_NAME: 'Report Name',
    RLS_ROLE_NAME: 'RLS Role Name',
    CLUSTER: 'Cluster',
    TABLE_NAME: 'Table Name',
    TABLE_DESC: 'Table Description',
    TABLE_ENTER: 'Please enter attribute name',
    COL_NAME: 'Column Name',
    BUSI_NAME: 'Business Name',
    MEASURE: 'Metric',
    DIMENSION: 'Dimension',
    TIME: 'Timestamp',
    OTHER: 'Other',
    AI_RECOM: 'AI Intelligent Recommendation',
    DESCRIPTION: 'Description',
    OPERATION: 'Operation',
    TABLE_INFO: 'Table Info',
    TABLE_BUSINESS: 'Table Business Name',
    Attribute_Info: 'Attribute Info',
    NAME: ' attribute name',
    COLUMN_NAME: 'Name',
    COLUMN_TYPE: 'Type',
    COLUMN_VIEIBILITY: 'Visibility',
    TYPE: ' semantic type',
    VIEIBILITY: ' visibility',
    ATTRIBUTE_NAME: 'Attribute Name',
    ADD_INFO: 'Additional Info',
    LANGUAGE_TYPE: 'Semantic Type',
    VISIBLESWITCH: 'visibility all',
    GETBUTTON: 'Generate',
    DEMO_TIPS: 'It is recommended to include to a business description of the table, the fields included, and the purpose of the table. For example, this is a table that records customer order details, including fields such as fiscal year, fiscal month, sales amount, and sales quantity. This table can be used when users ask about order revenue.',
    DEMO_TIPS2: 'Separate with commas if more than one value',
    DEMO_TIPS3: '"Visibility" controls whether the large model can obtain the metadata information of the field. You can set the fields required to answer the question to visible and the remaining fields to invisible. Introducing unnecessary fields may lead to the illusion of large models and reduce query accuracy.',
    ROLE: 'Role'
  },
  PLUGIN: {
    INPUT_NAME: 'Please input plugin name',
    SELECT_TYPE: 'Please select plugin type',
    PLUGIN_NAME: 'Plugin Name',
    PLUGIN_TYPE: 'Plugin Type',
    PLUGIN_DESC: 'Plugin Description',
    VERIFY: 'Verify',
    PUBLISH: 'Publish',
    OFFLINE: 'Offline',
    CREATE_PLUGIN: 'Create Plugin',
    EDIT_PLUGIN: 'Edit Plugin',
    API_TYPE: 'API Type',
    CALL_METHOD: 'Call Method',
    PLUGIN_URL: 'Plugin URL',
    HTTP_TIPS: 'The address must contain an HTTP/HTTPS header',
    HTTP_ERROR: 'The URL must start with http/https',
    REQUEST_INFO: 'Request Information',
    REQUEST_HEAD: 'Request Header',
    PARAM_NAME: 'Parameter Name',
    PARAM_TYPE: 'Parameter Type',
    PARAM_DESC: 'Parameter Desc',
    REQUIRED: 'Required',
    DEFAULT_VALUE: 'Default Value',
    REQUEST_DATA: 'Request Data',
    VALUE_SOURCE: 'Value Source',
    RESPONSE_INFO: 'Response Information',
    RESPONSE_DATA: 'Response Data',
    DATA_DESC: 'A description of the return parameter',
    STATUS_DESC: 'Success or failed status',
    MSG_DESC: 'If failed, return the reason, otherwise it is empty',
    SUBMIT_TIPS: 'Submit the plugin after it is successfully verified',
    VERIFY_PLUGIN: 'Verify Plugin'
  },
  USER: {
    INPUT_ITCODE: 'ITCode, multiple are separated by /',
    ERROR_ITCODE: 'ITCode contains illegal characters, multiple separated by/',
    INPUT_ITCODE_ONCE: 'Please input ITCode',
    INPUT_ROLE: 'Please input role name',
    SELECT_STATUS: 'Please select status',
    NUM: 'No.',
    NAME: 'Name',
    DEPARMENT: 'Department',
    ROLE_NAME: 'Role Name',
    COMMENT: 'Note',
    OPERAOTOR: 'Operator',
    Updated_TIME: 'Updated Time',
    OPERATION: 'Operation',

    DELETE: 'Delete',
    CREATE: 'Create',
    EDIT: 'Edit',
    EDIT_USER: 'Edit User',
    COPY_USER: 'Copy User',
    DELETE_USER: 'Delete User',
    COPY: 'Copy',
    USER: ' User',
    USERNAME: 'User Name',
    TIPS1: 'Please Add Role',
    TIPS2: 'Please add at least one role',
    TIPS3: 'Please select at least one user',
    TIPS4: 'Upload failed, please try uploading again',
    TIPS5: 'Uploading, do not close the page',
    TIPS6: 'Upload files only in XLS/XLSX format!',
    TIPS7: 'Upload successfully',
    TIPS8: 'The name you entered does not match the itcode quantity, please adjust and submit',
    TIPS9: 'Successfully deleted',
    DELETE_ALL: 'Batch deletion',
    EXPORT: 'Export',
    UploadFile: 'Upload file',
    ADD_ALL_ROLE: 'Add roles in batches',
    DELETE_ALL_ROLE: 'Delete roles in batches',
    SelectUser: 'Selected users',
    AllUser: 'All users',
  },
  APP: {
    INPUT_APPNAME: 'App Name',
    APP_TYPE: 'App Type',
    SELECT_SCENE: 'Scene',
    MODEL_NAME: 'Model',
    AGENT_SETTING: 'Agent Setting',
    AGENT_TYPE: 'Agent Type',
    ADD_AGENT: 'Add Agent',
    SELECT_KPITYPE: 'KPI Type',
    DESC: 'App Description',
    STATE: 'Status',
    CREATE_APP: 'Create App',
    EDIT_APP: 'Edit App',
    VIEW_APP: 'View App',
    DATA_SOURCE: 'Data Source',
    PROMPT: 'Prompt',
    DEMO_QUESTION: 'Sample Question',
    OWNER: 'Owner',
    PLUGIN: 'Plugin',
    RESOURCE: 'Resource',
    PUBLISHED: 'Published',
    UNPUBLISHED: 'Unpublished',
    MULTI_AGENT_APP: 'Multi-Agent App',
    NATIVE_APP: 'Native App',
    APPLICATION_TYPE: 'Application Type',
    NATIVE_APPLICATION: 'Native Application',
    MULTI_AGENT_APPLICATION: 'Multi Agent Application',
    NATIVR_DESC: 'Create application using the standard data chat template',
    MULTI_DESC: 'Create application with multiple agents configured',
    SELECT_APP_TYPE: 'Please select the application type',
    AGENT_CONFIG: 'Please configure at least one agent',
    INSIGHT_PROMPT: 'Insight Prompt',
    INSIGHT_KNOWLEDGE: 'Insight Knowledge',
    INSIGHT_MODEL: 'Insight Model'
  },
  ROLE: {
    ADD_ROLE: 'Create',
    INPUT_ROLENAME: 'role name, multiple are separated by /',
    SEARCH: 'Search',
    HANDLE: 'Batch Operation',
    ROLE_NUM: 'No.',
    ROLE_NAME: 'Role Name',
    ROLE_DESC: 'Role Description',
    ROLE_USER: 'Granted Users',
    ROLE_OPERATOR: 'Operator',
    Updated_TIME: 'Updated Time',
    OPERATION: 'Operation',
    EDIT_ROLE: 'Edit Role',
    COPY_ROLE: 'Copy',
    DELETE_ROLE: 'Delete',
    DELETE_TIPS: 'Be sure to delete?',
    EDIT_PERMISION: 'Edit Permission',
    PAGE_PERMISION: 'Page Permision',
    DATA_PERMISION: 'Data Permision',
    SELECT_ROLE: 'Select role',
    ALL_ROLE: 'All role',
    SELECT_ONE: 'Select at least one role'
  },
  AGENT: {
    INPUT_NAME: 'Please input App name',
    SELECT_SCENE: 'Please select scene',
    WELCOME: 'You can choose one of the following applications to ask questions about the data.',
    COPY_SUCCESS: 'copy success',
    COPY_FAIL: 'copy fail',
    ADD_FAVORITE: 'Added to favorites',
    CANCEL_FAVORITE: 'Remove from favorites'
  },
  KNOWLEDGE: {
    CREATE: 'Create',
    CREATOR: 'Creator',
    DESCRIPTION: 'Description',
    LAST_TIEM: 'Last Editing Time',
    CONVERSATION: 'Conversation',
    DELETE_KNOW: 'Delete Knowledge Base',
    SURE_DELETE_KNOW: 'Are you sure to delete this knowledge base?',
    DATA_SAFE: 'Data Compliance Notice',
    DATA_SAFE_CONTENT: '1. Limited to internal Lenovo data. Based on data security and privacy protection requirements of PRC, to avoid non-compliance risks related to data privacy, do not upload any sensitive data (including PII/CI/LR). If you need to upload such data, contact the security and legal teams for evaluation. <br> <br> 2. Do not use content that involves third-party copyrights as input. <br> <br> 3. Do not use content that violates national policies (such as pornography, gambling, drugs, or reactionary speech) as input.',
    CREATE_KNOW: 'Create Knowledge Base',
    KNOW_BASE_INFO: 'Basic Information of Knowledge Base',
    KNOW_TYPE: 'Knowledge Base Type',
    KNOW_IMPORT: 'Import into Knowledge Base',
    // KNOW_NAME: 'Knowledge Base Name',
    KNOW_NAME: 'Name',
    STEP_NEXT: 'Next',
    TEXT: 'Text',
    TEXT_DESC: 'Enter your original text',
    URL: 'URL',
    URL_DESC: 'Get the content from URL',
    DOCUMENT: 'Document',
    DOCUMENT_DESC: 'Upload Document (supported formats: PDF, CSV, Text, PowerPoint, Word, Markdown)',
    NAME: 'Name',
    TEXT_SOURCE: 'Text Source',
    TEXT_CONTENT: 'Content',
    SYNC: 'Sync',
    BACK: 'Back',
    COMPLETE: 'Complete',
    WEB_URL: 'Web URL',
    UPLOAD_FILE: 'Select or drag and drop file here',
    PARAMS: 'Parameters',
    EMBED: 'Embedding',
    PROMPT: 'Prompt',
    SUMMARY: 'Summary',
    ADD_KNOW: 'Add Knowledge',
    CHUNKING: 'Chunking',
    LAST_SYNC_TIME: 'Last Sync Time',
    DETAIL: 'Details',
    DELETE: 'Delete',
    TOPK: 'The top K vectors based on the similarity score',
    RECALL_TYPE: 'Recall type',
    CHUNK_SIZE: 'Size of data chunks used in processing',
    RECALL_SCORE: 'The threshold score for similar vector retrieval',
    MODEL: 'The model used to embedding',
    CHUNK_OVERLAP: 'Overlap between adjacent data chunks',
    SCENE: 'Context parameters for defining the settings or environment for using prompts',
    TEMPLATE: 'Predefined prompt structure or format to ensure consistent style or tone in AI responses',
    MAX_TOKEN: 'Maximum number of tokens allowed in the prompt',
    CONTENT: 'Content',
    METADATA: 'Metadata',
    DELETE_DATA_SOURCE: 'Delete data source',
    SURE_DELETE_DATA_SOURCE: 'Are you sure to delete this data source?',
    ONE_FILE: 'Only one file can be uploaded at a time'
  }
}
