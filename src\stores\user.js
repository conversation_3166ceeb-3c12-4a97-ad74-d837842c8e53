import { defineStore } from 'pinia'
import { getMenus, getUserRights } from '@/views/Login/api'
import { getChatList } from '@/components/LeftBar/api'
import { getFeedbackSelect } from '@/views/Chat/api'
import { menuRoutes } from '@/router/menuRoutes'
import router from '@/router'

export const useUserStore = defineStore('user', {
  state: () => ({
    routes: null, // 菜单路由
    scenes: [], // 聊天场景
    model: [], // 模型列表
    promptSystem: false, // 系统提示语权限
    promptTab: false, // chat data聊天中的系统提示语Tab权限
    chatListParams: { // 聊天列表查询条件
      userInput: '',
      startDate: '',
      endDate: '',
    },
    chatPage: { // 聊天列表分页相关
      pageNum: 1,
      pageSize: 20,
      pageTotal: 0
    },
    chatLoading: false, // 聊天列表Loading
    chatList: [], // 聊天列表数据
    feedbackSelect: [], // 评分问答类别
    thumbnailPhoto: '' // 个人头像base64
  }),
  getters: {
    getRoutes: (state) => state.routes,
    getScenes: (state) => state.scenes,
    getModel: (state) => state.model,
    getPromptSystem: (state) => state.promptSystem,
    getPromptTab: (state) => state.promptTab,
    getChatList: (state) => state.chatList,
    getFeedbackSelect: (state) => state.feedbackSelect
  },
  actions: {
    async setRoutes() {
      const res = await getMenus()
      this.routes = res || []
      menuRoutes.forEach(menu => {
        if (res.find(item => menu.path.indexOf(item.menuPath) > -1)) {
          router.addRoute(menu)
        }
      })
    },
    async setDataPermission() {
      const res = await getUserRights()
      this.thumbnailPhoto = res.thumbnailPhoto
      const data = res?.dataPermission || {}
      this.scenes = data.scenes || []
      this.model = data.model || []
      this.promptTab = data.prompt_function ? true : false
      this.promptSystem = !!data.prompt_page_system
    },
    async setChatList(pageNum, params) {
      this.chatLoading = true
      try {
        this.chatPage.pageNum = pageNum || this.chatPage.pageNum
        this.chatListParams = params || this.chatListParams
        const res = await getChatList({
          ...this.chatListParams,
          pageNum: this.chatPage.pageNum,
          pageSize: this.chatPage.pageSize
        })
        this.chatPage.pageTotal = res?.total || 0
        this.chatList = res?.list || []
        this.chatLoading = false
      } catch(e) {
        this.chatLoading = false
      }
    },
    setChatParam(id, select) {
      const item = this.chatList.find(x => x.convUid === id)
      item.selectParam = select
    },
    async setFeedbackSelect() {
      const res = await getFeedbackSelect()
      this.feedbackSelect = res || []
    }
  }
})
