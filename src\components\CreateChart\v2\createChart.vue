<template>
  <!-- 图表 -->
  <template v-if="renderChartDom">
    <!-- 图表类型选择 -->
    <!-- <span class="type-label">{{ $t('CHAT.CHART_TYPE') }}:</span>
    <el-select v-model="chartType" placeholder="Please Select...">
      <el-option v-for="item in chartTypeList" :key="item.value" :label="$t(`CHART.${item.value}`)" :value="item.value" />
    </el-select> -->
    
    <template v-if="legendFilterList.data.length > 0">
      <span class="type-label">{{ $t('CHAT.DIMENSION_GROUP') }}</span>
      <el-select v-model="legendType" @change="legendChange" clearable size="small" style="width: 130px;" placeholder="Please select">
        <el-option v-for="item in legendFilterList.data" :key="item.name" :label="item.name" :value="item.name" />
      </el-select>
    </template>
    <!-- 维度筛选器 -->
    <template v-if="chartFilterList.data.length > 0">
      <span class="type-label">{{ $t('CHAT.DIMENSION_FILTER') }}:</span>
      <template v-for="item in chartFilterList.data" :key="item.name">
        <span class="dimension-label">{{ item.name }}</span>
        <el-select v-model="filterValue[item.name]" @change="filterChange(item.name)" multiple collapse-tags clearable size="small" placeholder="All">
          <!-- <template #header>
            <el-checkbox
              v-model="filterValue[item.name].check"
              :indeterminate="filterValue[item.name].indeterminate"
              @change="handleCheckAll(item.name)"
            >
              All
            </el-checkbox>
          </template> -->
          <el-option v-for="option in item.data" :key="option.value" :label="option.label" :value="option.value" />
        </el-select>
      </template>
    </template>
    <v-chart ref="vChartsDom" :option="chartOption" autoresize style="height: 400px;" />
  </template>
  <!-- 卡片 -->
  <template v-if="cardDataList.length > 0">
    <div class="card-wrap">
      <el-card v-for="item in cardDataList" :key="item._name">
        <el-statistic v-if="item[item._name] === null" :title="item._name" :value="item[item._name]" :formatter="val => String(val)" />
        <el-statistic v-else :title="item._name" :value="item[item._name]" :formatter="formatFloatNum"/>
      </el-card>
    </div>
  </template>
</template>

<script setup>
import VChart from 'vue-echarts';
import { ref, watch, nextTick, reactive } from 'vue';
import * as echarts from 'echarts';
import createChartOption from './createChartOption.js';
/**
 * renderData:图表渲染所需数据
 * chartType:图表默认类型
 */
const props = defineProps({
  renderData: Object,
  chartType: String
})
let origin = props.renderData
// 格式化图表渲染数据格式
const endData = {
  tableData: origin.data,
  // 筛选后的图表数据
  source: {},
  // 筛选器列表数据
  filterDimensionList: [],
  // ...origin.semanticMap,
  measure: origin.semanticMap.measure,
  dimension: origin.semanticMap.dimension,
  time: origin.semanticMap.time,
  other: origin.semanticMap.other
}
// 各时间/维度下枚举值数量做降序排列 枚举值最多的做为x轴
// 参数 dimension/time 列表数据
const dimensionListSortByDesc = (list) => {
  const dimensionList = [...list]
  try {
    // 对各维度枚举值数量做降序排列
    dimensionList.sort((a,b) => {
      return b.data.length - a.data.length
    })
    return dimensionList
  } catch (error) {
    console.log('维度枚举值排序出错')
  }
}

// 获取图表筛选器下拉列表数据
// tableData: 图表数据 dimension: 规则里多余的dimension&time
const getDimensionListData = (tableData, dimension) => {
  let dimensionListData = []
  dimension.forEach(x => {
    const currentDimensionInfo = { name: x }
    let currentDimension = new Set()
    tableData.forEach(y => {
      if (y[x]) currentDimension.add(y[x])
    })
    currentDimensionInfo.data = Array.from(currentDimension).map(item => ({
      value: item,
      label: item
    }))
    // 该筛选器有枚举值 才渲染到界面
    if(currentDimensionInfo.data.length > 0) {
      dimensionListData.push(currentDimensionInfo)
    }
  })
  return dimensionListSortByDesc(dimensionListData)
}
// 各筛选器的默认值(第一个下拉值)
const getDefaultDimensionValue = (dimensionList) => {
  const defaulDimensionListValue = []
  dimensionList.forEach(item => {
    defaulDimensionListValue.push({
      name: item.name,
      value: item.data[0].value
    })
  })
  return defaulDimensionListValue
}
// 根据筛选器的组合值 获取当前筛选条件下的图表数据
const getChartDataFromFilterData = (tableData, selectedDimensionValue) => {
  let copyData = Array.from(tableData)
  return copyData.filter(row => {
    return Object.keys(selectedDimensionValue).every(key => {
      // 筛选条件为空 直接返回
      if(selectedDimensionValue[key].length === 0) {
        return true
      }
      return selectedDimensionValue[key].includes(row[key])
    })
  })
}

const vChartsDom = ref(null)
// 卡片图数据
const cardDataList = ref([])
// 是否开始渲染图表
const renderChartDom = ref(false)
// 筛选器数据列表
const chartFilterList = reactive({
  data: []
})
// 分组维度数据
const legendFilterList = reactive({
  data: []
})
// 当前分组维度值
const legendType = ref('')
// 图表类型列表
// const chartTypeList = [{
//   label: '柱状图',
//   value: 'COLUMN_CHART'
// }, {
//   label: '折线图',
//   value: 'LINE_CHART'
// }, {
//   label: '条形图',
//   value: 'BAR_CHART'
// }, {
//   label: '面积图',
//   value: 'AREA_CHART'
// }, {
//   label: '饼图',
//   value: 'PIE_CHART'
// }]
// 各筛选器当前选择值
let filterValue = reactive({})
// 图表类型
// const chartType = ref('COLUMN_CHART')
// 创建echarts配置所需参数
const chartInputData = reactive({
  charType: 'COLUMN_CHART',
  dimension: [],
  source: [],
  timeAxis: false,
  legendType: '',
  legendFilterList: []
})

const updateChartData = () => {
  const newChartOption = createChartOption(chartInputData)
  nextTick(() => {
    vChartsDom.value.setOption(newChartOption, {
      notMerge: true
    })
  })
}

const getCurrentFilterValue = (list) => {
  let formaterData = {}
  for (let key in list) {
    formaterData[key] = [...list[key]]
  }
  return formaterData
}
// 处理筛选器全选按钮操作
// const handleCheckAll = (val) => {}
// 处理筛选器全选按钮的显示状态
// const dealFilterCheckStatus = (val) => {
//   const tarFilter = chartFilterList.data.filter(item => item.name === val)
// }
// 筛选器变化事件
const filterChange = () => {
  const filters = getCurrentFilterValue(filterValue)
  // 变化后获取最新图表数据
  const filterChartData = getChartDataFromFilterData(endData.tableData, filters)
  // 数据处理
  // 结构类似{[group]:{[x]: {y}}}
  const groupData = new Map()
  const xname = chartInputData.dimension[0]
  const yname = chartInputData.dimension.slice(1)
  // 分组图例
  const group = legendType.value || xname
  filterChartData.forEach(row => {
    const g = row[group]
    const x = row[xname]
    if(!groupData.has(g)) {
      groupData.set(g, {})
    }
    const obj = groupData.get(g)
    if(!obj[x]) {
      obj[x] = {}
      yname.forEach(y => {
        obj[x][y] = 0
      })
    }
    // 度量值求和
    yname.forEach(y => {
      obj[x][y] += row[y]
    })
  })

  const formaterMutiSelect = (data) => {
    const res = []
    for (let [key, value] of data) {
      const list = Object.entries(value)
      list.forEach(item => {
        let dataRow = {
          [group]: key,
          [xname]: item[0]
        }
        for(let key in item[1]) {
          dataRow[key] = item[1][key]
        }
        res.push(dataRow)
      })
    }
    return res
  }
  const res = formaterMutiSelect(groupData)
  chartInputData.source = res

  if(vChartsDom.value) {
    updateChartData()
  }
}

const legendChange = (val) => {
  chartInputData.legendType = val

  // if(legendFilterList.data.length) {
    // chartFilterList.data = [...legendFilterList.data]
    // const newFilterData = legendFilterList.data.filter(item => item.name !== val)
    // chartFilterList.data = [...newFilterData]
  // }
  
  // 重置筛选条件
  // filterValue = reactive({})
  // chartFilterList.data.forEach(item => {
  //   // filterValue[item.name] = [item.data[0].value]
  //   filterValue[item.name] = []
  // })

  filterChange()
}

// 监听切换图表类型
watch(() => props.chartType, (val) => {
  chartInputData.charType = val
  updateChartData()
})


// 根据图表规则初始化 生成echarts配置的所需参数
const createCharDataFromRules = () => {
  // 卡片图
  // 规则：other-空 dimension-空 time-空 度量-非空
  if (endData.dimension.length === 0 && endData.time.length === 0 && endData.measure.length > 0) {
    let headers = []
    if (endData.tableData.length) {
      headers = Object.keys(endData.tableData[endData.tableData.length === 1 ? 0 : 1])
    }
    // header.length > 1
    if (endData.tableData.length) {
      let temp = []
      // 格式化卡片图展示数据
      headers.forEach(key => {
        const cardIndex = endData.tableData.length === 1 ? 0 : 1
        let val = endData.tableData[cardIndex][key]
        temp.push({
          _name: key,
          [key]: val
        })
      })
      cardDataList.value = temp
    }
    return
  }
  // 时间+度量
  // 规则：other-空 dimension-空 time-非空 度量-非空
  // x轴：时间，存在多个时间时 选枚举值最多的一个  y轴：度量
  // 筛选器：除x轴使用的时间外 其他时间值
  if (endData.dimension.length == 0 && endData.time.length > 0 && endData.measure.length > 0) {
    const dimensionListData = getDimensionListData(endData.tableData, endData.time)
    // 选择各时间筛选器枚举值最多的做为x轴
    let maxName = dimensionListData[0].name
    // 对x轴排序
    endData.tableData.sort((a, b) => {
      return a[maxName] - b[maxName]
    })
    // 过滤掉作为x轴的筛选器
    endData.filterDimensionList = dimensionListData.filter(item => item.name != maxName)

    // chartInputData.axisData = dimensionListData
    chartInputData.dimension = [maxName, ...endData.measure]
    chartInputData.timeAxis = true

    if(endData.measure.length === 1) {
      legendFilterList.data = [...endData.filterDimensionList]
    }

    chartFilterList.data = [...endData.filterDimensionList]

    legendType.value = legendFilterList.data.length ? legendFilterList.data[0].name : ''

    legendChange(legendType.value)

    chartInputData.legendFilterList = legendFilterList.data

    renderChartDom.value = true
  }
  // 规则：维度+度量
  // other-空 time-空 dimension-非空 度量-非空
  // x轴：维度，存在多个维度时 选择枚举值最多的一个  y轴：度量
  // 筛选器：除x轴使用维度外的其他维度值
  if (endData.time.length === 0 && endData.dimension.length > 0 && endData.measure.length > 0) {
    const dimensionListData = getDimensionListData(endData.tableData, endData.dimension)
    // 选择各维度筛选器枚举值最多的做为x轴
    let maxName = dimensionListData[0].name
    endData.tableData.sort((a, b) => {
      return a[maxName] - b[maxName]
    })
    // 过滤掉作为x轴的筛选器
    endData.filterDimensionList = dimensionListData.filter(item => item.name != maxName)

    // chartInputData.axisData = dimensionListData
    chartInputData.dimension = [maxName, ...endData.measure]

    if(endData.measure.length === 1) {
      legendFilterList.data = [...endData.filterDimensionList]
    }

    chartFilterList.data = [...endData.filterDimensionList]

    legendType.value = legendFilterList.data.length ? legendFilterList.data[0].name : ''

    legendChange(legendType.value)

    chartInputData.legendFilterList = legendFilterList.data

    renderChartDom.value = true
  }
  // 时间+维度+度量
  // 规则：other-空 time-非空 dimension-非空 度量-非空 
  // x轴：时间，多时间情况选择枚举值最多的一个  y轴：度量
  // 筛选器：除x轴使用时间外的其他时间和所有维度值
  if (endData.time.length > 0 && endData.dimension.length > 0 && endData.measure.length > 0) {
    // 时间筛选器
    const timeListData = getDimensionListData(endData.tableData, endData.time)
    // 选择各时间筛选器枚举值最多的做为x轴
    let maxName = timeListData[0].name
    // 对x轴排序
    endData.tableData.sort((a, b) => {
      return a[maxName] - b[maxName]
    })
    // 过滤掉作为x轴的筛选器
    const otherTimeList = timeListData.filter(item => item.name != maxName)
    endData.filterDimensionList = [...otherTimeList, ...getDimensionListData(endData.tableData, endData.dimension)]

    chartInputData.dimension = [maxName, ...endData.measure]
    chartInputData.timeAxis = true

    // 单度量
    if(endData.measure.length === 1) {
      legendFilterList.data = [...endData.filterDimensionList]
    }

    chartFilterList.data = [...endData.filterDimensionList]
    // 默认第一个 支持不选
    legendType.value = legendFilterList.data.length ? legendFilterList.data[0].name : ''

    legendChange(legendType.value)

    chartInputData.legendFilterList = legendFilterList.data
    
    renderChartDom.value = true
  }
  return chartInputData
}
const charData = createCharDataFromRules()
const chartOption = (charData && createChartOption(charData)) || {}

// 格式化卡片图浮点数字
const formatFloatNum = (num) => {
  let decimalPart = ''
  let integerPart = Math.floor(num).toString()
  if (num.toString().includes('.')) {
    const parts = num.toString().split('.')
    integerPart = parts[0]
    decimalPart = parts[1]
    decimalPart = decimalPart.slice(0, 6)
  }
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  return decimalPart? `${formattedInteger}.${decimalPart}` : formattedInteger
}
defineExpose({
  updateChartData
})

</script>

<style scoped>
.type-label {
  margin: 0 10px;
  font-size: 14px;
}

.dimension-label {
  margin: 0 5px;
  font-size: 12px;
}

.card-wrap {
  display: flex;

  .el-card {
    width: 200px;
    margin: 10px;
    text-align: center;
  }

  .el-statistic {
    text-align: center;
  }
}

.el-select {
  width: 200px;
}
</style>
