<script setup>
import { useI18n } from 'vue-i18n'
import { Plus } from '@element-plus/icons-vue'
import { ref, computed, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getToken } from '@/utils/auth'
import * as userApi from './api'
import * as roleApi from '../Role/api'

const { t } = useI18n()

// 状态选项
const stateOptions = [
  { label: t('MENUS.ACTIVE'), value: 'y', name: 'ACTIVE' },
  { label: t('MENUS.NO_ACTIVE'), value: 'n', name: 'NO_ACTIVE' }
]
const codeInputValue = ref('')
const nameInputValue = ref('')
const stateValue = ref('')
const pageNum = ref(1)
const pageSize = ref(10)
let userOrder = 'update_time desc'
const total = ref(0)
const userData = ref([])
// 批量操作用传参
const searchInfo = {
  itcodes: [],
  roleName: '',
  state: '',
  pageNum: 1,
  pageSize: 9999,
  orderBy: 'update_time desc'
}
// 支持数字和字母和/
const checkCode = (str) => {
  return /^[\da-z/]+$/i.test(str)
}
const getTableList = async () => {
  if (codeInputValue.value !== '' && !checkCode(codeInputValue.value)) {
    ElMessage.warning(t('USER.ERROR_ITCODE'))
    return
  }
  const param = {
    itcodes: [...new Set(codeInputValue.value ? codeInputValue.value.split('/') : [])],
    roleName: nameInputValue.value,
    state: stateValue.value,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    orderBy: userOrder
  }
  const users = await userApi.getUserList(param)
  total.value = users.total
  userData.value = users.list
  // 搜索用户列表后，将搜索条件保存，用户批量操作
  searchInfo.itcodes = [...new Set(codeInputValue.value ? codeInputValue.value.split('/') : [])]
  searchInfo.roleName = nameInputValue.value
  searchInfo.state = stateValue.value
}
let timer = null
const inputItcode = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    getItcodeInfo()
  }, 500)
}
// 输入itcode带出匹配的姓名和部门
const getItcodeInfo = async () => {
  itcodeInfoLoading.value = true
  let res = await userApi.getItcodeInfo(userForm.value.itcodes)
  if (!res) {
    // res为Null
    userForm.value.name = ''
    userForm.value.departmentName = ''
  } else {
    userForm.value.name = res.name || ''
    userForm.value.departmentName = res.departmentName
  }
  itcodeInfoLoading.value = false
}
const itcodeInfoLoading = ref(false)
const searchUser = () => {
  getTableList()
}
const resetSearch = () => {
  codeInputValue.value = ''
  nameInputValue.value = ''
  stateValue.value = ''
  pageNum.value = 1
  getTableList()
}
const indexMethod = (index) => {
  return (pageNum.value - 1) * 10 + index + 1
}
const handleSort = (val) => {
  let order = 'desc'
  let col = 'update_time'
  // null 代表复原
  if (val.order !== null) {
    order = val.order === 'descending' ? 'desc' : 'asc'
    switch (val.prop) {
    case 'itcode':
      col = 'itcode'
      break
    case 'groupNames':
      col = 'group_names'
      break
    case 'buName':
      col = 'bu_name'
      break
    case 'name':
      col = 'name'
      break
    case 'departmentName':
      col = 'department_name'
      break
    case 'roleNames':
      col = 'role_names'
      break
    case 'defaultUrlName':
      col = 'default_url_name'
      break
    case 'operator':
      col = 'operator'
      break
    default:
      col = 'update_time'
      break
    }
  }
  userOrder = col + ' ' + order
  getTableList()
}
const handleCurrentChange = (val) => {
  pageNum.value = val
  getTableList()
}

const dialogUser = ref(false)
// 当前点击的操作: 新增, 编辑, 复制
const currentHandle = ref(t('USER.CREATE'))
// 角色选项
const roleList = ref([])
// 用户分组选项
const groupList = ref([])
// 用户表单数据
const userForm = ref({
  itcodes: '',
  name: '',
  departmentName: '',
  state: 'y',
  roleId: '',
  groupIdList: [],
  desc: ''
})
// 校验itcodes
const validateItcodes = (rule, value, callback) => {
  if (!value) {
    callback(new Error(t('COMMON.PLS_INPUT')))
  } else if (!checkCode(value)) {
    callback(new Error('存在非法字符'))
  } else {
    callback()
  }
}
// 支持数字和字母中英文，新增和复制时额外支持/
const checkName = (str) => {
  const regex = currentHandle.value === t('USER.EDIT') ? /^[A-Za-z0-9\u4e00-\u9fa5]+$/gi : /^[A-Za-z0-9\u4e00-\u9fa5/]+$/gi
  return regex.test(str)
}
// 校验姓名
const validateName = (rule, value, callback) => {
  if (!value) {
    callback(new Error(t('COMMON.PLS_INPUT')))
  } else if (!checkName(value)) {
    callback(new Error(currentHandle.value === t('USER.EDIT') ? '存在非法字符，仅支持中英文数字' : '存在非法字符，多个以 / 分隔'))
  } else {
    callback()
  }
}
const userRules = computed(() => {
  return {
    itcodes: [{ required: true, validator: validateItcodes, trigger: 'blur' }],
    roleId: [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }],
    state: [{ required: true, message: t('COMMON.PLS_SELECT'), trigger: 'change' }],
  }
})
const getRoleList = async () => {
  const params = {
    roleName: '',
    pageNum: 1,
    pageSize: 9999,
    orderBy: 'roleName'
  }
  const role = await roleApi.getTableList(params)
  roleList.value = role.list
}
const getGroupList = async () => {
  groupList.value = await userApi.getGroupList()
}
const resetForm = () => {
  userForm.value = {
    itcodes: '',
    name: '',
    departmentName: '',
    state: 'y',
    roleIdList: [],
    groupIdList: [],
    desc: ''
  }
}
// 新增用户
const handleClickCreate = () => {
  currentHandle.value = t('USER.CREATE')
  resetForm()
  dialogUser.value = true
}
// 编辑用户
const handleClickEdit = (index, row) => {
  currentHandle.value = t('USER.EDIT')
  userForm.value = {
    itcodes: row.itcode,
    name: row.name,
    departmentName: row.departmentName,
    roleId:row.roleId,
    state: row.state,
    roleIdList: row.roleIds ? row.roleIds.split(',').map(item => parseInt(item)) : [],
    groupIdList: row.groupIds ? row.groupIds.split(',').map(item => parseInt(item)) : [],
    desc: row.description,
    id: row.id
  }
  dialogUser.value = true
}
// 复制用户
const handleClickCopy = (index, row) => {
  currentHandle.value = t('USER.COPY')
  userForm.value = {
    itcodes: row.itcode,
    name: row.name,
    departmentName: row.departmentName,
    roleId:row.roleId,
    state: row.state,
    roleIdList: row.roleIds ? row.roleIds.split(',').map(item => parseInt(item)) : [],
    groupIdList: row.groupIds ? row.groupIds.split(',').map(item => parseInt(item)) : [],
    desc: row.description
  }
  dialogUser.value = true
}
const deleteUser = async (id) => {
  await userApi.deleteUser(id)
  ElMessage.success(t('USER.TIPS9'))
  getTableList()
}
// 删除用户
const handleClickDelete = (index, row) => {
  ElMessageBox.confirm(
    t('ROLE.DELETE_TIPS'),
    '',
    { type: 'warning', closeOnClickModal: false }
  ).then(() => {
    deleteUser(row.id)
  }).catch(() => {
    console.log('取消删除')
  })
}

const dialogClose = () => {
  dialogUser.value = false
}
const submitLoading = ref(false)
const arrLowercase = (arr) => {
  for (let i = 0; i < arr.length; i++) {
    for (let j = i + 1; j < arr.length; j++) {
      if (arr[i].toLowerCase() === arr[j].toLowerCase()) {
        arr.splice(j, 1)
        j--
      }
    }
  }
  return arr
}
const addOrCopyUser = async () => {
  const addParams = Object.assign({}, userForm.value)
  addParams.itcodes = arrLowercase([...new Set(addParams.itcodes.split('/').filter(item => item))])
  addParams.names = arrLowercase([...new Set(addParams.name.split('/').filter(item => item))])
  if (addParams.itcodes.length !== addParams.names.length) {
    ElMessage.warning(t('USER.TIPS8'))
    submitLoading.value = false
    return
  }
  addParams.description = addParams.desc
  try {
    await userApi.batchAddUser(addParams)
    submitLoading.value = false
    dialogClose()
    getTableList()
  } catch (e) {
    submitLoading.value = false
  }
}
const editUser = async () => {
  const editParams = Object.assign({}, userForm.value)
  delete editParams.itcodes
  editParams.description = editParams.desc
  try {
    await userApi.editUser(editParams)
    submitLoading.value = false
    dialogClose()
    getTableList()
  } catch (e) {
    submitLoading.value = false
  }
}
const submitForm = () => {
  // if (userForm.value.roleIdList.length === 0) {
  //   ElMessage.warning('请添加角色')
  //   return
  // }
  if (!userForm.value.roleId) {
    ElMessage.warning(t('USER.TIPS1'))
    return
  }
  submitLoading.value = true
  if (currentHandle.value === t('USER.CREATE')) {
    addOrCopyUser()
  } else if (currentHandle.value === t('USER.EDIT')) {
    editUser()
  } else if (currentHandle.value === t('USER.COPY')) {
    addOrCopyUser()
  }
}

const uploadDisabled = ref(false)
const uploadApi = `${import.meta.env.VITE_APP_BASE_API_URL}/user/uploadOperateExcel`
const uploadApiHeaders = { Authorization: getToken() }
// 上传之前钩子
const handleBeforeUpload = (file) => {
  const isXLSX = file.name.includes('.xls') // 支持xls和xlsx格式
  if (!isXLSX) {
    ElMessage({
      showClose: true,
      duration: 10000,
      message: t('USER.TIPS6'),
      type: 'error'
    })
  }
  return isXLSX
}
// 文件上传时
const handleProgress = () => {
  uploadDisabled.value = true
  ElMessage({
    showClose: true,
    duration: 10000,
    message: t('USER.TIPS5'),
    type: 'warning'
  })
}
const handleSuccess = (res) => {
  uploadDisabled.value = false
  ElMessage.closeAll() // 上传成功后要把之前的message弹框都去掉
  if (res.status === 200) {
    ElMessage.success(t('USER.TIPS7'))
    getRoleList()
    getGroupList()
    searchUser()
  } else {
    const resMessage = res.msg
    ElMessage({
      showClose: true,
      duration: 10000,
      message: resMessage,
      type: 'error'
    })
  }
}
const handleError = (err, file) => {
  uploadDisabled.value = false
  ElMessage({
    showClose: true,
    duration: 10000,
    message: t('USER.TIPS4'),
    type: 'error'
  })
}

// 选中行
let selectedRow = []
// 'all'-指操作全部用户， 'select'-指操作表格选中用户
let handleUser = ''
const dialogRole = ref(false)
// 角色处理：添加，删除
const roleHandle = ref(t('USER.ADD_ALL_ROLE'))
const roleSelected = ref([])
const submitRoleLoading = ref(false)
const handleSelectionChange = (val) => {
  selectedRow = val
}
const addRoleSelectOrAll = (val) => {
  if (val === 'select' && selectedRow.length === 0) {
    ElMessage.warning(t('USER.TIPS3'))
    return
  }
  handleUser = val
  roleHandle.value = t('USER.ADD_ALL_ROLE')
  roleSelected.value = []
  dialogRole.value = true
}
const deleteRoleSelectOrAll = (val) => {
  if (val === 'select' && selectedRow.length === 0) {
    ElMessage.warning(t('USER.TIPS3'))
    return
  }
  handleUser = val
  roleHandle.value = t('USER.DELETE_ALL_ROLE')
  roleSelected.value = []
  dialogRole.value = true
}
const dialogRoleClose = () => {
  dialogRole.value = false
}
const submitRoleForm = async () => {
  if (roleSelected.value.length === 0) {
    ElMessage.warning(t('USER.TIPS2'))
    return
  }
  submitRoleLoading.value = true
  if (roleHandle.value === t('USER.ADD_ALL_ROLE')) {
    const params = {
      flag: '',
      userIds: [],
      roleIds: roleSelected.value
    }
    if (handleUser === 'all') {
      if (searchInfo.itcodes.length === 0 && searchInfo.roleName === '' && searchInfo.state === '') {
        params.flag = 'all'
      } else {
        params.flag = 'select'
        const users = await userApi.getUserList(searchInfo)
        params.userIds = users.list.map(item => item.id)
      }
    } else {
      params.flag = 'select'
      params.userIds = selectedRow.map(item => item.id)
    }
    await userApi.batchAddRole(params)
  } else {
    const params = {
      userIds: [],
      roleIds: roleSelected.value
    }
    if (handleUser === 'all') {
      if (searchInfo.itcodes.length === 0 && searchInfo.roleName === '' && searchInfo.state === '') {
        params.userIds = []
      } else {
        const users = await userApi.getUserList(searchInfo)
        params.userIds = users.list.map(item => item.id)
      }
    } else {
      params.userIds = selectedRow.map(item => item.id)
    }
    await userApi.batchDelRole(params)
  }
  getTableList()
  submitRoleLoading.value = false
  dialogRole.value = false
}
const deleteSelect = () => {
  if (selectedRow.length === 0) {
    ElMessage.warning(t('USER.TIPS3'))
    return
  }
  ElMessageBox.confirm(
    t('ROLE.DELETE_TIPS'),
    '',
    { type: 'warning', closeOnClickModal: false }
  ).then(async () => {
    const params = {userIds: selectedRow.map(item => item.id)}
    await userApi.batchDeleteUser(params)
    getTableList()
  }).catch(() => {
    console.log('取消删除')
  })
}
const exportSelectOrAll = async (val) => {
  handleUser = val
  let params = null
  if (val === 'all') {
    params = Object.assign({ flag: 'all' }, searchInfo)
    params.orderBy = userOrder
  } else {
    if (selectedRow.length === 0) {
      ElMessage.warning(t('USER.TIPS3'))
      return
    }
    params = {
      itcodes: selectedRow.map(item => item.itcode),
      roleName: '',
      state: '',
      pageNum: 1,
      pageSize: 9999,
      orderBy: userOrder,
      flag: 'select'
    }
  }
  const res = await userApi.exportUser(params)
  if (res.data) {
    const url = window.URL.createObjectURL(new Blob([res.data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'}))
    const link = document.createElement('a')
    let fileName = ''
    let contentDisposition = res.headers['content-disposition']
    link.style.display = 'none'
    link.href = url
    if (contentDisposition) {
      fileName = window.decodeURI(res.headers['content-disposition'].split('=')[1], 'UTF-8')
    }
    link.setAttribute('download', fileName || 'export.xlsx')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } else {
    ElMessage({
      message: '导出失败，请重试',
      type: 'error'
    })
  }
}

getGroupList()
getRoleList()
getTableList()

onBeforeUnmount(() => {
  ElMessage.closeAll()
})
</script>

<template>
  <div class="user-view">
    <div class="search-box">
      <el-input v-model="codeInputValue" :placeholder="$t('USER.INPUT_ITCODE')" clearable />
      <el-input v-model="nameInputValue" :placeholder="$t('USER.INPUT_ROLE')" clearable />
      <el-select v-model="stateValue" :placeholder="$t('USER.SELECT_STATUS')">
        <el-option
          v-for="item in stateOptions"
          :key="item.value"
          :label="$t(`MENUS.${item.name}`)"
          :value="item.value"
        />
      </el-select>
      <div class="search-btn-left">
        <el-button type="primary" @click="searchUser">{{ $t('COMMON.SEARCH') }}</el-button>
        <el-button @click="resetSearch()">{{ $t('COMMON.RESET') }}</el-button>
        <el-upload
          class="upload-user"
          :disabled="uploadDisabled"
          :action="uploadApi"
          :headers="uploadApiHeaders"
          :show-file-list="false"
          :before-upload="handleBeforeUpload"
          :on-progress="handleProgress"
          :on-success="handleSuccess"
          :on-error="handleError"
        >
          <el-tooltip class="item" effect="dark" :content="$t('USER.UploadFile')" placement="top">
            <i
              class="iconfont icon-shangchuanexcel"
              :class="{'icon-disabled': uploadDisabled}"
            />
          </el-tooltip>
        </el-upload>
        <el-tooltip class="item" effect="dark" :content="$t('USER.ADD_ALL_ROLE')" placement="top">
          <el-dropdown trigger="click" @command="addRoleSelectOrAll" placement="bottom">
            <span>
              <i class="iconfont icon-xinzengyonghujiaose" />
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="select">{{$t('USER.SelectUser')}}</el-dropdown-item>
                <el-dropdown-item command="all">{{$t('USER.AllUser')}}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" :content="$t('USER.DELETE_ALL_ROLE')" placement="top">
          <el-dropdown trigger="click" @command="deleteRoleSelectOrAll" placement="bottom">
            <span>
              <i class="iconfont icon-shanchuyonghujiaose" />
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="select">{{$t('USER.SelectUser')}}</el-dropdown-item>
                <el-dropdown-item command="all">{{$t('USER.AllUser')}}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" :content="$t('USER.DELETE_ALL')" placement="top">
          <span @click="deleteSelect">
            <i class="iconfont icon-shanchuyonghu" />
          </span>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" :content="$t('USER.EXPORT')" placement="top">
          <el-dropdown trigger="click" @command="exportSelectOrAll" placement="bottom">
            <span>
              <i class="iconfont icon-xiazaiexcel" />
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="select">{{$t('USER.SelectUser')}}</el-dropdown-item>
                <el-dropdown-item command="all">{{$t('USER.AllUser')}}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-tooltip>
      </div>
      <div class="search-btn-right">
        <el-button type="primary" :icon="Plus" @click="handleClickCreate">{{ $t('COMMON.CREATE') }}</el-button>
      </div>
    </div>
    <el-table
      :data="userData"
      class="table-box"
      header-cell-class-name="table-box-header"
      @selection-change="handleSelectionChange"
      @sort-change="handleSort"
    >
      <el-table-column type="selection" width="40" />
      <el-table-column type="index" :index="indexMethod" :label="$t('USER.NUM')" width="55" />
      <el-table-column prop="itcode" label="ITCode" sortable="custom" width="110" />
      <el-table-column prop="name" :label="$t('USER.NAME')" sortable="custom" width="100" />
<!--      <el-table-column prop="groupNames" label="用户分组" sortable="custom" min-width="135" />-->
<!--      <el-table-column prop="buName" label="业务单元" sortable="custom" min-width="135" />-->
      <el-table-column prop="departmentName" :label="$t('USER.DEPARMENT')" sortable="custom" min-width="100" />
      <el-table-column prop="roleNames" :label="$t('USER.ROLE_NAME')" sortable="custom" min-width="100" />
<!--      <el-table-column prop="defaultUrlName" label="默认首页" sortable="custom" min-width="110" />-->
      <el-table-column prop="description" :label="$t('USER.COMMENT')" min-width="100" />
      <el-table-column prop="operator" :label="$t('USER.OPERAOTOR')" sortable="custom" width="120" />
      <el-table-column prop="updateTime" :label="$t('USER.Updated_TIME')" sortable="custom" width="185">
        <template #default="scope">{{ scope.row.updateTime.split('.')[0] }}</template>
      </el-table-column>
      <el-table-column prop="state" :label="$t('COMMON.STATUS')" width="80">
        <template #default="scope">{{ scope.row.state === 'y' ? $t('MENUS.ACTIVE') : $t('MENUS.NO_ACTIVE') }}</template>
      </el-table-column>
      <el-table-column :label="$t('USER.OPERATION')" width="140">
        <template #default="scope">
          <div class="table-box-btn">
            <div class="table-button" @click="handleClickEdit(scope.$index, scope.row)">
              <el-tooltip class="item" effect="dark" :content="$t('USER.EDIT_USER')" placement="top">
                <i class="iconfont icon-edit" />
              </el-tooltip>
            </div>
            <div class="table-button" @click="handleClickCopy(scope.$index, scope.row)">
              <el-tooltip class="item" effect="dark" :content="$t('USER.COPY_USER')" placement="top">
                <i class="iconfont icon-copy" />
              </el-tooltip>
            </div>
            <div class="table-button" @click="handleClickDelete(scope.$index, scope.row)">
              <el-tooltip class="item" effect="dark" :content="$t('USER.DELETE_USER')" placement="top">
                <i class="iconfont icon-delete" />
              </el-tooltip>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="pagination-box"
      v-model:current-page="pageNum"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleCurrentChange(1)"
      @current-change="handleCurrentChange"
    />
    <el-dialog
      class="setting-page-dialog-wrap"
      v-model="dialogUser"
      :title="currentHandle + $t('USER.USER')"
      width="500px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userRules"
        label-width="100px"
      >
        <el-form-item prop="itcodes" :label="$t('USER.USERNAME')">
          <el-input
            @input="inputItcode"
            v-model="userForm.itcodes"
            :placeholder="$t('USER.INPUT_ITCODE_ONCE')"
            :disabled="currentHandle === t('USER.EDIT')"
            clearable
          />
        </el-form-item>
<!--        <el-form-item prop="name" label="姓名">-->
        <el-form-item :label="$t('USER.NAME')">
          <el-input
            v-loading="itcodeInfoLoading"
            v-model="userForm.name"
            :placeholder="$t('COMMON.PLS_INPUT')"
            disabled
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('USER.DEPARMENT')">
          <el-input
            v-loading="itcodeInfoLoading"
            v-model="userForm.departmentName"
            :placeholder="$t('COMMON.PLS_INPUT')"
            disabled
            clearable
          />
        </el-form-item>
        <el-form-item prop="state" :label="$t('COMMON.STATUS')">
          <el-select v-model="userForm.state">
            <el-option
              v-for="item in stateOptions"
              :key="item.value"
              :label="$t(`MENUS.${item.name}`)"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item prop="roleId" :label="$t('USER.ROLE_NAME')">
          <el-select v-model="userForm.roleId">
            <el-option
              v-for="(item, index) in roleList"
              :key="'role' + index"
              :label="item.roleName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('USER.COMMENT')">
          <el-input
            v-model="userForm.desc"
            clearable
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogClose">{{$t('MENUS.CANCEL')}}</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">{{$t('MENUS.SUBMIT')}}</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      :title="roleHandle"
      v-model="dialogRole"
      width="480px"
      :before-close="dialogRoleClose"
      :close-on-click-modal="false"
    >
      <div class="form-item">
        <span class="asterisk">*</span>
        <span class="form-label">{{$t('USER.ROLE_NAME')}}</span>
        <span class="form-input">
          <el-select
            v-model="roleSelected"
            multiple
            :multiple-limit="1"
            collapse-tags
            filterable
            :reserve-keyword="true"
          >
            <el-option
              v-for="(item, index) in roleList"
              :key="index"
              :label="item.roleName"
              :value="item.id"
            ></el-option>
          </el-select>
        </span>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogRoleClose">{{t('MENUS.CANCEL')}}</el-button>
          <el-button type="primary" @click="submitRoleForm" :loading="submitRoleLoading">{{$t('MENUS.SUBMIT')}}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.user-view {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  padding: 0 24px;
  background: #fff;
  .search-btn-left {
    display: flex;
    align-items: center;
    &>div, &>span {
      width: 32px;
      height: 32px;
      display: flex;
      border: 1px solid #C6CCD6;
      border-radius: 4px;
      align-items: center;
      justify-content: center;
      margin-right: 10px;
      i {
        margin: 0 5px;
        color: #767F8C;
        font-size: 20px;

      }
      &:hover {
        border-color: #B5CCF8;
        i {
          color: #568CF4;
        }
      }
    }
    
    .upload-user {
      margin-left: 12px;
    }
    .el-dropdown {
      line-height: inherit;
    }
    .user-icon-drop {
      display: block;
      font-size: 20px;
      cursor: pointer;
      padding: 4px 10px 5px;
      color: #000000;
      &:hover {
        color: #409EFF;
      }
    }
    .icon-disabled {
      color: #c0c4cc;
      cursor: not-allowed;
    }
  }
  .table-box-btn {
    .table-button {
      cursor: pointer;
      margin-right: 20px;
    }
  }
  .form-item {
    height: 32px;
    line-height: 32px;
    width: 320px;
    margin: 10px auto;
    .asterisk {
      display: inline-block;
      vertical-align: middle;
      width: 12px;
      color: red;
    }
    .form-label {
      display: inline-block;
      vertical-align: middle;
      width: 98px;
      font-size: 12px;
      color: #666766;
      letter-spacing: 0;
      font-weight: 500;
      text-align: justify;
      text-align-last: justify;
      padding-right: 14px;
    }
    .form-input {
      display: inline-block;
      vertical-align: middle;
      width: 210px;
      height: auto;
      .el-input, .el-select {
        width: 210px;
      }
      :deep(.el-tag) {
        max-width: 80px !important;
      }
    }
    &:last-child .form-input {
      height: 32px;
      line-height: 32px;
    }
    .name-verify {
      font-size: 8px;
      color: #FF0000;
      letter-spacing: 0;
      font-weight: 400;
    }
  }
  :deep(.el-dialog__body) {
    padding: 10px;
  }
  .dialog-footer {
    text-align: center;
    .el-button {
      width: 96px;
      margin: 0 35px;
    }
  }
  .el-overlay {
    .el-dialog {
      .el-form-item {
        .el-select {
          width: 100%;
        }
      }
    }
  }
}
.table-box-btn i {
  color: #568CF4;
}
</style>
